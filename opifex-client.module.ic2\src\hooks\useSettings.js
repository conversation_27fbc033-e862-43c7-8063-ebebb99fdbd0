// import { useCallback } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import {
//   deleteFontByFontFamilyAction,
//   deleteFontByIdAction,
//   getFontAction,
//   uploadFontAction,
// } from "../store/actions";
// import { fontsSelector } from "../store/selector/settingSelector";
// import { setSelectedFontFamily as setSelectedFontFamilyAction } from "../store/slice/settings";
// export const useFonts = () => {
//   const dispatch = useDispatch();
//   const {
//     data: fonts,
//     uniqueFonts,
//     focusFonts,
//     selectedFontFamily,
//     deletingId,
//     deletingFontFamily,
//     loading,
//     error,
//   } = useSelector(fontsSelector);

//   const getFontByCompanyCode = useCallback(
//     (companyCode) => dispatch(getFontAction({ companyCode })),
//     [dispatch]
//   );
//   const uploadFont = useCallback(
//     (params) => dispatch(uploadFontAction(params)),
//     [dispatch]
//   );
//   const deleteFontById = useCallback(
//     (params) => dispatch(deleteFontByIdAction(params)),
//     [dispatch]
//   );
//   const deleteFontByFontFamily = useCallback(
//     (params) => dispatch(deleteFontByFontFamilyAction(params)),
//     [dispatch]
//   );

//   const setSelectedFontFamily = useCallback(
//     (params) => dispatch(setSelectedFontFamilyAction(params)),
//     [dispatch]
//   );

//   return [
//     {
//       fonts,
//       uniqueFonts,
//       focusFonts,
//       selectedFontFamily,
//       deletingId,
//       deletingFontFamily,
//       loading,
//       error,
//     },
//     {
//       getFontByCompanyCode,
//       uploadFont,
//       deleteFontById,
//       deleteFontByFontFamily,
//       setSelectedFontFamily,
//     },
//   ];
// };
