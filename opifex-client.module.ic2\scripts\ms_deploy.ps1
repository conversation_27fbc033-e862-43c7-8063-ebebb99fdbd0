<#
.SYNOPSIS
    Deploys a web application using MS Deploy.
.DESCRIPTION
    PowerShell script to deploy web applications using Microsoft Web Deploy.
.PARAMETER source
    Relative path (project directory as a root) to the publish content directory.
    The path should not start/end with back-slash "\" or slash "/" characters.
    Regularly path will be bin/Release/publish
.PARAMETER siteName
    IIS site name
.PARAMETER appPath
    IIS application path
.PARAMETER user
    The user-name who has privilege to deploy app to remote server.
.PARAMETER passwd
    The password of user who has privilege to deploy app to remote server.
.PARAMETER server
    The web server name or IP address.
.NOTES
    List of web deploy providers:
    https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-r2-and-2008/dd569040(v=ws.10)
#>

param(
    [string]$source,
    [string]$siteName,
    [string]$appPath,
    [string]$environment,
    [string]$user,
    [string]$passwd,
    [string]$server,
    [int]$port
)

# Default values
$MSDeploy = "$env:ProgramFiles\IIS\Microsoft Web Deploy V3\msdeploy.exe"
$PORT = 8172
$IS_APP = $true

if($port) { $PORT = $port }

$DEST_SERVER = "https://$($server):$($PORT)/msdeploy.axd"
$source = Join-Path $PWD $source

Write-Host "========================================================================================"
Write-Host "Server: $DEST_SERVER"
Write-Host "Site Name: $siteName"
Write-Host "App Path: $appPath"
Write-Host "========================================================================================"

# Build arguments as an array
$arguments = @(
    "-verb:sync",
    "-enableRule:DoNotDelete",
    "-enableRule:AppOffline",
    "-disableLink:AppPoolExtension",
    "-disableLink:ContentExtension",
    "-disableLink:CertificateExtension",
    "-skip:objectname='dirPath',absolutepath='Config\\Company'",
    "-allowUntrusted",
    "-retryAttempts=10",
    "-retryInterval=2000",
    "-userAgent=`"VSCmdLine`"",
    #"-source:IisApp=`"$source`"",
    #"-dest:IisApp=`"$siteName$appPath`",UserName=`"$user`",Password=`"$passwd`",AuthType='Basic',IncludeAcls='False',ComputerName=`"$DEST_SERVER`""
    "-source:contentPath=`"$source`"",
    "-dest:contentPath=`"$siteName$appPath`",UserName=`"$user`",Password=`"$passwd`",AuthType='Basic',IncludeAcls='False',ComputerName=`"$DEST_SERVER`""
)

# Execute msdeploy with the arguments
& $MSDeploy $arguments

# Check the execution status
if ($LASTEXITCODE -ne 0) {
    exit $LASTEXITCODE
}
else {
    exit 0
}
