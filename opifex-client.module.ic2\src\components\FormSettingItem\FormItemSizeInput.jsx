import { useState } from "react";
import { Input, Select, Space, Form } from "antd";

import { parseSizeUnit } from "../../utils/common";
import useDebounce from "../../hooks/useDebounce";

export const SIZE_UNITS = [
  { label: "px", value: "px" },
  { label: "%", value: "%" },
  { label: "rem", value: "rem" },
  { label: "em", value: "em" },
];

const MeasurementInput = ({
  onChange,
  size,
  unit,
  placeholder,
  unitOptions = SIZE_UNITS,
}) => {
  const [sizeValue, setSizeValue] = useState(size || "");
  const [unitValue, setUnitValue] = useState(unit || SIZE_UNITS[0].value);

  const handleSelectChange = (selectedUnit) => {
    setUnitValue(selectedUnit);
    debouncedOnSizeChange();
  };

  const debouncedOnSizeChange = useDebounce(() => {
    if (!sizeValue) return onChange(null);
    onChange(sizeValue + unitValue);
  });

  const handleInputChange = (event) => {
    setSizeValue(event.target.value);
    debouncedOnSizeChange();
  };

  return (
    <Space.Compact>
      <Input
        type="number"
        onChange={handleInputChange}
        placeholder={placeholder || "size"}
        value={sizeValue}
        style={{ width: "calc(100% - 70px)" }}
      />
      <Select
        value={unitValue}
        onChange={handleSelectChange}
        style={{ width: 70 }}
        options={unitOptions}
      />
    </Space.Compact>
  );
};

const FormItemSizeInput = ({
  name,
  label = "",
  className,
  placeholder,
  unitOptions,
}) => {
  const form = Form.useFormInstance();
  const formValue = form.getFieldValue(name);
  const { number, unit } = parseSizeUnit(formValue) || {};

  return (
    <Form.Item name={name} label={label} className={className}>
      <MeasurementInput
        size={number}
        placeholder={placeholder}
        unit={unit}
        unitOptions={unitOptions}
      />
    </Form.Item>
  );
};

export { FormItemSizeInput };
