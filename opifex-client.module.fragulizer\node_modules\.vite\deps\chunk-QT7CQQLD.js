import {
  require_fragulizer_mf_v_runtimeInit_mf_v
} from "./chunk-FM5QRY6S.js";
import {
  require_index_cjs
} from "./chunk-Q6BHGTTH.js";
import {
  __commonJS
} from "./chunk-ZKAWKZG5.js";

// node_modules/__mf__virtual/fragulizer__loadShare__react__loadShare__.js
var require_fragulizer_loadShare_react_loadShare = __commonJS({
  "node_modules/__mf__virtual/fragulizer__loadShare__react__loadShare__.js"(exports, module) {
    var { loadShare } = require_index_cjs();
    var { initPromise } = require_fragulizer_mf_v_runtimeInit_mf_v();
    var res = initPromise.then((_) => loadShare("react", {
      customShareInfo: { shareConfig: {
        singleton: true,
        strictVersion: false,
        requiredVersion: "^19.1.0"
      } }
    }));
    var exportModule = (
      /*mf top-level-await placeholder replacement mf*/
      res.then((factory) => factory())
    );
    module.exports = exportModule;
  }
});

export {
  require_fragulizer_loadShare_react_loadShare
};
//# sourceMappingURL=chunk-QT7CQQLD.js.map
