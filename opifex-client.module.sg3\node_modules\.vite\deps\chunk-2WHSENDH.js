import {
  __commonJS
} from "./chunk-ZKAWKZG5.js";

// node_modules/@module-federation/runtime-core/dist/polyfills.cjs.cjs
var require_polyfills_cjs = __commonJS({
  "node_modules/@module-federation/runtime-core/dist/polyfills.cjs.cjs"(exports2) {
    "use strict";
    function _extends() {
      _extends = Object.assign || function assign(target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i];
          for (var key in source) if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];
        }
        return target;
      };
      return _extends.apply(this, arguments);
    }
    function _object_without_properties_loose(source, excluded) {
      if (source == null) return {};
      var target = {};
      var sourceKeys = Object.keys(source);
      var key, i;
      for (i = 0; i < sourceKeys.length; i++) {
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
      }
      return target;
    }
    exports2._extends = _extends;
    exports2._object_without_properties_loose = _object_without_properties_loose;
  }
});

// node_modules/@module-federation/sdk/dist/polyfills.cjs.cjs
var require_polyfills_cjs2 = __commonJS({
  "node_modules/@module-federation/sdk/dist/polyfills.cjs.cjs"(exports2) {
    "use strict";
    function _extends() {
      _extends = Object.assign || function assign(target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i];
          for (var key in source) if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];
        }
        return target;
      };
      return _extends.apply(this, arguments);
    }
    exports2._ = _extends;
  }
});

// node_modules/@module-federation/sdk/dist/index.cjs.cjs
var require_index_cjs = __commonJS({
  "node_modules/@module-federation/sdk/dist/index.cjs.cjs"(exports, module) {
    "use strict";
    var polyfills = require_polyfills_cjs2();
    var FederationModuleManifest = "federation-manifest.json";
    var MANIFEST_EXT = ".json";
    var BROWSER_LOG_KEY = "FEDERATION_DEBUG";
    var NameTransformSymbol = {
      AT: "@",
      HYPHEN: "-",
      SLASH: "/"
    };
    var NameTransformMap = {
      [NameTransformSymbol.AT]: "scope_",
      [NameTransformSymbol.HYPHEN]: "_",
      [NameTransformSymbol.SLASH]: "__"
    };
    var EncodedNameTransformMap = {
      [NameTransformMap[NameTransformSymbol.AT]]: NameTransformSymbol.AT,
      [NameTransformMap[NameTransformSymbol.HYPHEN]]: NameTransformSymbol.HYPHEN,
      [NameTransformMap[NameTransformSymbol.SLASH]]: NameTransformSymbol.SLASH
    };
    var SEPARATOR = ":";
    var ManifestFileName = "mf-manifest.json";
    var StatsFileName = "mf-stats.json";
    var MFModuleType = {
      NPM: "npm",
      APP: "app"
    };
    var MODULE_DEVTOOL_IDENTIFIER = "__MF_DEVTOOLS_MODULE_INFO__";
    var ENCODE_NAME_PREFIX = "ENCODE_NAME_PREFIX";
    var TEMP_DIR = ".federation";
    var MFPrefetchCommon = {
      identifier: "MFDataPrefetch",
      globalKey: "__PREFETCH__",
      library: "mf-data-prefetch",
      exportsKey: "__PREFETCH_EXPORTS__",
      fileName: "bootstrap.js"
    };
    var ContainerPlugin = Object.freeze({
      __proto__: null
    });
    var ContainerReferencePlugin = Object.freeze({
      __proto__: null
    });
    var ModuleFederationPlugin = Object.freeze({
      __proto__: null
    });
    var SharePlugin = Object.freeze({
      __proto__: null
    });
    function isBrowserEnv() {
      return typeof window !== "undefined" && typeof window.document !== "undefined";
    }
    function isReactNativeEnv() {
      var _navigator;
      return typeof navigator !== "undefined" && ((_navigator = navigator) == null ? void 0 : _navigator.product) === "ReactNative";
    }
    function isBrowserDebug() {
      try {
        if (isBrowserEnv() && window.localStorage) {
          return Boolean(localStorage.getItem(BROWSER_LOG_KEY));
        }
      } catch (error2) {
        return false;
      }
      return false;
    }
    function isDebugMode() {
      if (typeof process !== "undefined" && process.env && process.env["FEDERATION_DEBUG"]) {
        return Boolean(process.env["FEDERATION_DEBUG"]);
      }
      if (typeof FEDERATION_DEBUG !== "undefined" && Boolean(FEDERATION_DEBUG)) {
        return true;
      }
      return isBrowserDebug();
    }
    var getProcessEnv = function() {
      return typeof process !== "undefined" && process.env ? process.env : {};
    };
    var LOG_CATEGORY = "[ Federation Runtime ]";
    var parseEntry = (str, devVerOrUrl, separator = SEPARATOR) => {
      const strSplit = str.split(separator);
      const devVersionOrUrl = getProcessEnv()["NODE_ENV"] === "development" && devVerOrUrl;
      const defaultVersion = "*";
      const isEntry = (s) => s.startsWith("http") || s.includes(MANIFEST_EXT);
      if (strSplit.length >= 2) {
        let [name, ...versionOrEntryArr] = strSplit;
        if (str.startsWith(separator)) {
          name = strSplit.slice(0, 2).join(separator);
          versionOrEntryArr = [
            devVersionOrUrl || strSplit.slice(2).join(separator)
          ];
        }
        let versionOrEntry = devVersionOrUrl || versionOrEntryArr.join(separator);
        if (isEntry(versionOrEntry)) {
          return {
            name,
            entry: versionOrEntry
          };
        } else {
          return {
            name,
            version: versionOrEntry || defaultVersion
          };
        }
      } else if (strSplit.length === 1) {
        const [name] = strSplit;
        if (devVersionOrUrl && isEntry(devVersionOrUrl)) {
          return {
            name,
            entry: devVersionOrUrl
          };
        }
        return {
          name,
          version: devVersionOrUrl || defaultVersion
        };
      } else {
        throw `Invalid entry value: ${str}`;
      }
    };
    var composeKeyWithSeparator = function(...args) {
      if (!args.length) {
        return "";
      }
      return args.reduce((sum, cur) => {
        if (!cur) {
          return sum;
        }
        if (!sum) {
          return cur;
        }
        return `${sum}${SEPARATOR}${cur}`;
      }, "");
    };
    var encodeName = function(name, prefix = "", withExt = false) {
      try {
        const ext = withExt ? ".js" : "";
        return `${prefix}${name.replace(new RegExp(`${NameTransformSymbol.AT}`, "g"), NameTransformMap[NameTransformSymbol.AT]).replace(new RegExp(`${NameTransformSymbol.HYPHEN}`, "g"), NameTransformMap[NameTransformSymbol.HYPHEN]).replace(new RegExp(`${NameTransformSymbol.SLASH}`, "g"), NameTransformMap[NameTransformSymbol.SLASH])}${ext}`;
      } catch (err) {
        throw err;
      }
    };
    var decodeName = function(name, prefix, withExt) {
      try {
        let decodedName = name;
        if (prefix) {
          if (!decodedName.startsWith(prefix)) {
            return decodedName;
          }
          decodedName = decodedName.replace(new RegExp(prefix, "g"), "");
        }
        decodedName = decodedName.replace(new RegExp(`${NameTransformMap[NameTransformSymbol.AT]}`, "g"), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.AT]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.SLASH]}`, "g"), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.SLASH]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.HYPHEN]}`, "g"), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.HYPHEN]]);
        if (withExt) {
          decodedName = decodedName.replace(".js", "");
        }
        return decodedName;
      } catch (err) {
        throw err;
      }
    };
    var generateExposeFilename = (exposeName, withExt) => {
      if (!exposeName) {
        return "";
      }
      let expose = exposeName;
      if (expose === ".") {
        expose = "default_export";
      }
      if (expose.startsWith("./")) {
        expose = expose.replace("./", "");
      }
      return encodeName(expose, "__federation_expose_", withExt);
    };
    var generateShareFilename = (pkgName, withExt) => {
      if (!pkgName) {
        return "";
      }
      return encodeName(pkgName, "__federation_shared_", withExt);
    };
    var getResourceUrl = (module2, sourceUrl) => {
      if ("getPublicPath" in module2) {
        let publicPath;
        if (!module2.getPublicPath.startsWith("function")) {
          publicPath = new Function(module2.getPublicPath)();
        } else {
          publicPath = new Function("return " + module2.getPublicPath)()();
        }
        return `${publicPath}${sourceUrl}`;
      } else if ("publicPath" in module2) {
        if (!isBrowserEnv() && !isReactNativeEnv() && "ssrPublicPath" in module2) {
          return `${module2.ssrPublicPath}${sourceUrl}`;
        }
        return `${module2.publicPath}${sourceUrl}`;
      } else {
        console.warn("Cannot get resource URL. If in debug mode, please ignore.", module2, sourceUrl);
        return "";
      }
    };
    var assert = (condition, msg) => {
      if (!condition) {
        error(msg);
      }
    };
    var error = (msg) => {
      throw new Error(`${LOG_CATEGORY}: ${msg}`);
    };
    var warn = (msg) => {
      console.warn(`${LOG_CATEGORY}: ${msg}`);
    };
    function safeToString(info) {
      try {
        return JSON.stringify(info, null, 2);
      } catch (e) {
        return "";
      }
    }
    var VERSION_PATTERN_REGEXP = /^([\d^=v<>~]|[*xX]$)/;
    function isRequiredVersion(str) {
      return VERSION_PATTERN_REGEXP.test(str);
    }
    var simpleJoinRemoteEntry = (rPath, rName) => {
      if (!rPath) {
        return rName;
      }
      const transformPath = (str) => {
        if (str === ".") {
          return "";
        }
        if (str.startsWith("./")) {
          return str.replace("./", "");
        }
        if (str.startsWith("/")) {
          const strWithoutSlash = str.slice(1);
          if (strWithoutSlash.endsWith("/")) {
            return strWithoutSlash.slice(0, -1);
          }
          return strWithoutSlash;
        }
        return str;
      };
      const transformedPath = transformPath(rPath);
      if (!transformedPath) {
        return rName;
      }
      if (transformedPath.endsWith("/")) {
        return `${transformedPath}${rName}`;
      }
      return `${transformedPath}/${rName}`;
    };
    function inferAutoPublicPath(url2) {
      return url2.replace(/#.*$/, "").replace(/\?.*$/, "").replace(/\/[^\/]+$/, "/");
    }
    function generateSnapshotFromManifest(manifest, options = {}) {
      var _manifest_metaData, _manifest_metaData1;
      const { remotes = {}, overrides = {}, version } = options;
      let remoteSnapshot;
      const getPublicPath = () => {
        if ("publicPath" in manifest.metaData) {
          if (manifest.metaData.publicPath === "auto" && version) {
            return inferAutoPublicPath(version);
          }
          return manifest.metaData.publicPath;
        } else {
          return manifest.metaData.getPublicPath;
        }
      };
      const overridesKeys = Object.keys(overrides);
      let remotesInfo = {};
      if (!Object.keys(remotes).length) {
        var _manifest_remotes;
        remotesInfo = ((_manifest_remotes = manifest.remotes) == null ? void 0 : _manifest_remotes.reduce((res2, next) => {
          let matchedVersion;
          const name = next.federationContainerName;
          if (overridesKeys.includes(name)) {
            matchedVersion = overrides[name];
          } else {
            if ("version" in next) {
              matchedVersion = next.version;
            } else {
              matchedVersion = next.entry;
            }
          }
          res2[name] = {
            matchedVersion
          };
          return res2;
        }, {})) || {};
      }
      Object.keys(remotes).forEach((key) => remotesInfo[key] = {
        // overrides will override dependencies
        matchedVersion: overridesKeys.includes(key) ? overrides[key] : remotes[key]
      });
      const { remoteEntry: { path: remoteEntryPath, name: remoteEntryName, type: remoteEntryType }, types: remoteTypes, buildInfo: { buildVersion }, globalName, ssrRemoteEntry } = manifest.metaData;
      const { exposes } = manifest;
      let basicRemoteSnapshot = {
        version: version ? version : "",
        buildVersion,
        globalName,
        remoteEntry: simpleJoinRemoteEntry(remoteEntryPath, remoteEntryName),
        remoteEntryType,
        remoteTypes: simpleJoinRemoteEntry(remoteTypes.path, remoteTypes.name),
        remoteTypesZip: remoteTypes.zip || "",
        remoteTypesAPI: remoteTypes.api || "",
        remotesInfo,
        shared: manifest == null ? void 0 : manifest.shared.map((item) => ({
          assets: item.assets,
          sharedName: item.name,
          version: item.version
        })),
        modules: exposes == null ? void 0 : exposes.map((expose) => ({
          moduleName: expose.name,
          modulePath: expose.path,
          assets: expose.assets
        }))
      };
      if ((_manifest_metaData = manifest.metaData) == null ? void 0 : _manifest_metaData.prefetchInterface) {
        const prefetchInterface = manifest.metaData.prefetchInterface;
        basicRemoteSnapshot = polyfills._({}, basicRemoteSnapshot, {
          prefetchInterface
        });
      }
      if ((_manifest_metaData1 = manifest.metaData) == null ? void 0 : _manifest_metaData1.prefetchEntry) {
        const { path: path2, name, type } = manifest.metaData.prefetchEntry;
        basicRemoteSnapshot = polyfills._({}, basicRemoteSnapshot, {
          prefetchEntry: simpleJoinRemoteEntry(path2, name),
          prefetchEntryType: type
        });
      }
      if ("publicPath" in manifest.metaData) {
        remoteSnapshot = polyfills._({}, basicRemoteSnapshot, {
          publicPath: getPublicPath(),
          ssrPublicPath: manifest.metaData.ssrPublicPath
        });
      } else {
        remoteSnapshot = polyfills._({}, basicRemoteSnapshot, {
          getPublicPath: getPublicPath()
        });
      }
      if (ssrRemoteEntry) {
        const fullSSRRemoteEntry = simpleJoinRemoteEntry(ssrRemoteEntry.path, ssrRemoteEntry.name);
        remoteSnapshot.ssrRemoteEntry = fullSSRRemoteEntry;
        remoteSnapshot.ssrRemoteEntryType = ssrRemoteEntry.type || "commonjs-module";
      }
      return remoteSnapshot;
    }
    function isManifestProvider(moduleInfo) {
      if ("remoteEntry" in moduleInfo && moduleInfo.remoteEntry.includes(MANIFEST_EXT)) {
        return true;
      } else {
        return false;
      }
    }
    var PREFIX = "[ Module Federation ]";
    var Logger = class Logger {
      setPrefix(prefix) {
        this.prefix = prefix;
      }
      log(...args) {
        console.log(this.prefix, ...args);
      }
      warn(...args) {
        console.log(this.prefix, ...args);
      }
      error(...args) {
        console.log(this.prefix, ...args);
      }
      success(...args) {
        console.log(this.prefix, ...args);
      }
      info(...args) {
        console.log(this.prefix, ...args);
      }
      ready(...args) {
        console.log(this.prefix, ...args);
      }
      debug(...args) {
        if (isDebugMode()) {
          console.log(this.prefix, ...args);
        }
      }
      constructor(prefix) {
        this.prefix = prefix;
      }
    };
    function createLogger(prefix) {
      return new Logger(prefix);
    }
    var logger = createLogger(PREFIX);
    async function safeWrapper(callback, disableWarn) {
      try {
        const res2 = await callback();
        return res2;
      } catch (e) {
        !disableWarn && warn(e);
        return;
      }
    }
    function isStaticResourcesEqual(url1, url2) {
      const REG_EXP = /^(https?:)?\/\//i;
      const relativeUrl1 = url1.replace(REG_EXP, "").replace(/\/$/, "");
      const relativeUrl2 = url2.replace(REG_EXP, "").replace(/\/$/, "");
      return relativeUrl1 === relativeUrl2;
    }
    function createScript(info) {
      let script2 = null;
      let needAttach = true;
      let timeout = 2e4;
      let timeoutId;
      const scripts = document.getElementsByTagName("script");
      for (let i = 0; i < scripts.length; i++) {
        const s = scripts[i];
        const scriptSrc = s.getAttribute("src");
        if (scriptSrc && isStaticResourcesEqual(scriptSrc, info.url)) {
          script2 = s;
          needAttach = false;
          break;
        }
      }
      if (!script2) {
        const attrs2 = info.attrs;
        script2 = document.createElement("script");
        script2.type = (attrs2 == null ? void 0 : attrs2["type"]) === "module" ? "module" : "text/javascript";
        let createScriptRes = void 0;
        if (info.createScriptHook) {
          createScriptRes = info.createScriptHook(info.url, info.attrs);
          if (createScriptRes instanceof HTMLScriptElement) {
            script2 = createScriptRes;
          } else if (typeof createScriptRes === "object") {
            if ("script" in createScriptRes && createScriptRes.script) {
              script2 = createScriptRes.script;
            }
            if ("timeout" in createScriptRes && createScriptRes.timeout) {
              timeout = createScriptRes.timeout;
            }
          }
        }
        if (!script2.src) {
          script2.src = info.url;
        }
        if (attrs2 && !createScriptRes) {
          Object.keys(attrs2).forEach((name) => {
            if (script2) {
              if (name === "async" || name === "defer") {
                script2[name] = attrs2[name];
              } else if (!script2.getAttribute(name)) {
                script2.setAttribute(name, attrs2[name]);
              }
            }
          });
        }
      }
      const onScriptComplete = async (prev, event) => {
        clearTimeout(timeoutId);
        const onScriptCompleteCallback = () => {
          if ((event == null ? void 0 : event.type) === "error") {
            (info == null ? void 0 : info.onErrorCallback) && (info == null ? void 0 : info.onErrorCallback(event));
          } else {
            (info == null ? void 0 : info.cb) && (info == null ? void 0 : info.cb());
          }
        };
        if (script2) {
          script2.onerror = null;
          script2.onload = null;
          safeWrapper(() => {
            const { needDeleteScript = true } = info;
            if (needDeleteScript) {
              (script2 == null ? void 0 : script2.parentNode) && script2.parentNode.removeChild(script2);
            }
          });
          if (prev && typeof prev === "function") {
            const result = prev(event);
            if (result instanceof Promise) {
              const res2 = await result;
              onScriptCompleteCallback();
              return res2;
            }
            onScriptCompleteCallback();
            return result;
          }
        }
        onScriptCompleteCallback();
      };
      script2.onerror = onScriptComplete.bind(null, script2.onerror);
      script2.onload = onScriptComplete.bind(null, script2.onload);
      timeoutId = setTimeout(() => {
        onScriptComplete(null, new Error(`Remote script "${info.url}" time-outed.`));
      }, timeout);
      return {
        script: script2,
        needAttach
      };
    }
    function createLink(info) {
      let link = null;
      let needAttach = true;
      const links = document.getElementsByTagName("link");
      for (let i = 0; i < links.length; i++) {
        const l = links[i];
        const linkHref = l.getAttribute("href");
        const linkRel = l.getAttribute("rel");
        if (linkHref && isStaticResourcesEqual(linkHref, info.url) && linkRel === info.attrs["rel"]) {
          link = l;
          needAttach = false;
          break;
        }
      }
      if (!link) {
        link = document.createElement("link");
        link.setAttribute("href", info.url);
        let createLinkRes = void 0;
        const attrs2 = info.attrs;
        if (info.createLinkHook) {
          createLinkRes = info.createLinkHook(info.url, attrs2);
          if (createLinkRes instanceof HTMLLinkElement) {
            link = createLinkRes;
          }
        }
        if (attrs2 && !createLinkRes) {
          Object.keys(attrs2).forEach((name) => {
            if (link && !link.getAttribute(name)) {
              link.setAttribute(name, attrs2[name]);
            }
          });
        }
      }
      const onLinkComplete = (prev, event) => {
        const onLinkCompleteCallback = () => {
          if ((event == null ? void 0 : event.type) === "error") {
            (info == null ? void 0 : info.onErrorCallback) && (info == null ? void 0 : info.onErrorCallback(event));
          } else {
            (info == null ? void 0 : info.cb) && (info == null ? void 0 : info.cb());
          }
        };
        if (link) {
          link.onerror = null;
          link.onload = null;
          safeWrapper(() => {
            const { needDeleteLink = true } = info;
            if (needDeleteLink) {
              (link == null ? void 0 : link.parentNode) && link.parentNode.removeChild(link);
            }
          });
          if (prev) {
            const res2 = prev(event);
            onLinkCompleteCallback();
            return res2;
          }
        }
        onLinkCompleteCallback();
      };
      link.onerror = onLinkComplete.bind(null, link.onerror);
      link.onload = onLinkComplete.bind(null, link.onload);
      return {
        link,
        needAttach
      };
    }
    function loadScript(url2, info) {
      const { attrs: attrs2 = {}, createScriptHook } = info;
      return new Promise((resolve, reject) => {
        const { script: script2, needAttach } = createScript({
          url: url2,
          cb: resolve,
          onErrorCallback: reject,
          attrs: polyfills._({
            fetchpriority: "high"
          }, attrs2),
          createScriptHook,
          needDeleteScript: true
        });
        needAttach && document.head.appendChild(script2);
      });
    }
    function importNodeModule(name) {
      if (!name) {
        throw new Error("import specifier is required");
      }
      const importModule = new Function("name", `return import(name)`);
      return importModule(name).then((res2) => res2).catch((error2) => {
        console.error(`Error importing module ${name}:`, error2);
        throw error2;
      });
    }
    var loadNodeFetch = async () => {
      const fetchModule = await importNodeModule("node-fetch");
      return fetchModule.default || fetchModule;
    };
    var lazyLoaderHookFetch = async (input, init, loaderHook2) => {
      const hook = (url2, init2) => {
        return loaderHook2.lifecycle.fetch.emit(url2, init2);
      };
      const res2 = await hook(input, init || {});
      if (!res2 || !(res2 instanceof Response)) {
        const fetchFunction = typeof fetch === "undefined" ? await loadNodeFetch() : fetch;
        return fetchFunction(input, init || {});
      }
      return res2;
    };
    var createScriptNode = typeof ENV_TARGET === "undefined" || ENV_TARGET !== "web" ? (url, cb, attrs, loaderHook) => {
      if (loaderHook == null ? void 0 : loaderHook.createScriptHook) {
        const hookResult = loaderHook.createScriptHook(url);
        if (hookResult && typeof hookResult === "object" && "url" in hookResult) {
          url = hookResult.url;
        }
      }
      let urlObj;
      try {
        urlObj = new URL(url);
      } catch (e) {
        console.error("Error constructing URL:", e);
        cb(new Error(`Invalid URL: ${e}`));
        return;
      }
      const getFetch = async () => {
        if (loaderHook == null ? void 0 : loaderHook.fetch) {
          return (input, init) => lazyLoaderHookFetch(input, init, loaderHook);
        }
        return typeof fetch === "undefined" ? loadNodeFetch() : fetch;
      };
      const handleScriptFetch = async (f, urlObj) => {
        try {
          var _vm_constants;
          const res = await f(urlObj.href);
          const data = await res.text();
          const [path, vm] = await Promise.all([
            importNodeModule("path"),
            importNodeModule("vm")
          ]);
          const scriptContext = {
            exports: {},
            module: {
              exports: {}
            }
          };
          const urlDirname = urlObj.pathname.split("/").slice(0, -1).join("/");
          const filename = path.basename(urlObj.pathname);
          var _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER;
          const script = new vm.Script(`(function(exports, module, require, __dirname, __filename) {${data}
})`, {
            filename,
            importModuleDynamically: (_vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER = (_vm_constants = vm.constants) == null ? void 0 : _vm_constants.USE_MAIN_CONTEXT_DEFAULT_LOADER) != null ? _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER : importNodeModule
          });
          script.runInThisContext()(scriptContext.exports, scriptContext.module, eval("require"), urlDirname, filename);
          const exportedInterface = scriptContext.module.exports || scriptContext.exports;
          if (attrs && exportedInterface && attrs["globalName"]) {
            const container = exportedInterface[attrs["globalName"]] || exportedInterface;
            cb(void 0, container);
            return;
          }
          cb(void 0, exportedInterface);
        } catch (e) {
          cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));
        }
      };
      getFetch().then(async (f2) => {
        if ((attrs == null ? void 0 : attrs["type"]) === "esm" || (attrs == null ? void 0 : attrs["type"]) === "module") {
          return loadModule(urlObj.href, {
            fetch: f2,
            vm: await importNodeModule("vm")
          }).then(async (module2) => {
            await module2.evaluate();
            cb(void 0, module2.namespace);
          }).catch((e) => {
            cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));
          });
        }
        handleScriptFetch(f2, urlObj);
      }).catch((err) => {
        cb(err);
      });
    } : (url2, cb2, attrs2, loaderHook2) => {
      cb2(new Error("createScriptNode is disabled in non-Node.js environment"));
    };
    var loadScriptNode = typeof ENV_TARGET === "undefined" || ENV_TARGET !== "web" ? (url2, info) => {
      return new Promise((resolve, reject) => {
        createScriptNode(url2, (error2, scriptContext2) => {
          if (error2) {
            reject(error2);
          } else {
            var _info_attrs, _info_attrs1;
            const remoteEntryKey = (info == null ? void 0 : (_info_attrs = info.attrs) == null ? void 0 : _info_attrs["globalName"]) || `__FEDERATION_${info == null ? void 0 : (_info_attrs1 = info.attrs) == null ? void 0 : _info_attrs1["name"]}:custom__`;
            const entryExports = globalThis[remoteEntryKey] = scriptContext2;
            resolve(entryExports);
          }
        }, info.attrs, info.loaderHook);
      });
    } : (url2, info) => {
      throw new Error("loadScriptNode is disabled in non-Node.js environment");
    };
    async function loadModule(url2, options) {
      const { fetch: fetch1, vm: vm2 } = options;
      const response = await fetch1(url2);
      const code = await response.text();
      const module2 = new vm2.SourceTextModule(code, {
        // @ts-ignore
        importModuleDynamically: async (specifier, script2) => {
          const resolvedUrl = new URL(specifier, url2).href;
          return loadModule(resolvedUrl, options);
        }
      });
      await module2.link(async (specifier) => {
        const resolvedUrl = new URL(specifier, url2).href;
        const module3 = await loadModule(resolvedUrl, options);
        return module3;
      });
      return module2;
    }
    function normalizeOptions(enableDefault, defaultOptions, key) {
      return function(options) {
        if (options === false) {
          return false;
        }
        if (typeof options === "undefined") {
          if (enableDefault) {
            return defaultOptions;
          } else {
            return false;
          }
        }
        if (options === true) {
          return defaultOptions;
        }
        if (options && typeof options === "object") {
          return polyfills._({}, defaultOptions, options);
        }
        throw new Error(`Unexpected type for \`${key}\`, expect boolean/undefined/object, got: ${typeof options}`);
      };
    }
    var createModuleFederationConfig = (options) => {
      return options;
    };
    exports.BROWSER_LOG_KEY = BROWSER_LOG_KEY;
    exports.ENCODE_NAME_PREFIX = ENCODE_NAME_PREFIX;
    exports.EncodedNameTransformMap = EncodedNameTransformMap;
    exports.FederationModuleManifest = FederationModuleManifest;
    exports.MANIFEST_EXT = MANIFEST_EXT;
    exports.MFModuleType = MFModuleType;
    exports.MFPrefetchCommon = MFPrefetchCommon;
    exports.MODULE_DEVTOOL_IDENTIFIER = MODULE_DEVTOOL_IDENTIFIER;
    exports.ManifestFileName = ManifestFileName;
    exports.NameTransformMap = NameTransformMap;
    exports.NameTransformSymbol = NameTransformSymbol;
    exports.SEPARATOR = SEPARATOR;
    exports.StatsFileName = StatsFileName;
    exports.TEMP_DIR = TEMP_DIR;
    exports.assert = assert;
    exports.composeKeyWithSeparator = composeKeyWithSeparator;
    exports.containerPlugin = ContainerPlugin;
    exports.containerReferencePlugin = ContainerReferencePlugin;
    exports.createLink = createLink;
    exports.createLogger = createLogger;
    exports.createModuleFederationConfig = createModuleFederationConfig;
    exports.createScript = createScript;
    exports.createScriptNode = createScriptNode;
    exports.decodeName = decodeName;
    exports.encodeName = encodeName;
    exports.error = error;
    exports.generateExposeFilename = generateExposeFilename;
    exports.generateShareFilename = generateShareFilename;
    exports.generateSnapshotFromManifest = generateSnapshotFromManifest;
    exports.getProcessEnv = getProcessEnv;
    exports.getResourceUrl = getResourceUrl;
    exports.inferAutoPublicPath = inferAutoPublicPath;
    exports.isBrowserEnv = isBrowserEnv;
    exports.isDebugMode = isDebugMode;
    exports.isManifestProvider = isManifestProvider;
    exports.isReactNativeEnv = isReactNativeEnv;
    exports.isRequiredVersion = isRequiredVersion;
    exports.isStaticResourcesEqual = isStaticResourcesEqual;
    exports.loadScript = loadScript;
    exports.loadScriptNode = loadScriptNode;
    exports.logger = logger;
    exports.moduleFederationPlugin = ModuleFederationPlugin;
    exports.normalizeOptions = normalizeOptions;
    exports.parseEntry = parseEntry;
    exports.safeToString = safeToString;
    exports.safeWrapper = safeWrapper;
    exports.sharePlugin = SharePlugin;
    exports.simpleJoinRemoteEntry = simpleJoinRemoteEntry;
    exports.warn = warn;
  }
});

// node_modules/@module-federation/error-codes/dist/index.cjs.js
var require_index_cjs2 = __commonJS({
  "node_modules/@module-federation/error-codes/dist/index.cjs.js"(exports2) {
    "use strict";
    var RUNTIME_001 = "RUNTIME-001";
    var RUNTIME_002 = "RUNTIME-002";
    var RUNTIME_003 = "RUNTIME-003";
    var RUNTIME_004 = "RUNTIME-004";
    var RUNTIME_005 = "RUNTIME-005";
    var RUNTIME_006 = "RUNTIME-006";
    var RUNTIME_007 = "RUNTIME-007";
    var RUNTIME_008 = "RUNTIME-008";
    var RUNTIME_009 = "RUNTIME-009";
    var TYPE_001 = "TYPE-001";
    var BUILD_001 = "BUILD-001";
    var BUILD_002 = "BUILD-002";
    var getDocsUrl = (errorCode) => {
      const type = errorCode.split("-")[0].toLowerCase();
      return `View the docs to see how to solve: https://module-federation.io/guide/troubleshooting/${type}/${errorCode}`;
    };
    var getShortErrorMsg = (errorCode, errorDescMap2, args, originalErrorMsg) => {
      const msg = [
        `${[
          errorDescMap2[errorCode]
        ]} #${errorCode}`
      ];
      args && msg.push(`args: ${JSON.stringify(args)}`);
      msg.push(getDocsUrl(errorCode));
      originalErrorMsg && msg.push(`Original Error Message:
 ${originalErrorMsg}`);
      return msg.join("\n");
    };
    function _extends() {
      _extends = Object.assign || function assign(target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i];
          for (var key in source) if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];
        }
        return target;
      };
      return _extends.apply(this, arguments);
    }
    var runtimeDescMap = {
      [RUNTIME_001]: "Failed to get remoteEntry exports.",
      [RUNTIME_002]: 'The remote entry interface does not contain "init"',
      [RUNTIME_003]: "Failed to get manifest.",
      [RUNTIME_004]: "Failed to locate remote.",
      [RUNTIME_005]: "Invalid loadShareSync function call from bundler runtime",
      [RUNTIME_006]: "Invalid loadShareSync function call from runtime",
      [RUNTIME_007]: "Failed to get remote snapshot.",
      [RUNTIME_008]: "Failed to load script resources.",
      [RUNTIME_009]: "Please call createInstance first."
    };
    var typeDescMap = {
      [TYPE_001]: "Failed to generate type declaration. Execute the below cmd to reproduce and fix the error."
    };
    var buildDescMap = {
      [BUILD_001]: "Failed to find expose module.",
      [BUILD_002]: "PublicPath is required in prod mode."
    };
    var errorDescMap = _extends({}, runtimeDescMap, typeDescMap, buildDescMap);
    exports2.BUILD_001 = BUILD_001;
    exports2.BUILD_002 = BUILD_002;
    exports2.RUNTIME_001 = RUNTIME_001;
    exports2.RUNTIME_002 = RUNTIME_002;
    exports2.RUNTIME_003 = RUNTIME_003;
    exports2.RUNTIME_004 = RUNTIME_004;
    exports2.RUNTIME_005 = RUNTIME_005;
    exports2.RUNTIME_006 = RUNTIME_006;
    exports2.RUNTIME_007 = RUNTIME_007;
    exports2.RUNTIME_008 = RUNTIME_008;
    exports2.RUNTIME_009 = RUNTIME_009;
    exports2.TYPE_001 = TYPE_001;
    exports2.buildDescMap = buildDescMap;
    exports2.errorDescMap = errorDescMap;
    exports2.getShortErrorMsg = getShortErrorMsg;
    exports2.runtimeDescMap = runtimeDescMap;
    exports2.typeDescMap = typeDescMap;
  }
});

// node_modules/@module-federation/runtime-core/dist/index.cjs.cjs
var require_index_cjs3 = __commonJS({
  "node_modules/@module-federation/runtime-core/dist/index.cjs.cjs"(exports2) {
    "use strict";
    var polyfills2 = require_polyfills_cjs();
    var sdk = require_index_cjs();
    var errorCodes = require_index_cjs2();
    var LOG_CATEGORY2 = "[ Federation Runtime ]";
    var logger2 = sdk.createLogger(LOG_CATEGORY2);
    function assert2(condition, msg) {
      if (!condition) {
        error2(msg);
      }
    }
    function error2(msg) {
      if (msg instanceof Error) {
        if (!msg.message.startsWith(LOG_CATEGORY2)) {
          msg.message = `${LOG_CATEGORY2}: ${msg.message}`;
        }
        throw msg;
      }
      throw new Error(`${LOG_CATEGORY2}: ${msg}`);
    }
    function warn2(msg) {
      if (msg instanceof Error) {
        if (!msg.message.startsWith(LOG_CATEGORY2)) {
          msg.message = `${LOG_CATEGORY2}: ${msg.message}`;
        }
        logger2.warn(msg);
      } else {
        logger2.warn(msg);
      }
    }
    function addUniqueItem(arr, item) {
      if (arr.findIndex((name) => name === item) === -1) {
        arr.push(item);
      }
      return arr;
    }
    function getFMId(remoteInfo) {
      if ("version" in remoteInfo && remoteInfo.version) {
        return `${remoteInfo.name}:${remoteInfo.version}`;
      } else if ("entry" in remoteInfo && remoteInfo.entry) {
        return `${remoteInfo.name}:${remoteInfo.entry}`;
      } else {
        return `${remoteInfo.name}`;
      }
    }
    function isRemoteInfoWithEntry(remote) {
      return typeof remote.entry !== "undefined";
    }
    function isPureRemoteEntry(remote) {
      return !remote.entry.includes(".json");
    }
    async function safeWrapper2(callback, disableWarn) {
      try {
        const res2 = await callback();
        return res2;
      } catch (e) {
        !disableWarn && warn2(e);
        return;
      }
    }
    function isObject(val) {
      return val && typeof val === "object";
    }
    var objectToString = Object.prototype.toString;
    function isPlainObject(val) {
      return objectToString.call(val) === "[object Object]";
    }
    function isStaticResourcesEqual2(url1, url2) {
      const REG_EXP = /^(https?:)?\/\//i;
      const relativeUrl1 = url1.replace(REG_EXP, "").replace(/\/$/, "");
      const relativeUrl2 = url2.replace(REG_EXP, "").replace(/\/$/, "");
      return relativeUrl1 === relativeUrl2;
    }
    function arrayOptions(options) {
      return Array.isArray(options) ? options : [
        options
      ];
    }
    function getRemoteEntryInfoFromSnapshot(snapshot) {
      const defaultRemoteEntryInfo = {
        url: "",
        type: "global",
        globalName: ""
      };
      if (sdk.isBrowserEnv() || sdk.isReactNativeEnv()) {
        return "remoteEntry" in snapshot ? {
          url: snapshot.remoteEntry,
          type: snapshot.remoteEntryType,
          globalName: snapshot.globalName
        } : defaultRemoteEntryInfo;
      }
      if ("ssrRemoteEntry" in snapshot) {
        return {
          url: snapshot.ssrRemoteEntry || defaultRemoteEntryInfo.url,
          type: snapshot.ssrRemoteEntryType || defaultRemoteEntryInfo.type,
          globalName: snapshot.globalName
        };
      }
      return defaultRemoteEntryInfo;
    }
    var processModuleAlias = (name, subPath) => {
      let moduleName;
      if (name.endsWith("/")) {
        moduleName = name.slice(0, -1);
      } else {
        moduleName = name;
      }
      if (subPath.startsWith(".")) {
        subPath = subPath.slice(1);
      }
      moduleName = moduleName + subPath;
      return moduleName;
    };
    var CurrentGlobal = typeof globalThis === "object" ? globalThis : window;
    var nativeGlobal = (() => {
      try {
        return document.defaultView;
      } catch (e) {
        return CurrentGlobal;
      }
    })();
    var Global = nativeGlobal;
    function definePropertyGlobalVal(target, key, val) {
      Object.defineProperty(target, key, {
        value: val,
        configurable: false,
        writable: true
      });
    }
    function includeOwnProperty(target, key) {
      return Object.hasOwnProperty.call(target, key);
    }
    if (!includeOwnProperty(CurrentGlobal, "__GLOBAL_LOADING_REMOTE_ENTRY__")) {
      definePropertyGlobalVal(CurrentGlobal, "__GLOBAL_LOADING_REMOTE_ENTRY__", {});
    }
    var globalLoading = CurrentGlobal.__GLOBAL_LOADING_REMOTE_ENTRY__;
    function setGlobalDefaultVal(target) {
      var _target___FEDERATION__, _target___FEDERATION__1, _target___FEDERATION__2, _target___FEDERATION__3, _target___FEDERATION__4, _target___FEDERATION__5;
      if (includeOwnProperty(target, "__VMOK__") && !includeOwnProperty(target, "__FEDERATION__")) {
        definePropertyGlobalVal(target, "__FEDERATION__", target.__VMOK__);
      }
      if (!includeOwnProperty(target, "__FEDERATION__")) {
        definePropertyGlobalVal(target, "__FEDERATION__", {
          __GLOBAL_PLUGIN__: [],
          __INSTANCES__: [],
          moduleInfo: {},
          __SHARE__: {},
          __MANIFEST_LOADING__: {},
          __PRELOADED_MAP__: /* @__PURE__ */ new Map()
        });
        definePropertyGlobalVal(target, "__VMOK__", target.__FEDERATION__);
      }
      var ___GLOBAL_PLUGIN__;
      (___GLOBAL_PLUGIN__ = (_target___FEDERATION__ = target.__FEDERATION__).__GLOBAL_PLUGIN__) != null ? ___GLOBAL_PLUGIN__ : _target___FEDERATION__.__GLOBAL_PLUGIN__ = [];
      var ___INSTANCES__;
      (___INSTANCES__ = (_target___FEDERATION__1 = target.__FEDERATION__).__INSTANCES__) != null ? ___INSTANCES__ : _target___FEDERATION__1.__INSTANCES__ = [];
      var _moduleInfo;
      (_moduleInfo = (_target___FEDERATION__2 = target.__FEDERATION__).moduleInfo) != null ? _moduleInfo : _target___FEDERATION__2.moduleInfo = {};
      var ___SHARE__;
      (___SHARE__ = (_target___FEDERATION__3 = target.__FEDERATION__).__SHARE__) != null ? ___SHARE__ : _target___FEDERATION__3.__SHARE__ = {};
      var ___MANIFEST_LOADING__;
      (___MANIFEST_LOADING__ = (_target___FEDERATION__4 = target.__FEDERATION__).__MANIFEST_LOADING__) != null ? ___MANIFEST_LOADING__ : _target___FEDERATION__4.__MANIFEST_LOADING__ = {};
      var ___PRELOADED_MAP__;
      (___PRELOADED_MAP__ = (_target___FEDERATION__5 = target.__FEDERATION__).__PRELOADED_MAP__) != null ? ___PRELOADED_MAP__ : _target___FEDERATION__5.__PRELOADED_MAP__ = /* @__PURE__ */ new Map();
    }
    setGlobalDefaultVal(CurrentGlobal);
    setGlobalDefaultVal(nativeGlobal);
    function resetFederationGlobalInfo() {
      CurrentGlobal.__FEDERATION__.__GLOBAL_PLUGIN__ = [];
      CurrentGlobal.__FEDERATION__.__INSTANCES__ = [];
      CurrentGlobal.__FEDERATION__.moduleInfo = {};
      CurrentGlobal.__FEDERATION__.__SHARE__ = {};
      CurrentGlobal.__FEDERATION__.__MANIFEST_LOADING__ = {};
      Object.keys(globalLoading).forEach((key) => {
        delete globalLoading[key];
      });
    }
    function setGlobalFederationInstance(FederationInstance) {
      CurrentGlobal.__FEDERATION__.__INSTANCES__.push(FederationInstance);
    }
    function getGlobalFederationConstructor() {
      return CurrentGlobal.__FEDERATION__.__DEBUG_CONSTRUCTOR__;
    }
    function setGlobalFederationConstructor(FederationConstructor, isDebug = sdk.isDebugMode()) {
      if (isDebug) {
        CurrentGlobal.__FEDERATION__.__DEBUG_CONSTRUCTOR__ = FederationConstructor;
        CurrentGlobal.__FEDERATION__.__DEBUG_CONSTRUCTOR_VERSION__ = "0.17.1";
      }
    }
    function getInfoWithoutType(target, key) {
      if (typeof key === "string") {
        const keyRes = target[key];
        if (keyRes) {
          return {
            value: target[key],
            key
          };
        } else {
          const targetKeys = Object.keys(target);
          for (const targetKey of targetKeys) {
            const [targetTypeOrName, _] = targetKey.split(":");
            const nKey = `${targetTypeOrName}:${key}`;
            const typeWithKeyRes = target[nKey];
            if (typeWithKeyRes) {
              return {
                value: typeWithKeyRes,
                key: nKey
              };
            }
          }
          return {
            value: void 0,
            key
          };
        }
      } else {
        throw new Error("key must be string");
      }
    }
    var getGlobalSnapshot = () => nativeGlobal.__FEDERATION__.moduleInfo;
    var getTargetSnapshotInfoByModuleInfo = (moduleInfo, snapshot) => {
      const moduleKey = getFMId(moduleInfo);
      const getModuleInfo = getInfoWithoutType(snapshot, moduleKey).value;
      if (getModuleInfo && !getModuleInfo.version && "version" in moduleInfo && moduleInfo["version"]) {
        getModuleInfo.version = moduleInfo["version"];
      }
      if (getModuleInfo) {
        return getModuleInfo;
      }
      if ("version" in moduleInfo && moduleInfo["version"]) {
        const { version } = moduleInfo, resModuleInfo = polyfills2._object_without_properties_loose(moduleInfo, [
          "version"
        ]);
        const moduleKeyWithoutVersion = getFMId(resModuleInfo);
        const getModuleInfoWithoutVersion = getInfoWithoutType(nativeGlobal.__FEDERATION__.moduleInfo, moduleKeyWithoutVersion).value;
        if ((getModuleInfoWithoutVersion == null ? void 0 : getModuleInfoWithoutVersion.version) === version) {
          return getModuleInfoWithoutVersion;
        }
      }
      return;
    };
    var getGlobalSnapshotInfoByModuleInfo = (moduleInfo) => getTargetSnapshotInfoByModuleInfo(moduleInfo, nativeGlobal.__FEDERATION__.moduleInfo);
    var setGlobalSnapshotInfoByModuleInfo = (remoteInfo, moduleDetailInfo) => {
      const moduleKey = getFMId(remoteInfo);
      nativeGlobal.__FEDERATION__.moduleInfo[moduleKey] = moduleDetailInfo;
      return nativeGlobal.__FEDERATION__.moduleInfo;
    };
    var addGlobalSnapshot = (moduleInfos) => {
      nativeGlobal.__FEDERATION__.moduleInfo = polyfills2._extends({}, nativeGlobal.__FEDERATION__.moduleInfo, moduleInfos);
      return () => {
        const keys = Object.keys(moduleInfos);
        for (const key of keys) {
          delete nativeGlobal.__FEDERATION__.moduleInfo[key];
        }
      };
    };
    var getRemoteEntryExports = (name, globalName) => {
      const remoteEntryKey = globalName || `__FEDERATION_${name}:custom__`;
      const entryExports = CurrentGlobal[remoteEntryKey];
      return {
        remoteEntryKey,
        entryExports
      };
    };
    var registerGlobalPlugins = (plugins) => {
      const { __GLOBAL_PLUGIN__ } = nativeGlobal.__FEDERATION__;
      plugins.forEach((plugin) => {
        if (__GLOBAL_PLUGIN__.findIndex((p) => p.name === plugin.name) === -1) {
          __GLOBAL_PLUGIN__.push(plugin);
        } else {
          warn2(`The plugin ${plugin.name} has been registered.`);
        }
      });
    };
    var getGlobalHostPlugins = () => nativeGlobal.__FEDERATION__.__GLOBAL_PLUGIN__;
    var getPreloaded = (id) => CurrentGlobal.__FEDERATION__.__PRELOADED_MAP__.get(id);
    var setPreloaded = (id) => CurrentGlobal.__FEDERATION__.__PRELOADED_MAP__.set(id, true);
    var DEFAULT_SCOPE = "default";
    var DEFAULT_REMOTE_TYPE = "global";
    var buildIdentifier = "[0-9A-Za-z-]+";
    var build = `(?:\\+(${buildIdentifier}(?:\\.${buildIdentifier})*))`;
    var numericIdentifier = "0|[1-9]\\d*";
    var numericIdentifierLoose = "[0-9]+";
    var nonNumericIdentifier = "\\d*[a-zA-Z-][a-zA-Z0-9-]*";
    var preReleaseIdentifierLoose = `(?:${numericIdentifierLoose}|${nonNumericIdentifier})`;
    var preReleaseLoose = `(?:-?(${preReleaseIdentifierLoose}(?:\\.${preReleaseIdentifierLoose})*))`;
    var preReleaseIdentifier = `(?:${numericIdentifier}|${nonNumericIdentifier})`;
    var preRelease = `(?:-(${preReleaseIdentifier}(?:\\.${preReleaseIdentifier})*))`;
    var xRangeIdentifier = `${numericIdentifier}|x|X|\\*`;
    var xRangePlain = `[v=\\s]*(${xRangeIdentifier})(?:\\.(${xRangeIdentifier})(?:\\.(${xRangeIdentifier})(?:${preRelease})?${build}?)?)?`;
    var hyphenRange = `^\\s*(${xRangePlain})\\s+-\\s+(${xRangePlain})\\s*$`;
    var mainVersionLoose = `(${numericIdentifierLoose})\\.(${numericIdentifierLoose})\\.(${numericIdentifierLoose})`;
    var loosePlain = `[v=\\s]*${mainVersionLoose}${preReleaseLoose}?${build}?`;
    var gtlt = "((?:<|>)?=?)";
    var comparatorTrim = `(\\s*)${gtlt}\\s*(${loosePlain}|${xRangePlain})`;
    var loneTilde = "(?:~>?)";
    var tildeTrim = `(\\s*)${loneTilde}\\s+`;
    var loneCaret = "(?:\\^)";
    var caretTrim = `(\\s*)${loneCaret}\\s+`;
    var star = "(<|>)?=?\\s*\\*";
    var caret = `^${loneCaret}${xRangePlain}$`;
    var mainVersion = `(${numericIdentifier})\\.(${numericIdentifier})\\.(${numericIdentifier})`;
    var fullPlain = `v?${mainVersion}${preRelease}?${build}?`;
    var tilde = `^${loneTilde}${xRangePlain}$`;
    var xRange = `^${gtlt}\\s*${xRangePlain}$`;
    var comparator = `^${gtlt}\\s*(${fullPlain})$|^$`;
    var gte0 = "^\\s*>=\\s*0.0.0\\s*$";
    function parseRegex(source) {
      return new RegExp(source);
    }
    function isXVersion(version) {
      return !version || version.toLowerCase() === "x" || version === "*";
    }
    function pipe(...fns) {
      return (x) => fns.reduce((v, f2) => f2(v), x);
    }
    function extractComparator(comparatorString) {
      return comparatorString.match(parseRegex(comparator));
    }
    function combineVersion(major, minor, patch, preRelease2) {
      const mainVersion2 = `${major}.${minor}.${patch}`;
      if (preRelease2) {
        return `${mainVersion2}-${preRelease2}`;
      }
      return mainVersion2;
    }
    function parseHyphen(range) {
      return range.replace(parseRegex(hyphenRange), (_range, from, fromMajor, fromMinor, fromPatch, _fromPreRelease, _fromBuild, to, toMajor, toMinor, toPatch, toPreRelease) => {
        if (isXVersion(fromMajor)) {
          from = "";
        } else if (isXVersion(fromMinor)) {
          from = `>=${fromMajor}.0.0`;
        } else if (isXVersion(fromPatch)) {
          from = `>=${fromMajor}.${fromMinor}.0`;
        } else {
          from = `>=${from}`;
        }
        if (isXVersion(toMajor)) {
          to = "";
        } else if (isXVersion(toMinor)) {
          to = `<${Number(toMajor) + 1}.0.0-0`;
        } else if (isXVersion(toPatch)) {
          to = `<${toMajor}.${Number(toMinor) + 1}.0-0`;
        } else if (toPreRelease) {
          to = `<=${toMajor}.${toMinor}.${toPatch}-${toPreRelease}`;
        } else {
          to = `<=${to}`;
        }
        return `${from} ${to}`.trim();
      });
    }
    function parseComparatorTrim(range) {
      return range.replace(parseRegex(comparatorTrim), "$1$2$3");
    }
    function parseTildeTrim(range) {
      return range.replace(parseRegex(tildeTrim), "$1~");
    }
    function parseCaretTrim(range) {
      return range.replace(parseRegex(caretTrim), "$1^");
    }
    function parseCarets(range) {
      return range.trim().split(/\s+/).map((rangeVersion) => rangeVersion.replace(parseRegex(caret), (_, major, minor, patch, preRelease2) => {
        if (isXVersion(major)) {
          return "";
        } else if (isXVersion(minor)) {
          return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;
        } else if (isXVersion(patch)) {
          if (major === "0") {
            return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;
          } else {
            return `>=${major}.${minor}.0 <${Number(major) + 1}.0.0-0`;
          }
        } else if (preRelease2) {
          if (major === "0") {
            if (minor === "0") {
              return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${minor}.${Number(patch) + 1}-0`;
            } else {
              return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${Number(minor) + 1}.0-0`;
            }
          } else {
            return `>=${major}.${minor}.${patch}-${preRelease2} <${Number(major) + 1}.0.0-0`;
          }
        } else {
          if (major === "0") {
            if (minor === "0") {
              return `>=${major}.${minor}.${patch} <${major}.${minor}.${Number(patch) + 1}-0`;
            } else {
              return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;
            }
          }
          return `>=${major}.${minor}.${patch} <${Number(major) + 1}.0.0-0`;
        }
      })).join(" ");
    }
    function parseTildes(range) {
      return range.trim().split(/\s+/).map((rangeVersion) => rangeVersion.replace(parseRegex(tilde), (_, major, minor, patch, preRelease2) => {
        if (isXVersion(major)) {
          return "";
        } else if (isXVersion(minor)) {
          return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;
        } else if (isXVersion(patch)) {
          return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;
        } else if (preRelease2) {
          return `>=${major}.${minor}.${patch}-${preRelease2} <${major}.${Number(minor) + 1}.0-0`;
        }
        return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;
      })).join(" ");
    }
    function parseXRanges(range) {
      return range.split(/\s+/).map((rangeVersion) => rangeVersion.trim().replace(parseRegex(xRange), (ret, gtlt2, major, minor, patch, preRelease2) => {
        const isXMajor = isXVersion(major);
        const isXMinor = isXMajor || isXVersion(minor);
        const isXPatch = isXMinor || isXVersion(patch);
        if (gtlt2 === "=" && isXPatch) {
          gtlt2 = "";
        }
        preRelease2 = "";
        if (isXMajor) {
          if (gtlt2 === ">" || gtlt2 === "<") {
            return "<0.0.0-0";
          } else {
            return "*";
          }
        } else if (gtlt2 && isXPatch) {
          if (isXMinor) {
            minor = 0;
          }
          patch = 0;
          if (gtlt2 === ">") {
            gtlt2 = ">=";
            if (isXMinor) {
              major = Number(major) + 1;
              minor = 0;
              patch = 0;
            } else {
              minor = Number(minor) + 1;
              patch = 0;
            }
          } else if (gtlt2 === "<=") {
            gtlt2 = "<";
            if (isXMinor) {
              major = Number(major) + 1;
            } else {
              minor = Number(minor) + 1;
            }
          }
          if (gtlt2 === "<") {
            preRelease2 = "-0";
          }
          return `${gtlt2 + major}.${minor}.${patch}${preRelease2}`;
        } else if (isXMinor) {
          return `>=${major}.0.0${preRelease2} <${Number(major) + 1}.0.0-0`;
        } else if (isXPatch) {
          return `>=${major}.${minor}.0${preRelease2} <${major}.${Number(minor) + 1}.0-0`;
        }
        return ret;
      })).join(" ");
    }
    function parseStar(range) {
      return range.trim().replace(parseRegex(star), "");
    }
    function parseGTE0(comparatorString) {
      return comparatorString.trim().replace(parseRegex(gte0), "");
    }
    function compareAtom(rangeAtom, versionAtom) {
      rangeAtom = Number(rangeAtom) || rangeAtom;
      versionAtom = Number(versionAtom) || versionAtom;
      if (rangeAtom > versionAtom) {
        return 1;
      }
      if (rangeAtom === versionAtom) {
        return 0;
      }
      return -1;
    }
    function comparePreRelease(rangeAtom, versionAtom) {
      const { preRelease: rangePreRelease } = rangeAtom;
      const { preRelease: versionPreRelease } = versionAtom;
      if (rangePreRelease === void 0 && Boolean(versionPreRelease)) {
        return 1;
      }
      if (Boolean(rangePreRelease) && versionPreRelease === void 0) {
        return -1;
      }
      if (rangePreRelease === void 0 && versionPreRelease === void 0) {
        return 0;
      }
      for (let i = 0, n = rangePreRelease.length; i <= n; i++) {
        const rangeElement = rangePreRelease[i];
        const versionElement = versionPreRelease[i];
        if (rangeElement === versionElement) {
          continue;
        }
        if (rangeElement === void 0 && versionElement === void 0) {
          return 0;
        }
        if (!rangeElement) {
          return 1;
        }
        if (!versionElement) {
          return -1;
        }
        return compareAtom(rangeElement, versionElement);
      }
      return 0;
    }
    function compareVersion(rangeAtom, versionAtom) {
      return compareAtom(rangeAtom.major, versionAtom.major) || compareAtom(rangeAtom.minor, versionAtom.minor) || compareAtom(rangeAtom.patch, versionAtom.patch) || comparePreRelease(rangeAtom, versionAtom);
    }
    function eq(rangeAtom, versionAtom) {
      return rangeAtom.version === versionAtom.version;
    }
    function compare(rangeAtom, versionAtom) {
      switch (rangeAtom.operator) {
        case "":
        case "=":
          return eq(rangeAtom, versionAtom);
        case ">":
          return compareVersion(rangeAtom, versionAtom) < 0;
        case ">=":
          return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) < 0;
        case "<":
          return compareVersion(rangeAtom, versionAtom) > 0;
        case "<=":
          return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) > 0;
        case void 0: {
          return true;
        }
        default:
          return false;
      }
    }
    function parseComparatorString(range) {
      return pipe(
        // handle caret
        // ^ --> * (any, kinda silly)
        // ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0
        // ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0
        // ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0
        // ^1.2.3 --> >=1.2.3 <2.0.0-0
        // ^1.2.0 --> >=1.2.0 <2.0.0-0
        parseCarets,
        // handle tilde
        // ~, ~> --> * (any, kinda silly)
        // ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0
        // ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0
        // ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0
        // ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0
        // ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0
        parseTildes,
        parseXRanges,
        parseStar
      )(range);
    }
    function parseRange(range) {
      return pipe(
        // handle hyphenRange
        // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`
        parseHyphen,
        // handle trim comparator
        // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`
        parseComparatorTrim,
        // handle trim tilde
        // `~ 1.2.3` => `~1.2.3`
        parseTildeTrim,
        // handle trim caret
        // `^ 1.2.3` => `^1.2.3`
        parseCaretTrim
      )(range.trim()).split(/\s+/).join(" ");
    }
    function satisfy(version, range) {
      if (!version) {
        return false;
      }
      const extractedVersion = extractComparator(version);
      if (!extractedVersion) {
        return false;
      }
      const [, versionOperator, , versionMajor, versionMinor, versionPatch, versionPreRelease] = extractedVersion;
      const versionAtom = {
        operator: versionOperator,
        version: combineVersion(versionMajor, versionMinor, versionPatch, versionPreRelease),
        major: versionMajor,
        minor: versionMinor,
        patch: versionPatch,
        preRelease: versionPreRelease == null ? void 0 : versionPreRelease.split(".")
      };
      const orRanges = range.split("||");
      for (const orRange of orRanges) {
        const trimmedOrRange = orRange.trim();
        if (!trimmedOrRange) {
          return true;
        }
        if (trimmedOrRange === "*" || trimmedOrRange === "x") {
          return true;
        }
        try {
          const parsedSubRange = parseRange(trimmedOrRange);
          if (!parsedSubRange.trim()) {
            return true;
          }
          const parsedComparatorString = parsedSubRange.split(" ").map((rangeVersion) => parseComparatorString(rangeVersion)).join(" ");
          if (!parsedComparatorString.trim()) {
            return true;
          }
          const comparators = parsedComparatorString.split(/\s+/).map((comparator2) => parseGTE0(comparator2)).filter(Boolean);
          if (comparators.length === 0) {
            continue;
          }
          let subRangeSatisfied = true;
          for (const comparator2 of comparators) {
            const extractedComparator = extractComparator(comparator2);
            if (!extractedComparator) {
              subRangeSatisfied = false;
              break;
            }
            const [, rangeOperator, , rangeMajor, rangeMinor, rangePatch, rangePreRelease] = extractedComparator;
            const rangeAtom = {
              operator: rangeOperator,
              version: combineVersion(rangeMajor, rangeMinor, rangePatch, rangePreRelease),
              major: rangeMajor,
              minor: rangeMinor,
              patch: rangePatch,
              preRelease: rangePreRelease == null ? void 0 : rangePreRelease.split(".")
            };
            if (!compare(rangeAtom, versionAtom)) {
              subRangeSatisfied = false;
              break;
            }
          }
          if (subRangeSatisfied) {
            return true;
          }
        } catch (e) {
          console.error(`[semver] Error processing range part "${trimmedOrRange}":`, e);
          continue;
        }
      }
      return false;
    }
    function formatShare(shareArgs, from, name, shareStrategy) {
      let get;
      if ("get" in shareArgs) {
        get = shareArgs.get;
      } else if ("lib" in shareArgs) {
        get = () => Promise.resolve(shareArgs.lib);
      } else {
        get = () => Promise.resolve(() => {
          throw new Error(`Can not get shared '${name}'!`);
        });
      }
      var _shareArgs_version, _shareArgs_scope, _shareArgs_strategy;
      return polyfills2._extends({
        deps: [],
        useIn: [],
        from,
        loading: null
      }, shareArgs, {
        shareConfig: polyfills2._extends({
          requiredVersion: `^${shareArgs.version}`,
          singleton: false,
          eager: false,
          strictVersion: false
        }, shareArgs.shareConfig),
        get,
        loaded: (shareArgs == null ? void 0 : shareArgs.loaded) || "lib" in shareArgs ? true : void 0,
        version: (_shareArgs_version = shareArgs.version) != null ? _shareArgs_version : "0",
        scope: Array.isArray(shareArgs.scope) ? shareArgs.scope : [
          (_shareArgs_scope = shareArgs.scope) != null ? _shareArgs_scope : "default"
        ],
        strategy: ((_shareArgs_strategy = shareArgs.strategy) != null ? _shareArgs_strategy : shareStrategy) || "version-first"
      });
    }
    function formatShareConfigs(globalOptions, userOptions) {
      const shareArgs = userOptions.shared || {};
      const from = userOptions.name;
      const shareInfos = Object.keys(shareArgs).reduce((res2, pkgName) => {
        const arrayShareArgs = arrayOptions(shareArgs[pkgName]);
        res2[pkgName] = res2[pkgName] || [];
        arrayShareArgs.forEach((shareConfig) => {
          res2[pkgName].push(formatShare(shareConfig, from, pkgName, userOptions.shareStrategy));
        });
        return res2;
      }, {});
      const shared = polyfills2._extends({}, globalOptions.shared);
      Object.keys(shareInfos).forEach((shareKey) => {
        if (!shared[shareKey]) {
          shared[shareKey] = shareInfos[shareKey];
        } else {
          shareInfos[shareKey].forEach((newUserSharedOptions) => {
            const isSameVersion = shared[shareKey].find((sharedVal) => sharedVal.version === newUserSharedOptions.version);
            if (!isSameVersion) {
              shared[shareKey].push(newUserSharedOptions);
            }
          });
        }
      });
      return {
        shared,
        shareInfos
      };
    }
    function versionLt(a, b) {
      const transformInvalidVersion = (version) => {
        const isNumberVersion = !Number.isNaN(Number(version));
        if (isNumberVersion) {
          const splitArr = version.split(".");
          let validVersion = version;
          for (let i = 0; i < 3 - splitArr.length; i++) {
            validVersion += ".0";
          }
          return validVersion;
        }
        return version;
      };
      if (satisfy(transformInvalidVersion(a), `<=${transformInvalidVersion(b)}`)) {
        return true;
      } else {
        return false;
      }
    }
    var findVersion = (shareVersionMap, cb2) => {
      const callback = cb2 || function(prev, cur) {
        return versionLt(prev, cur);
      };
      return Object.keys(shareVersionMap).reduce((prev, cur) => {
        if (!prev) {
          return cur;
        }
        if (callback(prev, cur)) {
          return cur;
        }
        if (prev === "0") {
          return cur;
        }
        return prev;
      }, 0);
    };
    var isLoaded = (shared) => {
      return Boolean(shared.loaded) || typeof shared.lib === "function";
    };
    var isLoading = (shared) => {
      return Boolean(shared.loading);
    };
    function findSingletonVersionOrderByVersion(shareScopeMap, scope, pkgName) {
      const versions = shareScopeMap[scope][pkgName];
      const callback = function(prev, cur) {
        return !isLoaded(versions[prev]) && versionLt(prev, cur);
      };
      return findVersion(shareScopeMap[scope][pkgName], callback);
    }
    function findSingletonVersionOrderByLoaded(shareScopeMap, scope, pkgName) {
      const versions = shareScopeMap[scope][pkgName];
      const callback = function(prev, cur) {
        const isLoadingOrLoaded = (shared) => {
          return isLoaded(shared) || isLoading(shared);
        };
        if (isLoadingOrLoaded(versions[cur])) {
          if (isLoadingOrLoaded(versions[prev])) {
            return Boolean(versionLt(prev, cur));
          } else {
            return true;
          }
        }
        if (isLoadingOrLoaded(versions[prev])) {
          return false;
        }
        return versionLt(prev, cur);
      };
      return findVersion(shareScopeMap[scope][pkgName], callback);
    }
    function getFindShareFunction(strategy) {
      if (strategy === "loaded-first") {
        return findSingletonVersionOrderByLoaded;
      }
      return findSingletonVersionOrderByVersion;
    }
    function getRegisteredShare(localShareScopeMap, pkgName, shareInfo, resolveShare) {
      if (!localShareScopeMap) {
        return;
      }
      const { shareConfig, scope = DEFAULT_SCOPE, strategy } = shareInfo;
      const scopes = Array.isArray(scope) ? scope : [
        scope
      ];
      for (const sc of scopes) {
        if (shareConfig && localShareScopeMap[sc] && localShareScopeMap[sc][pkgName]) {
          const { requiredVersion } = shareConfig;
          const findShareFunction = getFindShareFunction(strategy);
          const maxOrSingletonVersion = findShareFunction(localShareScopeMap, sc, pkgName);
          const defaultResolver = () => {
            if (shareConfig.singleton) {
              if (typeof requiredVersion === "string" && !satisfy(maxOrSingletonVersion, requiredVersion)) {
                const msg = `Version ${maxOrSingletonVersion} from ${maxOrSingletonVersion && localShareScopeMap[sc][pkgName][maxOrSingletonVersion].from} of shared singleton module ${pkgName} does not satisfy the requirement of ${shareInfo.from} which needs ${requiredVersion})`;
                if (shareConfig.strictVersion) {
                  error2(msg);
                } else {
                  warn2(msg);
                }
              }
              return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];
            } else {
              if (requiredVersion === false || requiredVersion === "*") {
                return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];
              }
              if (satisfy(maxOrSingletonVersion, requiredVersion)) {
                return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];
              }
              for (const [versionKey, versionValue] of Object.entries(localShareScopeMap[sc][pkgName])) {
                if (satisfy(versionKey, requiredVersion)) {
                  return versionValue;
                }
              }
            }
          };
          const params = {
            shareScopeMap: localShareScopeMap,
            scope: sc,
            pkgName,
            version: maxOrSingletonVersion,
            GlobalFederation: Global.__FEDERATION__,
            resolver: defaultResolver
          };
          const resolveShared = resolveShare.emit(params) || params;
          return resolveShared.resolver();
        }
      }
    }
    function getGlobalShareScope() {
      return Global.__FEDERATION__.__SHARE__;
    }
    function getTargetSharedOptions(options) {
      const { pkgName, extraOptions, shareInfos } = options;
      const defaultResolver = (sharedOptions) => {
        if (!sharedOptions) {
          return void 0;
        }
        const shareVersionMap = {};
        sharedOptions.forEach((shared) => {
          shareVersionMap[shared.version] = shared;
        });
        const callback = function(prev, cur) {
          return !isLoaded(shareVersionMap[prev]) && versionLt(prev, cur);
        };
        const maxVersion = findVersion(shareVersionMap, callback);
        return shareVersionMap[maxVersion];
      };
      var _extraOptions_resolver;
      const resolver = (_extraOptions_resolver = extraOptions == null ? void 0 : extraOptions.resolver) != null ? _extraOptions_resolver : defaultResolver;
      return Object.assign({}, resolver(shareInfos[pkgName]), extraOptions == null ? void 0 : extraOptions.customShareInfo);
    }
    function getBuilderId() {
      return typeof FEDERATION_BUILD_IDENTIFIER !== "undefined" ? FEDERATION_BUILD_IDENTIFIER : "";
    }
    function matchRemoteWithNameAndExpose(remotes, id) {
      for (const remote of remotes) {
        const isNameMatched = id.startsWith(remote.name);
        let expose = id.replace(remote.name, "");
        if (isNameMatched) {
          if (expose.startsWith("/")) {
            const pkgNameOrAlias = remote.name;
            expose = `.${expose}`;
            return {
              pkgNameOrAlias,
              expose,
              remote
            };
          } else if (expose === "") {
            return {
              pkgNameOrAlias: remote.name,
              expose: ".",
              remote
            };
          }
        }
        const isAliasMatched = remote.alias && id.startsWith(remote.alias);
        let exposeWithAlias = remote.alias && id.replace(remote.alias, "");
        if (remote.alias && isAliasMatched) {
          if (exposeWithAlias && exposeWithAlias.startsWith("/")) {
            const pkgNameOrAlias = remote.alias;
            exposeWithAlias = `.${exposeWithAlias}`;
            return {
              pkgNameOrAlias,
              expose: exposeWithAlias,
              remote
            };
          } else if (exposeWithAlias === "") {
            return {
              pkgNameOrAlias: remote.alias,
              expose: ".",
              remote
            };
          }
        }
      }
      return;
    }
    function matchRemote(remotes, nameOrAlias) {
      for (const remote of remotes) {
        const isNameMatched = nameOrAlias === remote.name;
        if (isNameMatched) {
          return remote;
        }
        const isAliasMatched = remote.alias && nameOrAlias === remote.alias;
        if (isAliasMatched) {
          return remote;
        }
      }
      return;
    }
    function registerPlugins(plugins, instance) {
      const globalPlugins = getGlobalHostPlugins();
      const hookInstances = [
        instance.hooks,
        instance.remoteHandler.hooks,
        instance.sharedHandler.hooks,
        instance.snapshotHandler.hooks,
        instance.loaderHook,
        instance.bridgeHook
      ];
      if (globalPlugins.length > 0) {
        globalPlugins.forEach((plugin) => {
          if (plugins == null ? void 0 : plugins.find((item) => item.name !== plugin.name)) {
            plugins.push(plugin);
          }
        });
      }
      if (plugins && plugins.length > 0) {
        plugins.forEach((plugin) => {
          hookInstances.forEach((hookInstance) => {
            hookInstance.applyPlugin(plugin, instance);
          });
        });
      }
      return plugins;
    }
    var importCallback = ".then(callbacks[0]).catch(callbacks[1])";
    async function loadEsmEntry({ entry, remoteEntryExports }) {
      return new Promise((resolve, reject) => {
        try {
          if (!remoteEntryExports) {
            if (typeof FEDERATION_ALLOW_NEW_FUNCTION !== "undefined") {
              new Function("callbacks", `import("${entry}")${importCallback}`)([
                resolve,
                reject
              ]);
            } else {
              import(
                /* webpackIgnore: true */
                /* @vite-ignore */
                entry
              ).then(resolve).catch(reject);
            }
          } else {
            resolve(remoteEntryExports);
          }
        } catch (e) {
          reject(e);
        }
      });
    }
    async function loadSystemJsEntry({ entry, remoteEntryExports }) {
      return new Promise((resolve, reject) => {
        try {
          if (!remoteEntryExports) {
            if (typeof __system_context__ === "undefined") {
              System.import(entry).then(resolve).catch(reject);
            } else {
              new Function("callbacks", `System.import("${entry}")${importCallback}`)([
                resolve,
                reject
              ]);
            }
          } else {
            resolve(remoteEntryExports);
          }
        } catch (e) {
          reject(e);
        }
      });
    }
    function handleRemoteEntryLoaded(name, globalName, entry) {
      const { remoteEntryKey, entryExports } = getRemoteEntryExports(name, globalName);
      assert2(entryExports, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_001, errorCodes.runtimeDescMap, {
        remoteName: name,
        remoteEntryUrl: entry,
        remoteEntryKey
      }));
      return entryExports;
    }
    async function loadEntryScript({ name, globalName, entry, loaderHook: loaderHook2 }) {
      const { entryExports: remoteEntryExports } = getRemoteEntryExports(name, globalName);
      if (remoteEntryExports) {
        return remoteEntryExports;
      }
      return sdk.loadScript(entry, {
        attrs: {},
        createScriptHook: (url2, attrs2) => {
          const res2 = loaderHook2.lifecycle.createScript.emit({
            url: url2,
            attrs: attrs2
          });
          if (!res2) return;
          if (res2 instanceof HTMLScriptElement) {
            return res2;
          }
          if ("script" in res2 || "timeout" in res2) {
            return res2;
          }
          return;
        }
      }).then(() => {
        return handleRemoteEntryLoaded(name, globalName, entry);
      }).catch((e) => {
        assert2(void 0, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_008, errorCodes.runtimeDescMap, {
          remoteName: name,
          resourceUrl: entry
        }));
        throw e;
      });
    }
    async function loadEntryDom({ remoteInfo, remoteEntryExports, loaderHook: loaderHook2 }) {
      const { entry, entryGlobalName: globalName, name, type } = remoteInfo;
      switch (type) {
        case "esm":
        case "module":
          return loadEsmEntry({
            entry,
            remoteEntryExports
          });
        case "system":
          return loadSystemJsEntry({
            entry,
            remoteEntryExports
          });
        default:
          return loadEntryScript({
            entry,
            globalName,
            name,
            loaderHook: loaderHook2
          });
      }
    }
    async function loadEntryNode({ remoteInfo, loaderHook: loaderHook2 }) {
      const { entry, entryGlobalName: globalName, name, type } = remoteInfo;
      const { entryExports: remoteEntryExports } = getRemoteEntryExports(name, globalName);
      if (remoteEntryExports) {
        return remoteEntryExports;
      }
      return sdk.loadScriptNode(entry, {
        attrs: {
          name,
          globalName,
          type
        },
        loaderHook: {
          createScriptHook: (url2, attrs2 = {}) => {
            const res2 = loaderHook2.lifecycle.createScript.emit({
              url: url2,
              attrs: attrs2
            });
            if (!res2) return;
            if ("url" in res2) {
              return res2;
            }
            return;
          }
        }
      }).then(() => {
        return handleRemoteEntryLoaded(name, globalName, entry);
      }).catch((e) => {
        throw e;
      });
    }
    function getRemoteEntryUniqueKey(remoteInfo) {
      const { entry, name } = remoteInfo;
      return sdk.composeKeyWithSeparator(name, entry);
    }
    async function getRemoteEntry({ origin, remoteEntryExports, remoteInfo }) {
      const uniqueKey = getRemoteEntryUniqueKey(remoteInfo);
      if (remoteEntryExports) {
        return remoteEntryExports;
      }
      if (!globalLoading[uniqueKey]) {
        const loadEntryHook = origin.remoteHandler.hooks.lifecycle.loadEntry;
        const loaderHook2 = origin.loaderHook;
        globalLoading[uniqueKey] = loadEntryHook.emit({
          loaderHook: loaderHook2,
          remoteInfo,
          remoteEntryExports
        }).then((res2) => {
          if (res2) {
            return res2;
          }
          const isWebEnvironment = typeof ENV_TARGET !== "undefined" ? ENV_TARGET === "web" : sdk.isBrowserEnv();
          return isWebEnvironment ? loadEntryDom({
            remoteInfo,
            remoteEntryExports,
            loaderHook: loaderHook2
          }) : loadEntryNode({
            remoteInfo,
            loaderHook: loaderHook2
          });
        });
      }
      return globalLoading[uniqueKey];
    }
    function getRemoteInfo(remote) {
      return polyfills2._extends({}, remote, {
        entry: "entry" in remote ? remote.entry : "",
        type: remote.type || DEFAULT_REMOTE_TYPE,
        entryGlobalName: remote.entryGlobalName || remote.name,
        shareScope: remote.shareScope || DEFAULT_SCOPE
      });
    }
    function defaultPreloadArgs(preloadConfig) {
      return polyfills2._extends({
        resourceCategory: "sync",
        share: true,
        depsRemote: true,
        prefetchInterface: false
      }, preloadConfig);
    }
    function formatPreloadArgs(remotes, preloadArgs) {
      return preloadArgs.map((args) => {
        const remoteInfo = matchRemote(remotes, args.nameOrAlias);
        assert2(remoteInfo, `Unable to preload ${args.nameOrAlias} as it is not included in ${!remoteInfo && sdk.safeToString({
          remoteInfo,
          remotes
        })}`);
        return {
          remote: remoteInfo,
          preloadConfig: defaultPreloadArgs(args)
        };
      });
    }
    function normalizePreloadExposes(exposes) {
      if (!exposes) {
        return [];
      }
      return exposes.map((expose) => {
        if (expose === ".") {
          return expose;
        }
        if (expose.startsWith("./")) {
          return expose.replace("./", "");
        }
        return expose;
      });
    }
    function preloadAssets(remoteInfo, host, assets, useLinkPreload = true) {
      const { cssAssets, jsAssetsWithoutEntry, entryAssets } = assets;
      if (host.options.inBrowser) {
        entryAssets.forEach((asset) => {
          const { moduleInfo } = asset;
          const module3 = host.moduleCache.get(remoteInfo.name);
          if (module3) {
            getRemoteEntry({
              origin: host,
              remoteInfo: moduleInfo,
              remoteEntryExports: module3.remoteEntryExports
            });
          } else {
            getRemoteEntry({
              origin: host,
              remoteInfo: moduleInfo,
              remoteEntryExports: void 0
            });
          }
        });
        if (useLinkPreload) {
          const defaultAttrs = {
            rel: "preload",
            as: "style"
          };
          cssAssets.forEach((cssUrl) => {
            const { link: cssEl, needAttach } = sdk.createLink({
              url: cssUrl,
              cb: () => {
              },
              attrs: defaultAttrs,
              createLinkHook: (url2, attrs2) => {
                const res2 = host.loaderHook.lifecycle.createLink.emit({
                  url: url2,
                  attrs: attrs2
                });
                if (res2 instanceof HTMLLinkElement) {
                  return res2;
                }
                return;
              }
            });
            needAttach && document.head.appendChild(cssEl);
          });
        } else {
          const defaultAttrs = {
            rel: "stylesheet",
            type: "text/css"
          };
          cssAssets.forEach((cssUrl) => {
            const { link: cssEl, needAttach } = sdk.createLink({
              url: cssUrl,
              cb: () => {
              },
              attrs: defaultAttrs,
              createLinkHook: (url2, attrs2) => {
                const res2 = host.loaderHook.lifecycle.createLink.emit({
                  url: url2,
                  attrs: attrs2
                });
                if (res2 instanceof HTMLLinkElement) {
                  return res2;
                }
                return;
              },
              needDeleteLink: false
            });
            needAttach && document.head.appendChild(cssEl);
          });
        }
        if (useLinkPreload) {
          const defaultAttrs = {
            rel: "preload",
            as: "script"
          };
          jsAssetsWithoutEntry.forEach((jsUrl) => {
            const { link: linkEl, needAttach } = sdk.createLink({
              url: jsUrl,
              cb: () => {
              },
              attrs: defaultAttrs,
              createLinkHook: (url2, attrs2) => {
                const res2 = host.loaderHook.lifecycle.createLink.emit({
                  url: url2,
                  attrs: attrs2
                });
                if (res2 instanceof HTMLLinkElement) {
                  return res2;
                }
                return;
              }
            });
            needAttach && document.head.appendChild(linkEl);
          });
        } else {
          const defaultAttrs = {
            fetchpriority: "high",
            type: (remoteInfo == null ? void 0 : remoteInfo.type) === "module" ? "module" : "text/javascript"
          };
          jsAssetsWithoutEntry.forEach((jsUrl) => {
            const { script: scriptEl, needAttach } = sdk.createScript({
              url: jsUrl,
              cb: () => {
              },
              attrs: defaultAttrs,
              createScriptHook: (url2, attrs2) => {
                const res2 = host.loaderHook.lifecycle.createScript.emit({
                  url: url2,
                  attrs: attrs2
                });
                if (res2 instanceof HTMLScriptElement) {
                  return res2;
                }
                return;
              },
              needDeleteScript: true
            });
            needAttach && document.head.appendChild(scriptEl);
          });
        }
      }
    }
    var ShareUtils = {
      getRegisteredShare,
      getGlobalShareScope
    };
    var GlobalUtils = {
      Global,
      nativeGlobal,
      resetFederationGlobalInfo,
      setGlobalFederationInstance,
      getGlobalFederationConstructor,
      setGlobalFederationConstructor,
      getInfoWithoutType,
      getGlobalSnapshot,
      getTargetSnapshotInfoByModuleInfo,
      getGlobalSnapshotInfoByModuleInfo,
      setGlobalSnapshotInfoByModuleInfo,
      addGlobalSnapshot,
      getRemoteEntryExports,
      registerGlobalPlugins,
      getGlobalHostPlugins,
      getPreloaded,
      setPreloaded
    };
    var helpers = {
      global: GlobalUtils,
      share: ShareUtils,
      utils: {
        matchRemoteWithNameAndExpose,
        preloadAssets,
        getRemoteInfo
      }
    };
    var Module = class Module {
      async getEntry() {
        if (this.remoteEntryExports) {
          return this.remoteEntryExports;
        }
        let remoteEntryExports;
        try {
          remoteEntryExports = await getRemoteEntry({
            origin: this.host,
            remoteInfo: this.remoteInfo,
            remoteEntryExports: this.remoteEntryExports
          });
        } catch (err) {
          const uniqueKey = getRemoteEntryUniqueKey(this.remoteInfo);
          remoteEntryExports = await this.host.loaderHook.lifecycle.loadEntryError.emit({
            getRemoteEntry,
            origin: this.host,
            remoteInfo: this.remoteInfo,
            remoteEntryExports: this.remoteEntryExports,
            globalLoading,
            uniqueKey
          });
        }
        assert2(remoteEntryExports, `remoteEntryExports is undefined 
 ${sdk.safeToString(this.remoteInfo)}`);
        this.remoteEntryExports = remoteEntryExports;
        return this.remoteEntryExports;
      }
      // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
      async get(id, expose, options, remoteSnapshot) {
        const { loadFactory = true } = options || {
          loadFactory: true
        };
        const remoteEntryExports = await this.getEntry();
        if (!this.inited) {
          const localShareScopeMap = this.host.shareScopeMap;
          const shareScopeKeys = Array.isArray(this.remoteInfo.shareScope) ? this.remoteInfo.shareScope : [
            this.remoteInfo.shareScope
          ];
          if (!shareScopeKeys.length) {
            shareScopeKeys.push("default");
          }
          shareScopeKeys.forEach((shareScopeKey) => {
            if (!localShareScopeMap[shareScopeKey]) {
              localShareScopeMap[shareScopeKey] = {};
            }
          });
          const shareScope = localShareScopeMap[shareScopeKeys[0]];
          const initScope = [];
          const remoteEntryInitOptions = {
            version: this.remoteInfo.version || "",
            shareScopeKeys: Array.isArray(this.remoteInfo.shareScope) ? shareScopeKeys : this.remoteInfo.shareScope || "default"
          };
          Object.defineProperty(remoteEntryInitOptions, "shareScopeMap", {
            value: localShareScopeMap,
            // remoteEntryInitOptions will be traversed and assigned during container init, ,so this attribute is not allowed to be traversed
            enumerable: false
          });
          const initContainerOptions = await this.host.hooks.lifecycle.beforeInitContainer.emit({
            shareScope,
            // @ts-ignore shareScopeMap will be set by Object.defineProperty
            remoteEntryInitOptions,
            initScope,
            remoteInfo: this.remoteInfo,
            origin: this.host
          });
          if (typeof (remoteEntryExports == null ? void 0 : remoteEntryExports.init) === "undefined") {
            error2(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_002, errorCodes.runtimeDescMap, {
              hostName: this.host.name,
              remoteName: this.remoteInfo.name,
              remoteEntryUrl: this.remoteInfo.entry,
              remoteEntryKey: this.remoteInfo.entryGlobalName
            }));
          }
          await remoteEntryExports.init(initContainerOptions.shareScope, initContainerOptions.initScope, initContainerOptions.remoteEntryInitOptions);
          await this.host.hooks.lifecycle.initContainer.emit(polyfills2._extends({}, initContainerOptions, {
            id,
            remoteSnapshot,
            remoteEntryExports
          }));
        }
        this.lib = remoteEntryExports;
        this.inited = true;
        let moduleFactory;
        moduleFactory = await this.host.loaderHook.lifecycle.getModuleFactory.emit({
          remoteEntryExports,
          expose,
          moduleInfo: this.remoteInfo
        });
        if (!moduleFactory) {
          moduleFactory = await remoteEntryExports.get(expose);
        }
        assert2(moduleFactory, `${getFMId(this.remoteInfo)} remote don't export ${expose}.`);
        const symbolName = processModuleAlias(this.remoteInfo.name, expose);
        const wrapModuleFactory = this.wraperFactory(moduleFactory, symbolName);
        if (!loadFactory) {
          return wrapModuleFactory;
        }
        const exposeContent = await wrapModuleFactory();
        return exposeContent;
      }
      wraperFactory(moduleFactory, id) {
        function defineModuleId(res2, id2) {
          if (res2 && typeof res2 === "object" && Object.isExtensible(res2) && !Object.getOwnPropertyDescriptor(res2, Symbol.for("mf_module_id"))) {
            Object.defineProperty(res2, Symbol.for("mf_module_id"), {
              value: id2,
              enumerable: false
            });
          }
        }
        if (moduleFactory instanceof Promise) {
          return async () => {
            const res2 = await moduleFactory();
            defineModuleId(res2, id);
            return res2;
          };
        } else {
          return () => {
            const res2 = moduleFactory();
            defineModuleId(res2, id);
            return res2;
          };
        }
      }
      constructor({ remoteInfo, host }) {
        this.inited = false;
        this.lib = void 0;
        this.remoteInfo = remoteInfo;
        this.host = host;
      }
    };
    var SyncHook = class {
      on(fn) {
        if (typeof fn === "function") {
          this.listeners.add(fn);
        }
      }
      once(fn) {
        const self = this;
        this.on(function wrapper(...args) {
          self.remove(wrapper);
          return fn.apply(null, args);
        });
      }
      emit(...data2) {
        let result;
        if (this.listeners.size > 0) {
          this.listeners.forEach((fn) => {
            result = fn(...data2);
          });
        }
        return result;
      }
      remove(fn) {
        this.listeners.delete(fn);
      }
      removeAll() {
        this.listeners.clear();
      }
      constructor(type) {
        this.type = "";
        this.listeners = /* @__PURE__ */ new Set();
        if (type) {
          this.type = type;
        }
      }
    };
    var AsyncHook = class extends SyncHook {
      emit(...data2) {
        let result;
        const ls = Array.from(this.listeners);
        if (ls.length > 0) {
          let i = 0;
          const call = (prev) => {
            if (prev === false) {
              return false;
            } else if (i < ls.length) {
              return Promise.resolve(ls[i++].apply(null, data2)).then(call);
            } else {
              return prev;
            }
          };
          result = call();
        }
        return Promise.resolve(result);
      }
    };
    function checkReturnData(originalData, returnedData) {
      if (!isObject(returnedData)) {
        return false;
      }
      if (originalData !== returnedData) {
        for (const key in originalData) {
          if (!(key in returnedData)) {
            return false;
          }
        }
      }
      return true;
    }
    var SyncWaterfallHook = class extends SyncHook {
      emit(data2) {
        if (!isObject(data2)) {
          error2(`The data for the "${this.type}" hook should be an object.`);
        }
        for (const fn of this.listeners) {
          try {
            const tempData = fn(data2);
            if (checkReturnData(data2, tempData)) {
              data2 = tempData;
            } else {
              this.onerror(`A plugin returned an unacceptable value for the "${this.type}" type.`);
              break;
            }
          } catch (e) {
            warn2(e);
            this.onerror(e);
          }
        }
        return data2;
      }
      constructor(type) {
        super();
        this.onerror = error2;
        this.type = type;
      }
    };
    var AsyncWaterfallHook = class extends SyncHook {
      emit(data2) {
        if (!isObject(data2)) {
          error2(`The response data for the "${this.type}" hook must be an object.`);
        }
        const ls = Array.from(this.listeners);
        if (ls.length > 0) {
          let i = 0;
          const processError = (e) => {
            warn2(e);
            this.onerror(e);
            return data2;
          };
          const call = (prevData) => {
            if (checkReturnData(data2, prevData)) {
              data2 = prevData;
              if (i < ls.length) {
                try {
                  return Promise.resolve(ls[i++](data2)).then(call, processError);
                } catch (e) {
                  return processError(e);
                }
              }
            } else {
              this.onerror(`A plugin returned an incorrect value for the "${this.type}" type.`);
            }
            return data2;
          };
          return Promise.resolve(call(data2));
        }
        return Promise.resolve(data2);
      }
      constructor(type) {
        super();
        this.onerror = error2;
        this.type = type;
      }
    };
    var PluginSystem = class {
      applyPlugin(plugin, instance) {
        assert2(isPlainObject(plugin), "Plugin configuration is invalid.");
        const pluginName = plugin.name;
        assert2(pluginName, "A name must be provided by the plugin.");
        if (!this.registerPlugins[pluginName]) {
          this.registerPlugins[pluginName] = plugin;
          plugin.apply == null ? void 0 : plugin.apply.call(plugin, instance);
          Object.keys(this.lifecycle).forEach((key) => {
            const pluginLife = plugin[key];
            if (pluginLife) {
              this.lifecycle[key].on(pluginLife);
            }
          });
        }
      }
      removePlugin(pluginName) {
        assert2(pluginName, "A name is required.");
        const plugin = this.registerPlugins[pluginName];
        assert2(plugin, `The plugin "${pluginName}" is not registered.`);
        Object.keys(plugin).forEach((key) => {
          if (key !== "name") {
            this.lifecycle[key].remove(plugin[key]);
          }
        });
      }
      constructor(lifecycle) {
        this.registerPlugins = {};
        this.lifecycle = lifecycle;
        this.lifecycleKeys = Object.keys(lifecycle);
      }
    };
    function assignRemoteInfo(remoteInfo, remoteSnapshot) {
      const remoteEntryInfo = getRemoteEntryInfoFromSnapshot(remoteSnapshot);
      if (!remoteEntryInfo.url) {
        error2(`The attribute remoteEntry of ${remoteInfo.name} must not be undefined.`);
      }
      let entryUrl = sdk.getResourceUrl(remoteSnapshot, remoteEntryInfo.url);
      if (!sdk.isBrowserEnv() && !entryUrl.startsWith("http")) {
        entryUrl = `https:${entryUrl}`;
      }
      remoteInfo.type = remoteEntryInfo.type;
      remoteInfo.entryGlobalName = remoteEntryInfo.globalName;
      remoteInfo.entry = entryUrl;
      remoteInfo.version = remoteSnapshot.version;
      remoteInfo.buildVersion = remoteSnapshot.buildVersion;
    }
    function snapshotPlugin() {
      return {
        name: "snapshot-plugin",
        async afterResolve(args) {
          const { remote, pkgNameOrAlias, expose, origin, remoteInfo, id } = args;
          if (!isRemoteInfoWithEntry(remote) || !isPureRemoteEntry(remote)) {
            const { remoteSnapshot, globalSnapshot } = await origin.snapshotHandler.loadRemoteSnapshotInfo({
              moduleInfo: remote,
              id
            });
            assignRemoteInfo(remoteInfo, remoteSnapshot);
            const preloadOptions = {
              remote,
              preloadConfig: {
                nameOrAlias: pkgNameOrAlias,
                exposes: [
                  expose
                ],
                resourceCategory: "sync",
                share: false,
                depsRemote: false
              }
            };
            const assets = await origin.remoteHandler.hooks.lifecycle.generatePreloadAssets.emit({
              origin,
              preloadOptions,
              remoteInfo,
              remote,
              remoteSnapshot,
              globalSnapshot
            });
            if (assets) {
              preloadAssets(remoteInfo, origin, assets, false);
            }
            return polyfills2._extends({}, args, {
              remoteSnapshot
            });
          }
          return args;
        }
      };
    }
    function splitId(id) {
      const splitInfo = id.split(":");
      if (splitInfo.length === 1) {
        return {
          name: splitInfo[0],
          version: void 0
        };
      } else if (splitInfo.length === 2) {
        return {
          name: splitInfo[0],
          version: splitInfo[1]
        };
      } else {
        return {
          name: splitInfo[1],
          version: splitInfo[2]
        };
      }
    }
    function traverseModuleInfo(globalSnapshot, remoteInfo, traverse, isRoot, memo = {}, remoteSnapshot) {
      const id = getFMId(remoteInfo);
      const { value: snapshotValue } = getInfoWithoutType(globalSnapshot, id);
      const effectiveRemoteSnapshot = remoteSnapshot || snapshotValue;
      if (effectiveRemoteSnapshot && !sdk.isManifestProvider(effectiveRemoteSnapshot)) {
        traverse(effectiveRemoteSnapshot, remoteInfo, isRoot);
        if (effectiveRemoteSnapshot.remotesInfo) {
          const remoteKeys = Object.keys(effectiveRemoteSnapshot.remotesInfo);
          for (const key of remoteKeys) {
            if (memo[key]) {
              continue;
            }
            memo[key] = true;
            const subRemoteInfo = splitId(key);
            const remoteValue = effectiveRemoteSnapshot.remotesInfo[key];
            traverseModuleInfo(globalSnapshot, {
              name: subRemoteInfo.name,
              version: remoteValue.matchedVersion
            }, traverse, false, memo, void 0);
          }
        }
      }
    }
    var isExisted = (type, url2) => {
      return document.querySelector(`${type}[${type === "link" ? "href" : "src"}="${url2}"]`);
    };
    function generatePreloadAssets(origin, preloadOptions, remote, globalSnapshot, remoteSnapshot) {
      const cssAssets = [];
      const jsAssets = [];
      const entryAssets = [];
      const loadedSharedJsAssets = /* @__PURE__ */ new Set();
      const loadedSharedCssAssets = /* @__PURE__ */ new Set();
      const { options } = origin;
      const { preloadConfig: rootPreloadConfig } = preloadOptions;
      const { depsRemote } = rootPreloadConfig;
      const memo = {};
      traverseModuleInfo(globalSnapshot, remote, (moduleInfoSnapshot, remoteInfo, isRoot) => {
        let preloadConfig;
        if (isRoot) {
          preloadConfig = rootPreloadConfig;
        } else {
          if (Array.isArray(depsRemote)) {
            const findPreloadConfig = depsRemote.find((remoteConfig) => {
              if (remoteConfig.nameOrAlias === remoteInfo.name || remoteConfig.nameOrAlias === remoteInfo.alias) {
                return true;
              }
              return false;
            });
            if (!findPreloadConfig) {
              return;
            }
            preloadConfig = defaultPreloadArgs(findPreloadConfig);
          } else if (depsRemote === true) {
            preloadConfig = rootPreloadConfig;
          } else {
            return;
          }
        }
        const remoteEntryUrl = sdk.getResourceUrl(moduleInfoSnapshot, getRemoteEntryInfoFromSnapshot(moduleInfoSnapshot).url);
        if (remoteEntryUrl) {
          entryAssets.push({
            name: remoteInfo.name,
            moduleInfo: {
              name: remoteInfo.name,
              entry: remoteEntryUrl,
              type: "remoteEntryType" in moduleInfoSnapshot ? moduleInfoSnapshot.remoteEntryType : "global",
              entryGlobalName: "globalName" in moduleInfoSnapshot ? moduleInfoSnapshot.globalName : remoteInfo.name,
              shareScope: "",
              version: "version" in moduleInfoSnapshot ? moduleInfoSnapshot.version : void 0
            },
            url: remoteEntryUrl
          });
        }
        let moduleAssetsInfo = "modules" in moduleInfoSnapshot ? moduleInfoSnapshot.modules : [];
        const normalizedPreloadExposes = normalizePreloadExposes(preloadConfig.exposes);
        if (normalizedPreloadExposes.length && "modules" in moduleInfoSnapshot) {
          var _moduleInfoSnapshot_modules;
          moduleAssetsInfo = moduleInfoSnapshot == null ? void 0 : (_moduleInfoSnapshot_modules = moduleInfoSnapshot.modules) == null ? void 0 : _moduleInfoSnapshot_modules.reduce((assets, moduleAssetInfo) => {
            if ((normalizedPreloadExposes == null ? void 0 : normalizedPreloadExposes.indexOf(moduleAssetInfo.moduleName)) !== -1) {
              assets.push(moduleAssetInfo);
            }
            return assets;
          }, []);
        }
        function handleAssets(assets) {
          const assetsRes = assets.map((asset) => sdk.getResourceUrl(moduleInfoSnapshot, asset));
          if (preloadConfig.filter) {
            return assetsRes.filter(preloadConfig.filter);
          }
          return assetsRes;
        }
        if (moduleAssetsInfo) {
          const assetsLength = moduleAssetsInfo.length;
          for (let index2 = 0; index2 < assetsLength; index2++) {
            const assetsInfo = moduleAssetsInfo[index2];
            const exposeFullPath = `${remoteInfo.name}/${assetsInfo.moduleName}`;
            origin.remoteHandler.hooks.lifecycle.handlePreloadModule.emit({
              id: assetsInfo.moduleName === "." ? remoteInfo.name : exposeFullPath,
              name: remoteInfo.name,
              remoteSnapshot: moduleInfoSnapshot,
              preloadConfig,
              remote: remoteInfo,
              origin
            });
            const preloaded = getPreloaded(exposeFullPath);
            if (preloaded) {
              continue;
            }
            if (preloadConfig.resourceCategory === "all") {
              cssAssets.push(...handleAssets(assetsInfo.assets.css.async));
              cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));
              jsAssets.push(...handleAssets(assetsInfo.assets.js.async));
              jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));
            } else if (preloadConfig.resourceCategory = "sync") {
              cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));
              jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));
            }
            setPreloaded(exposeFullPath);
          }
        }
      }, true, memo, remoteSnapshot);
      if (remoteSnapshot.shared && remoteSnapshot.shared.length > 0) {
        const collectSharedAssets = (shareInfo, snapshotShared) => {
          const registeredShared = getRegisteredShare(origin.shareScopeMap, snapshotShared.sharedName, shareInfo, origin.sharedHandler.hooks.lifecycle.resolveShare);
          if (registeredShared && typeof registeredShared.lib === "function") {
            snapshotShared.assets.js.sync.forEach((asset) => {
              loadedSharedJsAssets.add(asset);
            });
            snapshotShared.assets.css.sync.forEach((asset) => {
              loadedSharedCssAssets.add(asset);
            });
          }
        };
        remoteSnapshot.shared.forEach((shared) => {
          var _options_shared;
          const shareInfos = (_options_shared = options.shared) == null ? void 0 : _options_shared[shared.sharedName];
          if (!shareInfos) {
            return;
          }
          const sharedOptions = shared.version ? shareInfos.find((s) => s.version === shared.version) : shareInfos;
          if (!sharedOptions) {
            return;
          }
          const arrayShareInfo = arrayOptions(sharedOptions);
          arrayShareInfo.forEach((s) => {
            collectSharedAssets(s, shared);
          });
        });
      }
      const needPreloadJsAssets = jsAssets.filter((asset) => !loadedSharedJsAssets.has(asset) && !isExisted("script", asset));
      const needPreloadCssAssets = cssAssets.filter((asset) => !loadedSharedCssAssets.has(asset) && !isExisted("link", asset));
      return {
        cssAssets: needPreloadCssAssets,
        jsAssetsWithoutEntry: needPreloadJsAssets,
        entryAssets: entryAssets.filter((entry) => !isExisted("script", entry.url))
      };
    }
    var generatePreloadAssetsPlugin = function() {
      return {
        name: "generate-preload-assets-plugin",
        async generatePreloadAssets(args) {
          const { origin, preloadOptions, remoteInfo, remote, globalSnapshot, remoteSnapshot } = args;
          if (!sdk.isBrowserEnv()) {
            return {
              cssAssets: [],
              jsAssetsWithoutEntry: [],
              entryAssets: []
            };
          }
          if (isRemoteInfoWithEntry(remote) && isPureRemoteEntry(remote)) {
            return {
              cssAssets: [],
              jsAssetsWithoutEntry: [],
              entryAssets: [
                {
                  name: remote.name,
                  url: remote.entry,
                  moduleInfo: {
                    name: remoteInfo.name,
                    entry: remote.entry,
                    type: remoteInfo.type || "global",
                    entryGlobalName: "",
                    shareScope: ""
                  }
                }
              ]
            };
          }
          assignRemoteInfo(remoteInfo, remoteSnapshot);
          const assets = generatePreloadAssets(origin, preloadOptions, remoteInfo, globalSnapshot, remoteSnapshot);
          return assets;
        }
      };
    };
    function getGlobalRemoteInfo(moduleInfo, origin) {
      const hostGlobalSnapshot = getGlobalSnapshotInfoByModuleInfo({
        name: origin.name,
        version: origin.options.version
      });
      const globalRemoteInfo = hostGlobalSnapshot && "remotesInfo" in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && getInfoWithoutType(hostGlobalSnapshot.remotesInfo, moduleInfo.name).value;
      if (globalRemoteInfo && globalRemoteInfo.matchedVersion) {
        return {
          hostGlobalSnapshot,
          globalSnapshot: getGlobalSnapshot(),
          remoteSnapshot: getGlobalSnapshotInfoByModuleInfo({
            name: moduleInfo.name,
            version: globalRemoteInfo.matchedVersion
          })
        };
      }
      return {
        hostGlobalSnapshot: void 0,
        globalSnapshot: getGlobalSnapshot(),
        remoteSnapshot: getGlobalSnapshotInfoByModuleInfo({
          name: moduleInfo.name,
          version: "version" in moduleInfo ? moduleInfo.version : void 0
        })
      };
    }
    var SnapshotHandler = class {
      // eslint-disable-next-line max-lines-per-function
      async loadRemoteSnapshotInfo({ moduleInfo, id, expose }) {
        const { options } = this.HostInstance;
        await this.hooks.lifecycle.beforeLoadRemoteSnapshot.emit({
          options,
          moduleInfo
        });
        let hostSnapshot = getGlobalSnapshotInfoByModuleInfo({
          name: this.HostInstance.options.name,
          version: this.HostInstance.options.version
        });
        if (!hostSnapshot) {
          hostSnapshot = {
            version: this.HostInstance.options.version || "",
            remoteEntry: "",
            remotesInfo: {}
          };
          addGlobalSnapshot({
            [this.HostInstance.options.name]: hostSnapshot
          });
        }
        if (hostSnapshot && "remotesInfo" in hostSnapshot && !getInfoWithoutType(hostSnapshot.remotesInfo, moduleInfo.name).value) {
          if ("version" in moduleInfo || "entry" in moduleInfo) {
            hostSnapshot.remotesInfo = polyfills2._extends({}, hostSnapshot == null ? void 0 : hostSnapshot.remotesInfo, {
              [moduleInfo.name]: {
                matchedVersion: "version" in moduleInfo ? moduleInfo.version : moduleInfo.entry
              }
            });
          }
        }
        const { hostGlobalSnapshot, remoteSnapshot, globalSnapshot } = this.getGlobalRemoteInfo(moduleInfo);
        const { remoteSnapshot: globalRemoteSnapshot, globalSnapshot: globalSnapshotRes } = await this.hooks.lifecycle.loadSnapshot.emit({
          options,
          moduleInfo,
          hostGlobalSnapshot,
          remoteSnapshot,
          globalSnapshot
        });
        let mSnapshot;
        let gSnapshot;
        if (globalRemoteSnapshot) {
          if (sdk.isManifestProvider(globalRemoteSnapshot)) {
            const remoteEntry = sdk.isBrowserEnv() ? globalRemoteSnapshot.remoteEntry : globalRemoteSnapshot.ssrRemoteEntry || globalRemoteSnapshot.remoteEntry || "";
            const moduleSnapshot = await this.getManifestJson(remoteEntry, moduleInfo, {});
            const globalSnapshotRes2 = setGlobalSnapshotInfoByModuleInfo(polyfills2._extends({}, moduleInfo, {
              // The global remote may be overridden
              // Therefore, set the snapshot key to the global address of the actual request
              entry: remoteEntry
            }), moduleSnapshot);
            mSnapshot = moduleSnapshot;
            gSnapshot = globalSnapshotRes2;
          } else {
            const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({
              options: this.HostInstance.options,
              moduleInfo,
              remoteSnapshot: globalRemoteSnapshot,
              from: "global"
            });
            mSnapshot = remoteSnapshotRes;
            gSnapshot = globalSnapshotRes;
          }
        } else {
          if (isRemoteInfoWithEntry(moduleInfo)) {
            const moduleSnapshot = await this.getManifestJson(moduleInfo.entry, moduleInfo, {});
            const globalSnapshotRes2 = setGlobalSnapshotInfoByModuleInfo(moduleInfo, moduleSnapshot);
            const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({
              options: this.HostInstance.options,
              moduleInfo,
              remoteSnapshot: moduleSnapshot,
              from: "global"
            });
            mSnapshot = remoteSnapshotRes;
            gSnapshot = globalSnapshotRes2;
          } else {
            error2(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_007, errorCodes.runtimeDescMap, {
              hostName: moduleInfo.name,
              hostVersion: moduleInfo.version,
              globalSnapshot: JSON.stringify(globalSnapshotRes)
            }));
          }
        }
        await this.hooks.lifecycle.afterLoadSnapshot.emit({
          id,
          host: this.HostInstance,
          options,
          moduleInfo,
          remoteSnapshot: mSnapshot
        });
        return {
          remoteSnapshot: mSnapshot,
          globalSnapshot: gSnapshot
        };
      }
      getGlobalRemoteInfo(moduleInfo) {
        return getGlobalRemoteInfo(moduleInfo, this.HostInstance);
      }
      async getManifestJson(manifestUrl, moduleInfo, extraOptions) {
        const getManifest = async () => {
          let manifestJson = this.manifestCache.get(manifestUrl);
          if (manifestJson) {
            return manifestJson;
          }
          try {
            let res2 = await this.loaderHook.lifecycle.fetch.emit(manifestUrl, {});
            if (!res2 || !(res2 instanceof Response)) {
              res2 = await fetch(manifestUrl, {});
            }
            manifestJson = await res2.json();
          } catch (err) {
            manifestJson = await this.HostInstance.remoteHandler.hooks.lifecycle.errorLoadRemote.emit({
              id: manifestUrl,
              error: err,
              from: "runtime",
              lifecycle: "afterResolve",
              origin: this.HostInstance
            });
            if (!manifestJson) {
              delete this.manifestLoading[manifestUrl];
              error2(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_003, errorCodes.runtimeDescMap, {
                manifestUrl,
                moduleName: moduleInfo.name,
                hostName: this.HostInstance.options.name
              }, `${err}`));
            }
          }
          assert2(manifestJson.metaData && manifestJson.exposes && manifestJson.shared, `${manifestUrl} is not a federation manifest`);
          this.manifestCache.set(manifestUrl, manifestJson);
          return manifestJson;
        };
        const asyncLoadProcess = async () => {
          const manifestJson = await getManifest();
          const remoteSnapshot = sdk.generateSnapshotFromManifest(manifestJson, {
            version: manifestUrl
          });
          const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({
            options: this.HostInstance.options,
            moduleInfo,
            manifestJson,
            remoteSnapshot,
            manifestUrl,
            from: "manifest"
          });
          return remoteSnapshotRes;
        };
        if (!this.manifestLoading[manifestUrl]) {
          this.manifestLoading[manifestUrl] = asyncLoadProcess().then((res2) => res2);
        }
        return this.manifestLoading[manifestUrl];
      }
      constructor(HostInstance) {
        this.loadingHostSnapshot = null;
        this.manifestCache = /* @__PURE__ */ new Map();
        this.hooks = new PluginSystem({
          beforeLoadRemoteSnapshot: new AsyncHook("beforeLoadRemoteSnapshot"),
          loadSnapshot: new AsyncWaterfallHook("loadGlobalSnapshot"),
          loadRemoteSnapshot: new AsyncWaterfallHook("loadRemoteSnapshot"),
          afterLoadSnapshot: new AsyncWaterfallHook("afterLoadSnapshot")
        });
        this.manifestLoading = Global.__FEDERATION__.__MANIFEST_LOADING__;
        this.HostInstance = HostInstance;
        this.loaderHook = HostInstance.loaderHook;
      }
    };
    var SharedHandler = class {
      // register shared in shareScopeMap
      registerShared(globalOptions, userOptions) {
        const { shareInfos, shared } = formatShareConfigs(globalOptions, userOptions);
        const sharedKeys = Object.keys(shareInfos);
        sharedKeys.forEach((sharedKey) => {
          const sharedVals = shareInfos[sharedKey];
          sharedVals.forEach((sharedVal) => {
            const registeredShared = getRegisteredShare(this.shareScopeMap, sharedKey, sharedVal, this.hooks.lifecycle.resolveShare);
            if (!registeredShared && sharedVal && sharedVal.lib) {
              this.setShared({
                pkgName: sharedKey,
                lib: sharedVal.lib,
                get: sharedVal.get,
                loaded: true,
                shared: sharedVal,
                from: userOptions.name
              });
            }
          });
        });
        return {
          shareInfos,
          shared
        };
      }
      async loadShare(pkgName, extraOptions) {
        const { host } = this;
        const shareInfo = getTargetSharedOptions({
          pkgName,
          extraOptions,
          shareInfos: host.options.shared
        });
        if (shareInfo == null ? void 0 : shareInfo.scope) {
          await Promise.all(shareInfo.scope.map(async (shareScope) => {
            await Promise.all(this.initializeSharing(shareScope, {
              strategy: shareInfo.strategy
            }));
            return;
          }));
        }
        const loadShareRes = await this.hooks.lifecycle.beforeLoadShare.emit({
          pkgName,
          shareInfo,
          shared: host.options.shared,
          origin: host
        });
        const { shareInfo: shareInfoRes } = loadShareRes;
        assert2(shareInfoRes, `Cannot find ${pkgName} Share in the ${host.options.name}. Please ensure that the ${pkgName} Share parameters have been injected`);
        const registeredShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);
        const addUseIn = (shared) => {
          if (!shared.useIn) {
            shared.useIn = [];
          }
          addUniqueItem(shared.useIn, host.options.name);
        };
        if (registeredShared && registeredShared.lib) {
          addUseIn(registeredShared);
          return registeredShared.lib;
        } else if (registeredShared && registeredShared.loading && !registeredShared.loaded) {
          const factory = await registeredShared.loading;
          registeredShared.loaded = true;
          if (!registeredShared.lib) {
            registeredShared.lib = factory;
          }
          addUseIn(registeredShared);
          return factory;
        } else if (registeredShared) {
          const asyncLoadProcess = async () => {
            const factory = await registeredShared.get();
            shareInfoRes.lib = factory;
            shareInfoRes.loaded = true;
            addUseIn(shareInfoRes);
            const gShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);
            if (gShared) {
              gShared.lib = factory;
              gShared.loaded = true;
              addUseIn(gShared);
            }
            return factory;
          };
          const loading = asyncLoadProcess();
          this.setShared({
            pkgName,
            loaded: false,
            shared: registeredShared,
            from: host.options.name,
            lib: null,
            loading
          });
          return loading;
        } else {
          if (extraOptions == null ? void 0 : extraOptions.customShareInfo) {
            return false;
          }
          const asyncLoadProcess = async () => {
            const factory = await shareInfoRes.get();
            shareInfoRes.lib = factory;
            shareInfoRes.loaded = true;
            addUseIn(shareInfoRes);
            const gShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);
            if (gShared) {
              gShared.lib = factory;
              gShared.loaded = true;
            }
            return factory;
          };
          const loading = asyncLoadProcess();
          this.setShared({
            pkgName,
            loaded: false,
            shared: shareInfoRes,
            from: host.options.name,
            lib: null,
            loading
          });
          return loading;
        }
      }
      /**
       * This function initializes the sharing sequence (executed only once per share scope).
       * It accepts one argument, the name of the share scope.
       * If the share scope does not exist, it creates one.
       */
      // eslint-disable-next-line @typescript-eslint/member-ordering
      initializeSharing(shareScopeName = DEFAULT_SCOPE, extraOptions) {
        const { host } = this;
        const from = extraOptions == null ? void 0 : extraOptions.from;
        const strategy = extraOptions == null ? void 0 : extraOptions.strategy;
        let initScope = extraOptions == null ? void 0 : extraOptions.initScope;
        const promises = [];
        if (from !== "build") {
          const { initTokens } = this;
          if (!initScope) initScope = [];
          let initToken = initTokens[shareScopeName];
          if (!initToken) initToken = initTokens[shareScopeName] = {
            from: this.host.name
          };
          if (initScope.indexOf(initToken) >= 0) return promises;
          initScope.push(initToken);
        }
        const shareScope = this.shareScopeMap;
        const hostName = host.options.name;
        if (!shareScope[shareScopeName]) {
          shareScope[shareScopeName] = {};
        }
        const scope = shareScope[shareScopeName];
        const register = (name, shared) => {
          var _activeVersion_shareConfig;
          const { version, eager } = shared;
          scope[name] = scope[name] || {};
          const versions = scope[name];
          const activeVersion = versions[version];
          const activeVersionEager = Boolean(activeVersion && (activeVersion.eager || ((_activeVersion_shareConfig = activeVersion.shareConfig) == null ? void 0 : _activeVersion_shareConfig.eager)));
          if (!activeVersion || activeVersion.strategy !== "loaded-first" && !activeVersion.loaded && (Boolean(!eager) !== !activeVersionEager ? eager : hostName > activeVersion.from)) {
            versions[version] = shared;
          }
        };
        const initFn = (mod) => mod && mod.init && mod.init(shareScope[shareScopeName], initScope);
        const initRemoteModule = async (key) => {
          const { module: module3 } = await host.remoteHandler.getRemoteModuleAndOptions({
            id: key
          });
          if (module3.getEntry) {
            let remoteEntryExports;
            try {
              remoteEntryExports = await module3.getEntry();
            } catch (error3) {
              remoteEntryExports = await host.remoteHandler.hooks.lifecycle.errorLoadRemote.emit({
                id: key,
                error: error3,
                from: "runtime",
                lifecycle: "beforeLoadShare",
                origin: host
              });
            }
            if (!module3.inited) {
              await initFn(remoteEntryExports);
              module3.inited = true;
            }
          }
        };
        Object.keys(host.options.shared).forEach((shareName) => {
          const sharedArr = host.options.shared[shareName];
          sharedArr.forEach((shared) => {
            if (shared.scope.includes(shareScopeName)) {
              register(shareName, shared);
            }
          });
        });
        if (host.options.shareStrategy === "version-first" || strategy === "version-first") {
          host.options.remotes.forEach((remote) => {
            if (remote.shareScope === shareScopeName) {
              promises.push(initRemoteModule(remote.name));
            }
          });
        }
        return promises;
      }
      // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.
      // 1. If the loaded shared already exists globally, then it will be reused
      // 2. If lib exists in local shared, it will be used directly
      // 3. If the local get returns something other than Promise, then it will be used directly
      loadShareSync(pkgName, extraOptions) {
        const { host } = this;
        const shareInfo = getTargetSharedOptions({
          pkgName,
          extraOptions,
          shareInfos: host.options.shared
        });
        if (shareInfo == null ? void 0 : shareInfo.scope) {
          shareInfo.scope.forEach((shareScope) => {
            this.initializeSharing(shareScope, {
              strategy: shareInfo.strategy
            });
          });
        }
        const registeredShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfo, this.hooks.lifecycle.resolveShare);
        const addUseIn = (shared) => {
          if (!shared.useIn) {
            shared.useIn = [];
          }
          addUniqueItem(shared.useIn, host.options.name);
        };
        if (registeredShared) {
          if (typeof registeredShared.lib === "function") {
            addUseIn(registeredShared);
            if (!registeredShared.loaded) {
              registeredShared.loaded = true;
              if (registeredShared.from === host.options.name) {
                shareInfo.loaded = true;
              }
            }
            return registeredShared.lib;
          }
          if (typeof registeredShared.get === "function") {
            const module3 = registeredShared.get();
            if (!(module3 instanceof Promise)) {
              addUseIn(registeredShared);
              this.setShared({
                pkgName,
                loaded: true,
                from: host.options.name,
                lib: module3,
                shared: registeredShared
              });
              return module3;
            }
          }
        }
        if (shareInfo.lib) {
          if (!shareInfo.loaded) {
            shareInfo.loaded = true;
          }
          return shareInfo.lib;
        }
        if (shareInfo.get) {
          const module3 = shareInfo.get();
          if (module3 instanceof Promise) {
            const errorCode = (extraOptions == null ? void 0 : extraOptions.from) === "build" ? errorCodes.RUNTIME_005 : errorCodes.RUNTIME_006;
            throw new Error(errorCodes.getShortErrorMsg(errorCode, errorCodes.runtimeDescMap, {
              hostName: host.options.name,
              sharedPkgName: pkgName
            }));
          }
          shareInfo.lib = module3;
          this.setShared({
            pkgName,
            loaded: true,
            from: host.options.name,
            lib: shareInfo.lib,
            shared: shareInfo
          });
          return shareInfo.lib;
        }
        throw new Error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_006, errorCodes.runtimeDescMap, {
          hostName: host.options.name,
          sharedPkgName: pkgName
        }));
      }
      initShareScopeMap(scopeName, shareScope, extraOptions = {}) {
        const { host } = this;
        this.shareScopeMap[scopeName] = shareScope;
        this.hooks.lifecycle.initContainerShareScopeMap.emit({
          shareScope,
          options: host.options,
          origin: host,
          scopeName,
          hostShareScopeMap: extraOptions.hostShareScopeMap
        });
      }
      setShared({ pkgName, shared, from, lib, loading, loaded, get }) {
        const { version, scope = "default" } = shared, shareInfo = polyfills2._object_without_properties_loose(shared, [
          "version",
          "scope"
        ]);
        const scopes = Array.isArray(scope) ? scope : [
          scope
        ];
        scopes.forEach((sc) => {
          if (!this.shareScopeMap[sc]) {
            this.shareScopeMap[sc] = {};
          }
          if (!this.shareScopeMap[sc][pkgName]) {
            this.shareScopeMap[sc][pkgName] = {};
          }
          if (!this.shareScopeMap[sc][pkgName][version]) {
            this.shareScopeMap[sc][pkgName][version] = polyfills2._extends({
              version,
              scope: [
                "default"
              ]
            }, shareInfo, {
              lib,
              loaded,
              loading
            });
            if (get) {
              this.shareScopeMap[sc][pkgName][version].get = get;
            }
            return;
          }
          const registeredShared = this.shareScopeMap[sc][pkgName][version];
          if (loading && !registeredShared.loading) {
            registeredShared.loading = loading;
          }
        });
      }
      _setGlobalShareScopeMap(hostOptions) {
        const globalShareScopeMap = getGlobalShareScope();
        const identifier = hostOptions.id || hostOptions.name;
        if (identifier && !globalShareScopeMap[identifier]) {
          globalShareScopeMap[identifier] = this.shareScopeMap;
        }
      }
      constructor(host) {
        this.hooks = new PluginSystem({
          afterResolve: new AsyncWaterfallHook("afterResolve"),
          beforeLoadShare: new AsyncWaterfallHook("beforeLoadShare"),
          // not used yet
          loadShare: new AsyncHook(),
          resolveShare: new SyncWaterfallHook("resolveShare"),
          // maybe will change, temporarily for internal use only
          initContainerShareScopeMap: new SyncWaterfallHook("initContainerShareScopeMap")
        });
        this.host = host;
        this.shareScopeMap = {};
        this.initTokens = {};
        this._setGlobalShareScopeMap(host.options);
      }
    };
    var RemoteHandler = class {
      formatAndRegisterRemote(globalOptions, userOptions) {
        const userRemotes = userOptions.remotes || [];
        return userRemotes.reduce((res2, remote) => {
          this.registerRemote(remote, res2, {
            force: false
          });
          return res2;
        }, globalOptions.remotes);
      }
      setIdToRemoteMap(id, remoteMatchInfo) {
        const { remote, expose } = remoteMatchInfo;
        const { name, alias } = remote;
        this.idToRemoteMap[id] = {
          name: remote.name,
          expose
        };
        if (alias && id.startsWith(name)) {
          const idWithAlias = id.replace(name, alias);
          this.idToRemoteMap[idWithAlias] = {
            name: remote.name,
            expose
          };
          return;
        }
        if (alias && id.startsWith(alias)) {
          const idWithName = id.replace(alias, name);
          this.idToRemoteMap[idWithName] = {
            name: remote.name,
            expose
          };
        }
      }
      // eslint-disable-next-line max-lines-per-function
      // eslint-disable-next-line @typescript-eslint/member-ordering
      async loadRemote(id, options) {
        const { host } = this;
        try {
          const { loadFactory = true } = options || {
            loadFactory: true
          };
          const { module: module3, moduleOptions, remoteMatchInfo } = await this.getRemoteModuleAndOptions({
            id
          });
          const { pkgNameOrAlias, remote, expose, id: idRes, remoteSnapshot } = remoteMatchInfo;
          const moduleOrFactory = await module3.get(idRes, expose, options, remoteSnapshot);
          const moduleWrapper = await this.hooks.lifecycle.onLoad.emit({
            id: idRes,
            pkgNameOrAlias,
            expose,
            exposeModule: loadFactory ? moduleOrFactory : void 0,
            exposeModuleFactory: loadFactory ? void 0 : moduleOrFactory,
            remote,
            options: moduleOptions,
            moduleInstance: module3,
            origin: host
          });
          this.setIdToRemoteMap(id, remoteMatchInfo);
          if (typeof moduleWrapper === "function") {
            return moduleWrapper;
          }
          return moduleOrFactory;
        } catch (error3) {
          const { from = "runtime" } = options || {
            from: "runtime"
          };
          const failOver = await this.hooks.lifecycle.errorLoadRemote.emit({
            id,
            error: error3,
            from,
            lifecycle: "onLoad",
            origin: host
          });
          if (!failOver) {
            throw error3;
          }
          return failOver;
        }
      }
      // eslint-disable-next-line @typescript-eslint/member-ordering
      async preloadRemote(preloadOptions) {
        const { host } = this;
        await this.hooks.lifecycle.beforePreloadRemote.emit({
          preloadOps: preloadOptions,
          options: host.options,
          origin: host
        });
        const preloadOps = formatPreloadArgs(host.options.remotes, preloadOptions);
        await Promise.all(preloadOps.map(async (ops) => {
          const { remote } = ops;
          const remoteInfo = getRemoteInfo(remote);
          const { globalSnapshot, remoteSnapshot } = await host.snapshotHandler.loadRemoteSnapshotInfo({
            moduleInfo: remote
          });
          const assets = await this.hooks.lifecycle.generatePreloadAssets.emit({
            origin: host,
            preloadOptions: ops,
            remote,
            remoteInfo,
            globalSnapshot,
            remoteSnapshot
          });
          if (!assets) {
            return;
          }
          preloadAssets(remoteInfo, host, assets);
        }));
      }
      registerRemotes(remotes, options) {
        const { host } = this;
        remotes.forEach((remote) => {
          this.registerRemote(remote, host.options.remotes, {
            force: options == null ? void 0 : options.force
          });
        });
      }
      async getRemoteModuleAndOptions(options) {
        const { host } = this;
        const { id } = options;
        let loadRemoteArgs;
        try {
          loadRemoteArgs = await this.hooks.lifecycle.beforeRequest.emit({
            id,
            options: host.options,
            origin: host
          });
        } catch (error3) {
          loadRemoteArgs = await this.hooks.lifecycle.errorLoadRemote.emit({
            id,
            options: host.options,
            origin: host,
            from: "runtime",
            error: error3,
            lifecycle: "beforeRequest"
          });
          if (!loadRemoteArgs) {
            throw error3;
          }
        }
        const { id: idRes } = loadRemoteArgs;
        const remoteSplitInfo = matchRemoteWithNameAndExpose(host.options.remotes, idRes);
        assert2(remoteSplitInfo, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_004, errorCodes.runtimeDescMap, {
          hostName: host.options.name,
          requestId: idRes
        }));
        const { remote: rawRemote } = remoteSplitInfo;
        const remoteInfo = getRemoteInfo(rawRemote);
        const matchInfo = await host.sharedHandler.hooks.lifecycle.afterResolve.emit(polyfills2._extends({
          id: idRes
        }, remoteSplitInfo, {
          options: host.options,
          origin: host,
          remoteInfo
        }));
        const { remote, expose } = matchInfo;
        assert2(remote && expose, `The 'beforeRequest' hook was executed, but it failed to return the correct 'remote' and 'expose' values while loading ${idRes}.`);
        let module3 = host.moduleCache.get(remote.name);
        const moduleOptions = {
          host,
          remoteInfo
        };
        if (!module3) {
          module3 = new Module(moduleOptions);
          host.moduleCache.set(remote.name, module3);
        }
        return {
          module: module3,
          moduleOptions,
          remoteMatchInfo: matchInfo
        };
      }
      registerRemote(remote, targetRemotes, options) {
        const { host } = this;
        const normalizeRemote = () => {
          if (remote.alias) {
            const findEqual = targetRemotes.find((item) => {
              var _item_alias;
              return remote.alias && (item.name.startsWith(remote.alias) || ((_item_alias = item.alias) == null ? void 0 : _item_alias.startsWith(remote.alias)));
            });
            assert2(!findEqual, `The alias ${remote.alias} of remote ${remote.name} is not allowed to be the prefix of ${findEqual && findEqual.name} name or alias`);
          }
          if ("entry" in remote) {
            if (sdk.isBrowserEnv() && !remote.entry.startsWith("http")) {
              remote.entry = new URL(remote.entry, window.location.origin).href;
            }
          }
          if (!remote.shareScope) {
            remote.shareScope = DEFAULT_SCOPE;
          }
          if (!remote.type) {
            remote.type = DEFAULT_REMOTE_TYPE;
          }
        };
        this.hooks.lifecycle.beforeRegisterRemote.emit({
          remote,
          origin: host
        });
        const registeredRemote = targetRemotes.find((item) => item.name === remote.name);
        if (!registeredRemote) {
          normalizeRemote();
          targetRemotes.push(remote);
          this.hooks.lifecycle.registerRemote.emit({
            remote,
            origin: host
          });
        } else {
          const messages = [
            `The remote "${remote.name}" is already registered.`,
            "Please note that overriding it may cause unexpected errors."
          ];
          if (options == null ? void 0 : options.force) {
            this.removeRemote(registeredRemote);
            normalizeRemote();
            targetRemotes.push(remote);
            this.hooks.lifecycle.registerRemote.emit({
              remote,
              origin: host
            });
            sdk.warn(messages.join(" "));
          }
        }
      }
      removeRemote(remote) {
        try {
          const { host } = this;
          const { name } = remote;
          const remoteIndex = host.options.remotes.findIndex((item) => item.name === name);
          if (remoteIndex !== -1) {
            host.options.remotes.splice(remoteIndex, 1);
          }
          const loadedModule = host.moduleCache.get(remote.name);
          if (loadedModule) {
            const remoteInfo = loadedModule.remoteInfo;
            const key = remoteInfo.entryGlobalName;
            if (CurrentGlobal[key]) {
              var _Object_getOwnPropertyDescriptor;
              if ((_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(CurrentGlobal, key)) == null ? void 0 : _Object_getOwnPropertyDescriptor.configurable) {
                delete CurrentGlobal[key];
              } else {
                CurrentGlobal[key] = void 0;
              }
            }
            const remoteEntryUniqueKey = getRemoteEntryUniqueKey(loadedModule.remoteInfo);
            if (globalLoading[remoteEntryUniqueKey]) {
              delete globalLoading[remoteEntryUniqueKey];
            }
            host.snapshotHandler.manifestCache.delete(remoteInfo.entry);
            let remoteInsId = remoteInfo.buildVersion ? sdk.composeKeyWithSeparator(remoteInfo.name, remoteInfo.buildVersion) : remoteInfo.name;
            const remoteInsIndex = CurrentGlobal.__FEDERATION__.__INSTANCES__.findIndex((ins) => {
              if (remoteInfo.buildVersion) {
                return ins.options.id === remoteInsId;
              } else {
                return ins.name === remoteInsId;
              }
            });
            if (remoteInsIndex !== -1) {
              const remoteIns = CurrentGlobal.__FEDERATION__.__INSTANCES__[remoteInsIndex];
              remoteInsId = remoteIns.options.id || remoteInsId;
              const globalShareScopeMap = getGlobalShareScope();
              let isAllSharedNotUsed = true;
              const needDeleteKeys = [];
              Object.keys(globalShareScopeMap).forEach((instId) => {
                const shareScopeMap = globalShareScopeMap[instId];
                shareScopeMap && Object.keys(shareScopeMap).forEach((shareScope) => {
                  const shareScopeVal = shareScopeMap[shareScope];
                  shareScopeVal && Object.keys(shareScopeVal).forEach((shareName) => {
                    const sharedPkgs = shareScopeVal[shareName];
                    sharedPkgs && Object.keys(sharedPkgs).forEach((shareVersion) => {
                      const shared = sharedPkgs[shareVersion];
                      if (shared && typeof shared === "object" && shared.from === remoteInfo.name) {
                        if (shared.loaded || shared.loading) {
                          shared.useIn = shared.useIn.filter((usedHostName) => usedHostName !== remoteInfo.name);
                          if (shared.useIn.length) {
                            isAllSharedNotUsed = false;
                          } else {
                            needDeleteKeys.push([
                              instId,
                              shareScope,
                              shareName,
                              shareVersion
                            ]);
                          }
                        } else {
                          needDeleteKeys.push([
                            instId,
                            shareScope,
                            shareName,
                            shareVersion
                          ]);
                        }
                      }
                    });
                  });
                });
              });
              if (isAllSharedNotUsed) {
                remoteIns.shareScopeMap = {};
                delete globalShareScopeMap[remoteInsId];
              }
              needDeleteKeys.forEach(([insId, shareScope, shareName, shareVersion]) => {
                var _globalShareScopeMap_insId_shareScope_shareName, _globalShareScopeMap_insId_shareScope, _globalShareScopeMap_insId;
                (_globalShareScopeMap_insId = globalShareScopeMap[insId]) == null ? true : (_globalShareScopeMap_insId_shareScope = _globalShareScopeMap_insId[shareScope]) == null ? true : (_globalShareScopeMap_insId_shareScope_shareName = _globalShareScopeMap_insId_shareScope[shareName]) == null ? true : delete _globalShareScopeMap_insId_shareScope_shareName[shareVersion];
              });
              CurrentGlobal.__FEDERATION__.__INSTANCES__.splice(remoteInsIndex, 1);
            }
            const { hostGlobalSnapshot } = getGlobalRemoteInfo(remote, host);
            if (hostGlobalSnapshot) {
              const remoteKey = hostGlobalSnapshot && "remotesInfo" in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && getInfoWithoutType(hostGlobalSnapshot.remotesInfo, remote.name).key;
              if (remoteKey) {
                delete hostGlobalSnapshot.remotesInfo[remoteKey];
                if (
                  //eslint-disable-next-line no-extra-boolean-cast
                  Boolean(Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey])
                ) {
                  delete Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey];
                }
              }
            }
            host.moduleCache.delete(remote.name);
          }
        } catch (err) {
          logger2.log("removeRemote fail: ", err);
        }
      }
      constructor(host) {
        this.hooks = new PluginSystem({
          beforeRegisterRemote: new SyncWaterfallHook("beforeRegisterRemote"),
          registerRemote: new SyncWaterfallHook("registerRemote"),
          beforeRequest: new AsyncWaterfallHook("beforeRequest"),
          onLoad: new AsyncHook("onLoad"),
          handlePreloadModule: new SyncHook("handlePreloadModule"),
          errorLoadRemote: new AsyncHook("errorLoadRemote"),
          beforePreloadRemote: new AsyncHook("beforePreloadRemote"),
          generatePreloadAssets: new AsyncHook("generatePreloadAssets"),
          // not used yet
          afterPreloadRemote: new AsyncHook(),
          loadEntry: new AsyncHook()
        });
        this.host = host;
        this.idToRemoteMap = {};
      }
    };
    var USE_SNAPSHOT = typeof FEDERATION_OPTIMIZE_NO_SNAPSHOT_PLUGIN === "boolean" ? !FEDERATION_OPTIMIZE_NO_SNAPSHOT_PLUGIN : true;
    var ModuleFederation = class {
      initOptions(userOptions) {
        this.registerPlugins(userOptions.plugins);
        const options = this.formatOptions(this.options, userOptions);
        this.options = options;
        return options;
      }
      async loadShare(pkgName, extraOptions) {
        return this.sharedHandler.loadShare(pkgName, extraOptions);
      }
      // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.
      // 1. If the loaded shared already exists globally, then it will be reused
      // 2. If lib exists in local shared, it will be used directly
      // 3. If the local get returns something other than Promise, then it will be used directly
      loadShareSync(pkgName, extraOptions) {
        return this.sharedHandler.loadShareSync(pkgName, extraOptions);
      }
      initializeSharing(shareScopeName = DEFAULT_SCOPE, extraOptions) {
        return this.sharedHandler.initializeSharing(shareScopeName, extraOptions);
      }
      initRawContainer(name, url2, container) {
        const remoteInfo = getRemoteInfo({
          name,
          entry: url2
        });
        const module3 = new Module({
          host: this,
          remoteInfo
        });
        module3.remoteEntryExports = container;
        this.moduleCache.set(name, module3);
        return module3;
      }
      // eslint-disable-next-line max-lines-per-function
      // eslint-disable-next-line @typescript-eslint/member-ordering
      async loadRemote(id, options) {
        return this.remoteHandler.loadRemote(id, options);
      }
      // eslint-disable-next-line @typescript-eslint/member-ordering
      async preloadRemote(preloadOptions) {
        return this.remoteHandler.preloadRemote(preloadOptions);
      }
      initShareScopeMap(scopeName, shareScope, extraOptions = {}) {
        this.sharedHandler.initShareScopeMap(scopeName, shareScope, extraOptions);
      }
      formatOptions(globalOptions, userOptions) {
        const { shared } = formatShareConfigs(globalOptions, userOptions);
        const { userOptions: userOptionsRes, options: globalOptionsRes } = this.hooks.lifecycle.beforeInit.emit({
          origin: this,
          userOptions,
          options: globalOptions,
          shareInfo: shared
        });
        const remotes = this.remoteHandler.formatAndRegisterRemote(globalOptionsRes, userOptionsRes);
        const { shared: handledShared } = this.sharedHandler.registerShared(globalOptionsRes, userOptionsRes);
        const plugins = [
          ...globalOptionsRes.plugins
        ];
        if (userOptionsRes.plugins) {
          userOptionsRes.plugins.forEach((plugin) => {
            if (!plugins.includes(plugin)) {
              plugins.push(plugin);
            }
          });
        }
        const optionsRes = polyfills2._extends({}, globalOptions, userOptions, {
          plugins,
          remotes,
          shared: handledShared
        });
        this.hooks.lifecycle.init.emit({
          origin: this,
          options: optionsRes
        });
        return optionsRes;
      }
      registerPlugins(plugins) {
        const pluginRes = registerPlugins(plugins, this);
        this.options.plugins = this.options.plugins.reduce((res2, plugin) => {
          if (!plugin) return res2;
          if (res2 && !res2.find((item) => item.name === plugin.name)) {
            res2.push(plugin);
          }
          return res2;
        }, pluginRes || []);
      }
      registerRemotes(remotes, options) {
        return this.remoteHandler.registerRemotes(remotes, options);
      }
      registerShared(shared) {
        this.sharedHandler.registerShared(this.options, polyfills2._extends({}, this.options, {
          shared
        }));
      }
      constructor(userOptions) {
        this.hooks = new PluginSystem({
          beforeInit: new SyncWaterfallHook("beforeInit"),
          init: new SyncHook(),
          // maybe will change, temporarily for internal use only
          beforeInitContainer: new AsyncWaterfallHook("beforeInitContainer"),
          // maybe will change, temporarily for internal use only
          initContainer: new AsyncWaterfallHook("initContainer")
        });
        this.version = "0.17.1";
        this.moduleCache = /* @__PURE__ */ new Map();
        this.loaderHook = new PluginSystem({
          // FIXME: may not be suitable , not open to the public yet
          getModuleInfo: new SyncHook(),
          createScript: new SyncHook(),
          createLink: new SyncHook(),
          fetch: new AsyncHook(),
          loadEntryError: new AsyncHook(),
          getModuleFactory: new AsyncHook()
        });
        this.bridgeHook = new PluginSystem({
          beforeBridgeRender: new SyncHook(),
          afterBridgeRender: new SyncHook(),
          beforeBridgeDestroy: new SyncHook(),
          afterBridgeDestroy: new SyncHook()
        });
        const plugins = USE_SNAPSHOT ? [
          snapshotPlugin(),
          generatePreloadAssetsPlugin()
        ] : [];
        const defaultOptions = {
          id: getBuilderId(),
          name: userOptions.name,
          plugins,
          remotes: [],
          shared: {},
          inBrowser: sdk.isBrowserEnv()
        };
        this.name = userOptions.name;
        this.options = defaultOptions;
        this.snapshotHandler = new SnapshotHandler(this);
        this.sharedHandler = new SharedHandler(this);
        this.remoteHandler = new RemoteHandler(this);
        this.shareScopeMap = this.sharedHandler.shareScopeMap;
        this.registerPlugins([
          ...defaultOptions.plugins,
          ...userOptions.plugins || []
        ]);
        this.options = this.formatOptions(defaultOptions, userOptions);
      }
    };
    var index = Object.freeze({
      __proto__: null
    });
    exports2.loadScript = sdk.loadScript;
    exports2.loadScriptNode = sdk.loadScriptNode;
    exports2.CurrentGlobal = CurrentGlobal;
    exports2.Global = Global;
    exports2.Module = Module;
    exports2.ModuleFederation = ModuleFederation;
    exports2.addGlobalSnapshot = addGlobalSnapshot;
    exports2.assert = assert2;
    exports2.getGlobalFederationConstructor = getGlobalFederationConstructor;
    exports2.getGlobalSnapshot = getGlobalSnapshot;
    exports2.getInfoWithoutType = getInfoWithoutType;
    exports2.getRegisteredShare = getRegisteredShare;
    exports2.getRemoteEntry = getRemoteEntry;
    exports2.getRemoteInfo = getRemoteInfo;
    exports2.helpers = helpers;
    exports2.isStaticResourcesEqual = isStaticResourcesEqual2;
    exports2.matchRemoteWithNameAndExpose = matchRemoteWithNameAndExpose;
    exports2.registerGlobalPlugins = registerGlobalPlugins;
    exports2.resetFederationGlobalInfo = resetFederationGlobalInfo;
    exports2.safeWrapper = safeWrapper2;
    exports2.satisfy = satisfy;
    exports2.setGlobalFederationConstructor = setGlobalFederationConstructor;
    exports2.setGlobalFederationInstance = setGlobalFederationInstance;
    exports2.types = index;
  }
});

// node_modules/@module-federation/runtime/dist/utils.cjs.cjs
var require_utils_cjs = __commonJS({
  "node_modules/@module-federation/runtime/dist/utils.cjs.cjs"(exports2) {
    "use strict";
    var runtimeCore = require_index_cjs3();
    function getBuilderId() {
      return typeof FEDERATION_BUILD_IDENTIFIER !== "undefined" ? FEDERATION_BUILD_IDENTIFIER : "";
    }
    function getGlobalFederationInstance(name, version) {
      const buildId = getBuilderId();
      return runtimeCore.CurrentGlobal.__FEDERATION__.__INSTANCES__.find((GMInstance) => {
        if (buildId && GMInstance.options.id === buildId) {
          return true;
        }
        if (GMInstance.options.name === name && !GMInstance.options.version && !version) {
          return true;
        }
        if (GMInstance.options.name === name && version && GMInstance.options.version === version) {
          return true;
        }
        return false;
      });
    }
    exports2.getGlobalFederationInstance = getGlobalFederationInstance;
  }
});

// node_modules/@module-federation/runtime/dist/index.cjs.cjs
var require_index_cjs4 = __commonJS({
  "node_modules/@module-federation/runtime/dist/index.cjs.cjs"(exports2) {
    var runtimeCore = require_index_cjs3();
    var errorCodes = require_index_cjs2();
    var utils = require_utils_cjs();
    function createInstance(options) {
      const ModuleFederationConstructor = runtimeCore.getGlobalFederationConstructor() || runtimeCore.ModuleFederation;
      return new ModuleFederationConstructor(options);
    }
    var FederationInstance = null;
    function init(options) {
      const instance = utils.getGlobalFederationInstance(options.name, options.version);
      if (!instance) {
        FederationInstance = createInstance(options);
        runtimeCore.setGlobalFederationInstance(FederationInstance);
        return FederationInstance;
      } else {
        instance.initOptions(options);
        if (!FederationInstance) {
          FederationInstance = instance;
        }
        return instance;
      }
    }
    function loadRemote(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      const loadRemote1 = FederationInstance.loadRemote;
      return loadRemote1.apply(FederationInstance, args);
    }
    function loadShare(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      const loadShare1 = FederationInstance.loadShare;
      return loadShare1.apply(FederationInstance, args);
    }
    function loadShareSync(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      const loadShareSync1 = FederationInstance.loadShareSync;
      return loadShareSync1.apply(FederationInstance, args);
    }
    function preloadRemote(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      return FederationInstance.preloadRemote.apply(FederationInstance, args);
    }
    function registerRemotes(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      return FederationInstance.registerRemotes.apply(FederationInstance, args);
    }
    function registerPlugins(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      return FederationInstance.registerPlugins.apply(FederationInstance, args);
    }
    function getInstance() {
      return FederationInstance;
    }
    function registerShared(...args) {
      runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));
      return FederationInstance.registerShared.apply(FederationInstance, args);
    }
    runtimeCore.setGlobalFederationConstructor(runtimeCore.ModuleFederation);
    exports2.Module = runtimeCore.Module;
    exports2.ModuleFederation = runtimeCore.ModuleFederation;
    exports2.getRemoteEntry = runtimeCore.getRemoteEntry;
    exports2.getRemoteInfo = runtimeCore.getRemoteInfo;
    exports2.loadScript = runtimeCore.loadScript;
    exports2.loadScriptNode = runtimeCore.loadScriptNode;
    exports2.registerGlobalPlugins = runtimeCore.registerGlobalPlugins;
    exports2.createInstance = createInstance;
    exports2.getInstance = getInstance;
    exports2.init = init;
    exports2.loadRemote = loadRemote;
    exports2.loadShare = loadShare;
    exports2.loadShareSync = loadShareSync;
    exports2.preloadRemote = preloadRemote;
    exports2.registerPlugins = registerPlugins;
    exports2.registerRemotes = registerRemotes;
    exports2.registerShared = registerShared;
  }
});

export {
  require_index_cjs4 as require_index_cjs
};
//# sourceMappingURL=chunk-2WHSENDH.js.map
