export const isObjectEmpty = (objectName) => {
  return (
    Object.keys(objectName).length === 0 && objectName.constructor === Object
  );
};

export const isArrayEmpty = (array) => {
  return array.length === 0;
};

export const getSeparator = (value) => {
  switch (value) {
    case 'dot':
      return '.';
    case 'comma':
      return ',';
    case 'space':
      return ' ';
    default:
      break;
  }
  return value;
};

export const getArrayFieldValue = (value) => {
  return value && typeof value === 'string'
    ? value
        .split(',')
        ?.map((i) => i.trim())
        ?.filter((i) => i !== '')
    : [];
};

export const addBorderSolidDefaultStyle = ({ borderWidth, borderColor }) => {
  return borderWidth && borderColor
    ? {
        borderWidth: borderWidth,
        borderColor: borderColor,
        borderStyle: 'solid',
      }
    : { border: 'unset' };
};

export const addBorderEdgeSolidDefaultStyle = ({
  edge,
  borderWidth,
  borderColor,
}) => {
  return edge && borderWidth && borderColor
    ? {
        [`border${edge}Width`]: borderWidth,
        [`border${edge}Color`]: borderColor,
        [`border${edge}Style`]: 'solid',
      }
    : edge
    ? { [`border${edge}`]: 'unset' }
    : {};
};

export const addBorderRadiusDefaultStyle = ({
  borderTopLeftRadius,
  borderTopRightRadius,
  borderBottomLeftRadius,
  borderBottomRightRadius,
}) => {
  return {
    borderBottomLeftRadius: borderBottomLeftRadius ?? 'unset',
    borderBottomRightRadius: borderBottomRightRadius ?? 'unset',
    borderTopLeftRadius: borderTopLeftRadius ?? 'unset',
    borderTopRightRadius: borderTopRightRadius ?? 'unset',
  };
};

export const markImportantCssProperty = (property) => {
  if (!property) return {};
  
  const result = {};
  Object.entries(property).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      result[key] = `${value} !important`;
    }
  });
  return result;
};

/**
 * Generates options array for message type select dropdown
 * @param {Object} sources - Object containing source data with message types
 * @param {string} sourceId - ID of the source to get message types from
 * @returns {Array<{label: string, value: string}>} Array of message type options for select dropdown
 * @example
 * const sources = {
 *   '1': {
 *     messageTypes: [
 *       { name: 'Type A', messageTypeId: '1' },
 *       { name: 'Type B', messageTypeId: '2' }
 *     ]
 *   }
 * };
 * getMessageTypeOptions(sources, '1');
 * // Returns: [
 * //   { label: 'Type A', value: '1' },
 * //   { label: 'Type B', value: '2' }
 * // ]
 */
export const getMessageTypeOptions = (sources, sourceId) => {
  const source = sources[sourceId];
  return (
    source?.messageTypes?.map((mt) => ({
      label: mt?.name,
      value: mt?.messageTypeId?.toString(),
    })) || []
  );
};

/**
 * Generates a unique key with timestamp and random string
 * @param {string} [prefix='Key'] - The prefix to use for the generated key
 * @returns {string} A unique key in the format: `${prefix}_${timestamp}${random}`
 * @example
 * generateUniqueKey(); // Returns: "Key_timestamprandom"
 * generateUniqueKey('PR'); // Returns: "PR_timestamprandom"
 */
export const generateUniqueKey = (prefix = 'Key') => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 4);
  return `${prefix}_${timestamp}${random}`;
};

/**
 * Creates a function that generates unique keys with a specified prefix
 * @param {string} [prefix='Key'] - The prefix to use for the generated keys
 * @returns {Function} A function that generates unique keys in the format: `${prefix}_${timestamp}${random}`
 * @example
 * // Create a generator with default prefix
 * const generateKey = createUniqueKeyGenerator();
 * const key1 = generateKey(); // Returns: "Key_timestamprandom"
 * 
 * // Create a generator with custom prefix
 * const generatePressReleaseKey = createUniqueKeyGenerator('PR');
 * const key2 = generatePressReleaseKey(); // Returns: "PR_timestamprandom"
 */
export const createUniqueKeyGenerator = (prefix = 'Key') => {
  return () => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 4);
    return `${prefix}_${timestamp}${random}`;
  };
};

/**
 * Convert language forwarding array to string format
 * @param {Array<{Source: string, SourceLanguage: string, DestinationLanguage: string}>} data
 * @returns {string} Formatted string (e.g. "5|ar-AE|en-GB;6|zh-TW|zh-CN;")
 */
export const formatLanguageForwardingToString = (data) => {
  if (!Array.isArray(data) || data.length === 0) return '';
  return data
    .map(
      (item) =>
        `${item.Source}|${item.SourceLanguage || item.ForwardedLanguage}|${item.DestinationLanguage || item.DisplayLanguage}`
    )
    .join(';') + ';';
};

/**
 * Convert language forwarding string to array format
 * @param {string} str Formatted string (e.g. "5|ar-AE|en-GB;6|zh-TW|zh-CN;")
 * @returns {Array<{Source: string, ForwardedLanguage: string, DisplayLanguage: string}>}
 */
export const parseLanguageForwardingString = (str) => {
  if (!str) return [];
  return str
    .split(';')
    .map(item => item.trim())
    .filter(Boolean)
    .map((item) => {
      const [Source, ForwardedLanguage, DisplayLanguage] = item.split('|');
      return {
        Source,
        ForwardedLanguage,
        DisplayLanguage,
      };
    });
};

/**
 * Convert source message type array to string format
 * @param {Array<{Source: string, MessageType: string}>} data - Array of source and message type objects
 * @param {string} [messageFieldName='MessageType'] - The field name to use for the message type
 * @returns {string} Formatted string (e.g. "5|12;6|12;")
 * @example
 * const data = [{ Source: '5', MessageType: '12' }, { Source: '6', MessageType: '12' }];
 * formatSourceMessageTypeToString(data); // Returns: "5|12;6|12;"
 * formatSourceMessageTypeToString(data, 'Type'); // Returns: "5|12;6|12;" (using Type field)
 */
export const formatSourceMessageTypeToString = (data, messageFieldName = 'MessageType') => {
  if (!Array.isArray(data) || data.length === 0) return '';
  return data
    .map(
      (item) =>
        `${item.Source}|${item[messageFieldName]}`
    )
    .join(';') + ';';
};

/**
 * Convert source message type string to array format
 * @param {string} str - Formatted string (e.g. "5|12;6|12;")
 * @param {string} [messageFieldName='MessageType'] - The field name to use for the message type
 * @returns {Array<{Source: string, MessageType: string}>} Array of source and message type objects
 * @example
 * const str = "5|12;6|12;";
 * parseSourceMessageTypeString(str); // Returns: [{ Source: '5', MessageType: '12' }, { Source: '6', MessageType: '12' }]
 * parseSourceMessageTypeString(str, 'Type'); // Returns: [{ Source: '5', Type: '12' }, { Source: '6', Type: '12' }]
 */
export const parseSourceMessageTypeString = (str, messageFieldName = 'MessageType') => {
  if (!str) return [];
  return str
    .split(';')
    .map(item => item.trim())
    .filter(Boolean)
    .map((item) => {
      const [Source, messageType] = item.split('|');
      return {
        Source,
        [messageFieldName]: messageType,
      };
    });
};