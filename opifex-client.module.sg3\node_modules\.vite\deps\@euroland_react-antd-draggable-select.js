import {
  require_sg3_mf_2_project_mf_2_name_loadShare_antd_loadShare
} from "./chunk-CPTQBLXJ.js";
import {
  require_sg3_mf_2_project_mf_2_name_loadShare_react_mf_1_jsx_mf_2_runtime_loadShare
} from "./chunk-3JL5Q2GP.js";
import {
  DndContext,
  KeyboardCode,
  getClientRect,
  useDndContext,
  useDraggable,
  useDroppable
} from "./chunk-GDPTDO3T.js";
import {
  CSS,
  isKeyboardEvent,
  useCombinedRefs,
  useIsomorphicLayoutEffect,
  useUniqueId
} from "./chunk-K7HH4V7V.js";
import "./chunk-5TDOOKFX.js";
import {
  require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare
} from "./chunk-ZUHQWO77.js";
import "./chunk-5XPW24QV.js";
import "./chunk-2WHSENDH.js";
import {
  __toESM
} from "./chunk-ZKAWKZG5.js";

// node_modules/@euroland/react-antd-draggable-select/dist/libs/ReactAntdDraggableSelect.es.js
var import_jsx_runtime = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_mf_1_jsx_mf_2_runtime_loadShare());
var import_react4 = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());
var import_antd = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_antd_loadShare());

// node_modules/@euroland/react-antd-draggable-select/node_modules/@dnd-kit/sortable/dist/sortable.esm.js
var import_react = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());
function arrayMove(array, from, to) {
  const newArray = array.slice();
  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);
  return newArray;
}
function getSortedRects(items, rects) {
  return items.reduce((accumulator, id, index) => {
    const rect = rects.get(id);
    if (rect) {
      accumulator[index] = rect;
    }
    return accumulator;
  }, Array(items.length));
}
function isValidIndex(index) {
  return index !== null && index >= 0;
}
function itemsEqual(a3, b4) {
  if (a3 === b4) {
    return true;
  }
  if (a3.length !== b4.length) {
    return false;
  }
  for (let i6 = 0; i6 < a3.length; i6++) {
    if (a3[i6] !== b4[i6]) {
      return false;
    }
  }
  return true;
}
function normalizeDisabled(disabled) {
  if (typeof disabled === "boolean") {
    return {
      draggable: disabled,
      droppable: disabled
    };
  }
  return disabled;
}
var defaultScale = {
  scaleX: 1,
  scaleY: 1
};
var horizontalListSortingStrategy = (_ref) => {
  var _rects$activeIndex;
  let {
    rects,
    activeNodeRect: fallbackActiveRect,
    activeIndex,
    overIndex,
    index
  } = _ref;
  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;
  if (!activeNodeRect) {
    return null;
  }
  const itemGap = getItemGap(rects, index, activeIndex);
  if (index === activeIndex) {
    const newIndexRect = rects[overIndex];
    if (!newIndexRect) {
      return null;
    }
    return {
      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,
      y: 0,
      ...defaultScale
    };
  }
  if (index > activeIndex && index <= overIndex) {
    return {
      x: -activeNodeRect.width - itemGap,
      y: 0,
      ...defaultScale
    };
  }
  if (index < activeIndex && index >= overIndex) {
    return {
      x: activeNodeRect.width + itemGap,
      y: 0,
      ...defaultScale
    };
  }
  return {
    x: 0,
    y: 0,
    ...defaultScale
  };
};
function getItemGap(rects, index, activeIndex) {
  const currentRect = rects[index];
  const previousRect = rects[index - 1];
  const nextRect = rects[index + 1];
  if (!currentRect || !previousRect && !nextRect) {
    return 0;
  }
  if (activeIndex < index) {
    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);
  }
  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);
}
var rectSortingStrategy = (_ref) => {
  let {
    rects,
    activeIndex,
    overIndex,
    index
  } = _ref;
  const newRects = arrayMove(rects, overIndex, activeIndex);
  const oldRect = rects[index];
  const newRect = newRects[index];
  if (!newRect || !oldRect) {
    return null;
  }
  return {
    x: newRect.left - oldRect.left,
    y: newRect.top - oldRect.top,
    scaleX: newRect.width / oldRect.width,
    scaleY: newRect.height / oldRect.height
  };
};
var defaultScale$1 = {
  scaleX: 1,
  scaleY: 1
};
var verticalListSortingStrategy = (_ref) => {
  var _rects$activeIndex;
  let {
    activeIndex,
    activeNodeRect: fallbackActiveRect,
    index,
    rects,
    overIndex
  } = _ref;
  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;
  if (!activeNodeRect) {
    return null;
  }
  if (index === activeIndex) {
    const overIndexRect = rects[overIndex];
    if (!overIndexRect) {
      return null;
    }
    return {
      x: 0,
      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,
      ...defaultScale$1
    };
  }
  const itemGap = getItemGap$1(rects, index, activeIndex);
  if (index > activeIndex && index <= overIndex) {
    return {
      x: 0,
      y: -activeNodeRect.height - itemGap,
      ...defaultScale$1
    };
  }
  if (index < activeIndex && index >= overIndex) {
    return {
      x: 0,
      y: activeNodeRect.height + itemGap,
      ...defaultScale$1
    };
  }
  return {
    x: 0,
    y: 0,
    ...defaultScale$1
  };
};
function getItemGap$1(clientRects, index, activeIndex) {
  const currentRect = clientRects[index];
  const previousRect = clientRects[index - 1];
  const nextRect = clientRects[index + 1];
  if (!currentRect) {
    return 0;
  }
  if (activeIndex < index) {
    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;
  }
  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;
}
var ID_PREFIX = "Sortable";
var Context = import_react.default.createContext({
  activeIndex: -1,
  containerId: ID_PREFIX,
  disableTransforms: false,
  items: [],
  overIndex: -1,
  useDragOverlay: false,
  sortedRects: [],
  strategy: rectSortingStrategy,
  disabled: {
    draggable: false,
    droppable: false
  }
});
function SortableContext(_ref) {
  let {
    children,
    id,
    items: userDefinedItems,
    strategy = rectSortingStrategy,
    disabled: disabledProp = false
  } = _ref;
  const {
    active,
    dragOverlay,
    droppableRects,
    over,
    measureDroppableContainers
  } = useDndContext();
  const containerId = useUniqueId(ID_PREFIX, id);
  const useDragOverlay = Boolean(dragOverlay.rect !== null);
  const items = (0, import_react.useMemo)(() => userDefinedItems.map((item) => typeof item === "object" && "id" in item ? item.id : item), [userDefinedItems]);
  const isDragging = active != null;
  const activeIndex = active ? items.indexOf(active.id) : -1;
  const overIndex = over ? items.indexOf(over.id) : -1;
  const previousItemsRef = (0, import_react.useRef)(items);
  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);
  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;
  const disabled = normalizeDisabled(disabledProp);
  useIsomorphicLayoutEffect(() => {
    if (itemsHaveChanged && isDragging) {
      measureDroppableContainers(items);
    }
  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);
  (0, import_react.useEffect)(() => {
    previousItemsRef.current = items;
  }, [items]);
  const contextValue = (0, import_react.useMemo)(
    () => ({
      activeIndex,
      containerId,
      disabled,
      disableTransforms,
      items,
      overIndex,
      useDragOverlay,
      sortedRects: getSortedRects(items, droppableRects),
      strategy
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]
  );
  return import_react.default.createElement(Context.Provider, {
    value: contextValue
  }, children);
}
var defaultNewIndexGetter = (_ref) => {
  let {
    id,
    items,
    activeIndex,
    overIndex
  } = _ref;
  return arrayMove(items, activeIndex, overIndex).indexOf(id);
};
var defaultAnimateLayoutChanges = (_ref2) => {
  let {
    containerId,
    isSorting,
    wasDragging,
    index,
    items,
    newIndex,
    previousItems,
    previousContainerId,
    transition
  } = _ref2;
  if (!transition || !wasDragging) {
    return false;
  }
  if (previousItems !== items && index === newIndex) {
    return false;
  }
  if (isSorting) {
    return true;
  }
  return newIndex !== index && containerId === previousContainerId;
};
var defaultTransition = {
  duration: 200,
  easing: "ease"
};
var transitionProperty = "transform";
var disabledTransition = CSS.Transition.toString({
  property: transitionProperty,
  duration: 0,
  easing: "linear"
});
var defaultAttributes = {
  roleDescription: "sortable"
};
function useDerivedTransform(_ref) {
  let {
    disabled,
    index,
    node,
    rect
  } = _ref;
  const [derivedTransform, setDerivedtransform] = (0, import_react.useState)(null);
  const previousIndex = (0, import_react.useRef)(index);
  useIsomorphicLayoutEffect(() => {
    if (!disabled && index !== previousIndex.current && node.current) {
      const initial = rect.current;
      if (initial) {
        const current = getClientRect(node.current, {
          ignoreTransform: true
        });
        const delta = {
          x: initial.left - current.left,
          y: initial.top - current.top,
          scaleX: initial.width / current.width,
          scaleY: initial.height / current.height
        };
        if (delta.x || delta.y) {
          setDerivedtransform(delta);
        }
      }
    }
    if (index !== previousIndex.current) {
      previousIndex.current = index;
    }
  }, [disabled, index, node, rect]);
  (0, import_react.useEffect)(() => {
    if (derivedTransform) {
      setDerivedtransform(null);
    }
  }, [derivedTransform]);
  return derivedTransform;
}
function useSortable(_ref) {
  let {
    animateLayoutChanges = defaultAnimateLayoutChanges,
    attributes: userDefinedAttributes,
    disabled: localDisabled,
    data: customData,
    getNewIndex = defaultNewIndexGetter,
    id,
    strategy: localStrategy,
    resizeObserverConfig,
    transition = defaultTransition
  } = _ref;
  const {
    items,
    containerId,
    activeIndex,
    disabled: globalDisabled,
    disableTransforms,
    sortedRects,
    overIndex,
    useDragOverlay,
    strategy: globalStrategy
  } = (0, import_react.useContext)(Context);
  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);
  const index = items.indexOf(id);
  const data = (0, import_react.useMemo)(() => ({
    sortable: {
      containerId,
      index,
      items
    },
    ...customData
  }), [containerId, customData, index, items]);
  const itemsAfterCurrentSortable = (0, import_react.useMemo)(() => items.slice(items.indexOf(id)), [items, id]);
  const {
    rect,
    node,
    isOver,
    setNodeRef: setDroppableNodeRef
  } = useDroppable({
    id,
    data,
    disabled: disabled.droppable,
    resizeObserverConfig: {
      updateMeasurementsFor: itemsAfterCurrentSortable,
      ...resizeObserverConfig
    }
  });
  const {
    active,
    activatorEvent,
    activeNodeRect,
    attributes,
    setNodeRef: setDraggableNodeRef,
    listeners,
    isDragging,
    over,
    setActivatorNodeRef,
    transform
  } = useDraggable({
    id,
    data,
    attributes: {
      ...defaultAttributes,
      ...userDefinedAttributes
    },
    disabled: disabled.draggable
  });
  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);
  const isSorting = Boolean(active);
  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);
  const shouldDisplaceDragSource = !useDragOverlay && isDragging;
  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;
  const strategy = localStrategy != null ? localStrategy : globalStrategy;
  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({
    rects: sortedRects,
    activeNodeRect,
    activeIndex,
    overIndex,
    index
  }) : null;
  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({
    id,
    items,
    activeIndex,
    overIndex
  }) : index;
  const activeId = active == null ? void 0 : active.id;
  const previous = (0, import_react.useRef)({
    activeId,
    items,
    newIndex,
    containerId
  });
  const itemsHaveChanged = items !== previous.current.items;
  const shouldAnimateLayoutChanges = animateLayoutChanges({
    active,
    containerId,
    isDragging,
    isSorting,
    id,
    index,
    items,
    newIndex: previous.current.newIndex,
    previousItems: previous.current.items,
    previousContainerId: previous.current.containerId,
    transition,
    wasDragging: previous.current.activeId != null
  });
  const derivedTransform = useDerivedTransform({
    disabled: !shouldAnimateLayoutChanges,
    index,
    node,
    rect
  });
  (0, import_react.useEffect)(() => {
    if (isSorting && previous.current.newIndex !== newIndex) {
      previous.current.newIndex = newIndex;
    }
    if (containerId !== previous.current.containerId) {
      previous.current.containerId = containerId;
    }
    if (items !== previous.current.items) {
      previous.current.items = items;
    }
  }, [isSorting, newIndex, containerId, items]);
  (0, import_react.useEffect)(() => {
    if (activeId === previous.current.activeId) {
      return;
    }
    if (activeId != null && previous.current.activeId == null) {
      previous.current.activeId = activeId;
      return;
    }
    const timeoutId = setTimeout(() => {
      previous.current.activeId = activeId;
    }, 50);
    return () => clearTimeout(timeoutId);
  }, [activeId]);
  return {
    active,
    activeIndex,
    attributes,
    data,
    rect,
    index,
    newIndex,
    items,
    isOver,
    isSorting,
    isDragging,
    listeners,
    node,
    overIndex,
    over,
    setNodeRef,
    setActivatorNodeRef,
    setDroppableNodeRef,
    setDraggableNodeRef,
    transform: derivedTransform != null ? derivedTransform : finalTransform,
    transition: getTransition()
  };
  function getTransition() {
    if (
      // Temporarily disable transitions for a single frame to set up derived transforms
      derivedTransform || // Or to prevent items jumping to back to their "new" position when items change
      itemsHaveChanged && previous.current.newIndex === index
    ) {
      return disabledTransition;
    }
    if (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent) || !transition) {
      return void 0;
    }
    if (isSorting || shouldAnimateLayoutChanges) {
      return CSS.Transition.toString({
        ...transition,
        property: transitionProperty
      });
    }
    return void 0;
  }
}
function normalizeLocalDisabled(localDisabled, globalDisabled) {
  var _localDisabled$dragga, _localDisabled$droppa;
  if (typeof localDisabled === "boolean") {
    return {
      draggable: localDisabled,
      // Backwards compatibility
      droppable: false
    };
  }
  return {
    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,
    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable
  };
}
var directions = [KeyboardCode.Down, KeyboardCode.Right, KeyboardCode.Up, KeyboardCode.Left];

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/extends.es.js
function e() {
  return e = Object.assign ? Object.assign.bind() : function(t3) {
    for (var n5 = 1; n5 < arguments.length; n5++) {
      var a3 = arguments[n5];
      for (var r6 in a3) ({}).hasOwnProperty.call(a3, r6) && (t3[r6] = a3[r6]);
    }
    return t3;
  }, e.apply(null, arguments);
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/icons/CloseOutlined.es.js
var e5 = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons-svg/es/asn/CloseOutlined.es.js
var c = { icon: { tag: "svg", attrs: { "fill-rule": "evenodd", viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z" } }] }, name: "close", theme: "outlined" };

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.es.js
function a(r6) {
  if (Array.isArray(r6)) return r6;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.es.js
function c2(t3, a3) {
  var l5 = t3 == null ? null : typeof Symbol < "u" && t3[Symbol.iterator] || t3["@@iterator"];
  if (l5 != null) {
    var i6, u6, o5, r6, e6 = [], n5 = true, f7 = false;
    try {
      if (o5 = (l5 = l5.call(t3)).next, a3 !== 0) for (; !(n5 = (i6 = o5.call(l5)).done) && (e6.push(i6.value), e6.length !== a3); n5 = true) ;
    } catch (y2) {
      f7 = true, u6 = y2;
    } finally {
      try {
        if (!n5 && l5.return != null && (r6 = l5.return(), Object(r6) !== r6)) return;
      } finally {
        if (f7) throw u6;
      }
    }
    return e6;
  }
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.es.js
function o(l5, r6) {
  (r6 == null || r6 > l5.length) && (r6 = l5.length);
  for (var t3 = 0, n5 = Array(r6); t3 < r6; t3++) n5[t3] = l5[t3];
  return n5;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.es.js
function n(r6, e6) {
  if (r6) {
    if (typeof r6 == "string") return o(r6, e6);
    var t3 = {}.toString.call(r6).slice(8, -1);
    return t3 === "Object" && r6.constructor && (t3 = r6.constructor.name), t3 === "Map" || t3 === "Set" ? Array.from(r6) : t3 === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t3) ? o(r6, e6) : void 0;
  }
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/nonIterableRest.es.js
function e2() {
  throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/slicedToArray.es.js
function n2(r6, o5) {
  return a(r6) || c2(r6, o5) || n(r6, o5) || e2();
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/typeof.es.js
function o2(e6) {
  "@babel/helpers - typeof";
  return o2 = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(t3) {
    return typeof t3;
  } : function(t3) {
    return t3 && typeof Symbol == "function" && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
  }, o2(e6);
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/toPrimitive.es.js
function m(r6, e6) {
  if (o2(r6) != "object" || !r6) return r6;
  var i6 = r6[Symbol.toPrimitive];
  if (i6 !== void 0) {
    var t3 = i6.call(r6, e6 || "default");
    if (o2(t3) != "object") return t3;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (e6 === "string" ? String : Number)(r6);
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/toPropertyKey.es.js
function f(t3) {
  var r6 = m(t3, "string");
  return o2(r6) == "symbol" ? r6 : r6 + "";
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/defineProperty.es.js
function i(e6, r6, t3) {
  return (r6 = f(r6)) in e6 ? Object.defineProperty(e6, r6, {
    value: t3,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e6[r6] = t3, e6;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.es.js
function r(t3, i6) {
  if (t3 == null) return {};
  var o5 = {};
  for (var e6 in t3) if ({}.hasOwnProperty.call(t3, e6)) {
    if (i6.indexOf(e6) !== -1) continue;
    o5[e6] = t3[e6];
  }
  return o5;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.es.js
function u(r6, e6) {
  if (r6 == null) return {};
  var t3, o5, i6 = r(r6, e6);
  if (Object.getOwnPropertySymbols) {
    var l5 = Object.getOwnPropertySymbols(r6);
    for (o5 = 0; o5 < l5.length; o5++) t3 = l5[o5], e6.indexOf(t3) === -1 && {}.propertyIsEnumerable.call(r6, t3) && (i6[t3] = r6[t3]);
  }
  return i6;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/components/AntdIcon.es.js
var r4 = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());

// node_modules/@euroland/react-antd-draggable-select/dist/_virtual/_commonjsHelpers.es.js
function e3(t3) {
  return t3 && t3.__esModule && Object.prototype.hasOwnProperty.call(t3, "default") ? t3.default : t3;
}

// node_modules/@euroland/react-antd-draggable-select/dist/_virtual/index.es.js
var s = { exports: {} };

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/classnames/index.es.js
(function(a3) {
  (function() {
    var l5 = {}.hasOwnProperty;
    function t3() {
      for (var e6 = [], o5 = 0; o5 < arguments.length; o5++) {
        var s5 = arguments[o5];
        if (s5) {
          var n5 = typeof s5;
          if (n5 === "string" || n5 === "number")
            e6.push(s5);
          else if (Array.isArray(s5)) {
            if (s5.length) {
              var f7 = t3.apply(null, s5);
              f7 && e6.push(f7);
            }
          } else if (n5 === "object")
            if (s5.toString === Object.prototype.toString)
              for (var r6 in s5)
                l5.call(s5, r6) && s5[r6] && e6.push(r6);
            else
              e6.push(s5.toString());
        }
      }
      return e6.join(" ");
    }
    a3.exports ? (t3.default = t3, a3.exports = t3) : window.classNames = t3;
  })();
})(s);
var u2 = s.exports;
var h = e3(u2);

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/colors/es/presets.es.js
var f2 = ["#e6f4ff", "#bae0ff", "#91caff", "#69b1ff", "#4096ff", "#1677ff", "#0958d9", "#003eb3", "#002c8c", "#001d66"];
f2.primary = f2[5];

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/components/Context.es.js
var import_react2 = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());
var o3 = (0, import_react2.createContext)({});

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@babel/runtime/helpers/esm/objectSpread2.es.js
function o4(r6, n5) {
  var e6 = Object.keys(r6);
  if (Object.getOwnPropertySymbols) {
    var t3 = Object.getOwnPropertySymbols(r6);
    n5 && (t3 = t3.filter(function(c5) {
      return Object.getOwnPropertyDescriptor(r6, c5).enumerable;
    })), e6.push.apply(e6, t3);
  }
  return e6;
}
function i2(r6) {
  for (var n5 = 1; n5 < arguments.length; n5++) {
    var e6 = arguments[n5] != null ? arguments[n5] : {};
    n5 % 2 ? o4(Object(e6), true).forEach(function(t3) {
      i(r6, t3, e6[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(r6, Object.getOwnPropertyDescriptors(e6)) : o4(Object(e6)).forEach(function(t3) {
      Object.defineProperty(r6, t3, Object.getOwnPropertyDescriptor(e6, t3));
    });
  }
  return r6;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/components/IconBase.es.js
var T2 = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/fast-color/es/FastColor.es.js
var r2 = Math.round;
function m2(l5, t3) {
  const s5 = l5.replace(/^[^(]*\((.*)/, "$1").replace(/\).*/, "").match(/\d*\.?\d+%?/g) || [], i6 = s5.map((h6) => parseFloat(h6));
  for (let h6 = 0; h6 < 3; h6 += 1)
    i6[h6] = t3(i6[h6] || 0, s5[h6] || "", h6);
  return s5[3] ? i6[3] = s5[3].includes("%") ? i6[3] / 100 : i6[3] : i6[3] = 1, i6;
}
var d = (l5, t3, s5) => s5 === 0 ? l5 : l5 / 100;
function u3(l5, t3) {
  const s5 = t3 || 255;
  return l5 > s5 ? s5 : l5 < 0 ? 0 : l5;
}
var H = class _H {
  constructor(t3) {
    i(this, "isValid", true), i(this, "r", 0), i(this, "g", 0), i(this, "b", 0), i(this, "a", 1), i(this, "_h", void 0), i(this, "_s", void 0), i(this, "_l", void 0), i(this, "_v", void 0), i(this, "_max", void 0), i(this, "_min", void 0), i(this, "_brightness", void 0);
    function s5(i6) {
      return i6[0] in t3 && i6[1] in t3 && i6[2] in t3;
    }
    if (t3) if (typeof t3 == "string") {
      let h6 = function(e6) {
        return i6.startsWith(e6);
      };
      const i6 = t3.trim();
      /^#?[A-F\d]{3,8}$/i.test(i6) ? this.fromHexString(i6) : h6("rgb") ? this.fromRgbString(i6) : h6("hsl") ? this.fromHslString(i6) : (h6("hsv") || h6("hsb")) && this.fromHsvString(i6);
    } else if (t3 instanceof _H)
      this.r = t3.r, this.g = t3.g, this.b = t3.b, this.a = t3.a, this._h = t3._h, this._s = t3._s, this._l = t3._l, this._v = t3._v;
    else if (s5("rgb"))
      this.r = u3(t3.r), this.g = u3(t3.g), this.b = u3(t3.b), this.a = typeof t3.a == "number" ? u3(t3.a, 1) : 1;
    else if (s5("hsl"))
      this.fromHsl(t3);
    else if (s5("hsv"))
      this.fromHsv(t3);
    else
      throw new Error("@ant-design/fast-color: unsupported input " + JSON.stringify(t3));
  }
  // ======================= Setter =======================
  setR(t3) {
    return this._sc("r", t3);
  }
  setG(t3) {
    return this._sc("g", t3);
  }
  setB(t3) {
    return this._sc("b", t3);
  }
  setA(t3) {
    return this._sc("a", t3, 1);
  }
  setHue(t3) {
    const s5 = this.toHsv();
    return s5.h = t3, this._c(s5);
  }
  // ======================= Getter =======================
  /**
   * Returns the perceived luminance of a color, from 0-1.
   * @see http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef
   */
  getLuminance() {
    function t3(e6) {
      const a3 = e6 / 255;
      return a3 <= 0.03928 ? a3 / 12.92 : Math.pow((a3 + 0.055) / 1.055, 2.4);
    }
    const s5 = t3(this.r), i6 = t3(this.g), h6 = t3(this.b);
    return 0.2126 * s5 + 0.7152 * i6 + 0.0722 * h6;
  }
  getHue() {
    if (typeof this._h > "u") {
      const t3 = this.getMax() - this.getMin();
      t3 === 0 ? this._h = 0 : this._h = r2(60 * (this.r === this.getMax() ? (this.g - this.b) / t3 + (this.g < this.b ? 6 : 0) : this.g === this.getMax() ? (this.b - this.r) / t3 + 2 : (this.r - this.g) / t3 + 4));
    }
    return this._h;
  }
  getSaturation() {
    if (typeof this._s > "u") {
      const t3 = this.getMax() - this.getMin();
      t3 === 0 ? this._s = 0 : this._s = t3 / this.getMax();
    }
    return this._s;
  }
  getLightness() {
    return typeof this._l > "u" && (this._l = (this.getMax() + this.getMin()) / 510), this._l;
  }
  getValue() {
    return typeof this._v > "u" && (this._v = this.getMax() / 255), this._v;
  }
  /**
   * Returns the perceived brightness of the color, from 0-255.
   * Note: this is not the b of HSB
   * @see http://www.w3.org/TR/AERT#color-contrast
   */
  getBrightness() {
    return typeof this._brightness > "u" && (this._brightness = (this.r * 299 + this.g * 587 + this.b * 114) / 1e3), this._brightness;
  }
  // ======================== Func ========================
  darken(t3 = 10) {
    const s5 = this.getHue(), i6 = this.getSaturation();
    let h6 = this.getLightness() - t3 / 100;
    return h6 < 0 && (h6 = 0), this._c({
      h: s5,
      s: i6,
      l: h6,
      a: this.a
    });
  }
  lighten(t3 = 10) {
    const s5 = this.getHue(), i6 = this.getSaturation();
    let h6 = this.getLightness() + t3 / 100;
    return h6 > 1 && (h6 = 1), this._c({
      h: s5,
      s: i6,
      l: h6,
      a: this.a
    });
  }
  /**
   * Mix the current color a given amount with another color, from 0 to 100.
   * 0 means no mixing (return current color).
   */
  mix(t3, s5 = 50) {
    const i6 = this._c(t3), h6 = s5 / 100, e6 = (g) => (i6[g] - this[g]) * h6 + this[g], a3 = {
      r: r2(e6("r")),
      g: r2(e6("g")),
      b: r2(e6("b")),
      a: r2(e6("a") * 100) / 100
    };
    return this._c(a3);
  }
  /**
   * Mix the color with pure white, from 0 to 100.
   * Providing 0 will do nothing, providing 100 will always return white.
   */
  tint(t3 = 10) {
    return this.mix({
      r: 255,
      g: 255,
      b: 255,
      a: 1
    }, t3);
  }
  /**
   * Mix the color with pure black, from 0 to 100.
   * Providing 0 will do nothing, providing 100 will always return black.
   */
  shade(t3 = 10) {
    return this.mix({
      r: 0,
      g: 0,
      b: 0,
      a: 1
    }, t3);
  }
  onBackground(t3) {
    const s5 = this._c(t3), i6 = this.a + s5.a * (1 - this.a), h6 = (e6) => r2((this[e6] * this.a + s5[e6] * s5.a * (1 - this.a)) / i6);
    return this._c({
      r: h6("r"),
      g: h6("g"),
      b: h6("b"),
      a: i6
    });
  }
  // ======================= Status =======================
  isDark() {
    return this.getBrightness() < 128;
  }
  isLight() {
    return this.getBrightness() >= 128;
  }
  // ======================== MISC ========================
  equals(t3) {
    return this.r === t3.r && this.g === t3.g && this.b === t3.b && this.a === t3.a;
  }
  clone() {
    return this._c(this);
  }
  // ======================= Format =======================
  toHexString() {
    let t3 = "#";
    const s5 = (this.r || 0).toString(16);
    t3 += s5.length === 2 ? s5 : "0" + s5;
    const i6 = (this.g || 0).toString(16);
    t3 += i6.length === 2 ? i6 : "0" + i6;
    const h6 = (this.b || 0).toString(16);
    if (t3 += h6.length === 2 ? h6 : "0" + h6, typeof this.a == "number" && this.a >= 0 && this.a < 1) {
      const e6 = r2(this.a * 255).toString(16);
      t3 += e6.length === 2 ? e6 : "0" + e6;
    }
    return t3;
  }
  /** CSS support color pattern */
  toHsl() {
    return {
      h: this.getHue(),
      s: this.getSaturation(),
      l: this.getLightness(),
      a: this.a
    };
  }
  /** CSS support color pattern */
  toHslString() {
    const t3 = this.getHue(), s5 = r2(this.getSaturation() * 100), i6 = r2(this.getLightness() * 100);
    return this.a !== 1 ? `hsla(${t3},${s5}%,${i6}%,${this.a})` : `hsl(${t3},${s5}%,${i6}%)`;
  }
  /** Same as toHsb */
  toHsv() {
    return {
      h: this.getHue(),
      s: this.getSaturation(),
      v: this.getValue(),
      a: this.a
    };
  }
  toRgb() {
    return {
      r: this.r,
      g: this.g,
      b: this.b,
      a: this.a
    };
  }
  toRgbString() {
    return this.a !== 1 ? `rgba(${this.r},${this.g},${this.b},${this.a})` : `rgb(${this.r},${this.g},${this.b})`;
  }
  toString() {
    return this.toRgbString();
  }
  // ====================== Privates ======================
  /** Return a new FastColor object with one channel changed */
  _sc(t3, s5, i6) {
    const h6 = this.clone();
    return h6[t3] = u3(s5, i6), h6;
  }
  _c(t3) {
    return new this.constructor(t3);
  }
  getMax() {
    return typeof this._max > "u" && (this._max = Math.max(this.r, this.g, this.b)), this._max;
  }
  getMin() {
    return typeof this._min > "u" && (this._min = Math.min(this.r, this.g, this.b)), this._min;
  }
  fromHexString(t3) {
    const s5 = t3.replace("#", "");
    function i6(h6, e6) {
      return parseInt(s5[h6] + s5[e6 || h6], 16);
    }
    s5.length < 6 ? (this.r = i6(0), this.g = i6(1), this.b = i6(2), this.a = s5[3] ? i6(3) / 255 : 1) : (this.r = i6(0, 1), this.g = i6(2, 3), this.b = i6(4, 5), this.a = s5[6] ? i6(6, 7) / 255 : 1);
  }
  fromHsl({
    h: t3,
    s: s5,
    l: i6,
    a: h6
  }) {
    if (this._h = t3 % 360, this._s = s5, this._l = i6, this.a = typeof h6 == "number" ? h6 : 1, s5 <= 0) {
      const _ = r2(i6 * 255);
      this.r = _, this.g = _, this.b = _;
    }
    let e6 = 0, a3 = 0, g = 0;
    const n5 = t3 / 60, o5 = (1 - Math.abs(2 * i6 - 1)) * s5, f7 = o5 * (1 - Math.abs(n5 % 2 - 1));
    n5 >= 0 && n5 < 1 ? (e6 = o5, a3 = f7) : n5 >= 1 && n5 < 2 ? (e6 = f7, a3 = o5) : n5 >= 2 && n5 < 3 ? (a3 = o5, g = f7) : n5 >= 3 && n5 < 4 ? (a3 = f7, g = o5) : n5 >= 4 && n5 < 5 ? (e6 = f7, g = o5) : n5 >= 5 && n5 < 6 && (e6 = o5, g = f7);
    const b4 = i6 - o5 / 2;
    this.r = r2((e6 + b4) * 255), this.g = r2((a3 + b4) * 255), this.b = r2((g + b4) * 255);
  }
  fromHsv({
    h: t3,
    s: s5,
    v: i6,
    a: h6
  }) {
    this._h = t3 % 360, this._s = s5, this._v = i6, this.a = typeof h6 == "number" ? h6 : 1;
    const e6 = r2(i6 * 255);
    if (this.r = e6, this.g = e6, this.b = e6, s5 <= 0)
      return;
    const a3 = t3 / 60, g = Math.floor(a3), n5 = a3 - g, o5 = r2(i6 * (1 - s5) * 255), f7 = r2(i6 * (1 - s5 * n5) * 255), b4 = r2(i6 * (1 - s5 * (1 - n5)) * 255);
    switch (g) {
      case 0:
        this.g = b4, this.b = o5;
        break;
      case 1:
        this.r = f7, this.b = o5;
        break;
      case 2:
        this.r = o5, this.b = b4;
        break;
      case 3:
        this.r = o5, this.g = f7;
        break;
      case 4:
        this.r = b4, this.g = o5;
        break;
      case 5:
      default:
        this.g = o5, this.b = f7;
        break;
    }
  }
  fromHsvString(t3) {
    const s5 = m2(t3, d);
    this.fromHsv({
      h: s5[0],
      s: s5[1],
      v: s5[2],
      a: s5[3]
    });
  }
  fromHslString(t3) {
    const s5 = m2(t3, d);
    this.fromHsl({
      h: s5[0],
      s: s5[1],
      l: s5[2],
      a: s5[3]
    });
  }
  fromRgbString(t3) {
    const s5 = m2(t3, (i6, h6) => (
      // Convert percentage to number. e.g. 50% -> 128
      h6.includes("%") ? r2(i6 / 100 * 255) : i6
    ));
    this.r = s5[0], this.g = s5[1], this.b = s5[2], this.a = s5[3];
  }
};

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/colors/es/generate.es.js
var m3 = 2;
var f3 = 0.16;
var S = 0.05;
var c3 = 0.05;
var k = 0.15;
var p = 5;
var v = 4;
var w = [{
  index: 7,
  amount: 15
}, {
  index: 6,
  amount: 25
}, {
  index: 5,
  amount: 30
}, {
  index: 5,
  amount: 45
}, {
  index: 5,
  amount: 65
}, {
  index: 5,
  amount: 85
}, {
  index: 4,
  amount: 90
}, {
  index: 3,
  amount: 95
}, {
  index: 2,
  amount: 97
}, {
  index: 1,
  amount: 98
}];
function s2(t3, r6, a3) {
  var n5;
  return Math.round(t3.h) >= 60 && Math.round(t3.h) <= 240 ? n5 = a3 ? Math.round(t3.h) - m3 * r6 : Math.round(t3.h) + m3 * r6 : n5 = a3 ? Math.round(t3.h) + m3 * r6 : Math.round(t3.h) - m3 * r6, n5 < 0 ? n5 += 360 : n5 >= 360 && (n5 -= 360), n5;
}
function h2(t3, r6, a3) {
  if (t3.h === 0 && t3.s === 0)
    return t3.s;
  var n5;
  return a3 ? n5 = t3.s - f3 * r6 : r6 === v ? n5 = t3.s + f3 : n5 = t3.s + S * r6, n5 > 1 && (n5 = 1), a3 && r6 === p && n5 > 0.1 && (n5 = 0.1), n5 < 0.06 && (n5 = 0.06), Math.round(n5 * 100) / 100;
}
function x(t3, r6, a3) {
  var n5;
  return a3 ? n5 = t3.v + c3 * r6 : n5 = t3.v - k * r6, n5 = Math.max(0, Math.min(1, n5)), Math.round(n5 * 100) / 100;
}
function b(t3) {
  for (var r6 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, a3 = [], n5 = new H(t3), e6 = n5.toHsv(), u6 = p; u6 > 0; u6 -= 1) {
    var l5 = new H({
      h: s2(e6, u6, true),
      s: h2(e6, u6, true),
      v: x(e6, u6, true)
    });
    a3.push(l5);
  }
  a3.push(n5);
  for (var o5 = 1; o5 <= v; o5 += 1) {
    var g = new H({
      h: s2(e6, o5),
      s: h2(e6, o5),
      v: x(e6, o5)
    });
    a3.push(g);
  }
  return r6.theme === "dark" ? w.map(function(d4) {
    var M2 = d4.index, C2 = d4.amount;
    return new H(r6.backgroundColor || "#141414").mix(a3[M2], C2).toHexString();
  }) : a3.map(function(d4) {
    return d4.toHexString();
  });
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/rc-util/es/Dom/canUseDom.es.js
function e4() {
  return !!(typeof window < "u" && window.document && window.document.createElement);
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/rc-util/es/Dom/contains.es.js
function r3(e6, a3) {
  if (!e6)
    return false;
  if (e6.contains)
    return e6.contains(a3);
  for (var n5 = a3; n5; ) {
    if (n5 === e6)
      return true;
    n5 = n5.parentNode;
  }
  return false;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/rc-util/es/Dom/dynamicCSS.es.js
var h3 = "data-rc-order";
var y = "data-rc-priority";
var P = "rc-util-key";
var l = /* @__PURE__ */ new Map();
function b2() {
  var r6 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, e6 = r6.mark;
  return e6 ? e6.startsWith("data-") ? e6 : "data-".concat(e6) : P;
}
function s3(r6) {
  if (r6.attachTo)
    return r6.attachTo;
  var e6 = document.querySelector("head");
  return e6 || document.body;
}
function R(r6) {
  return r6 === "queue" ? "prependQueue" : r6 ? "prepend" : "append";
}
function m4(r6) {
  return Array.from((l.get(r6) || r6).children).filter(function(e6) {
    return e6.tagName === "STYLE";
  });
}
function A(r6) {
  var e6 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  if (!e4())
    return null;
  var n5 = e6.csp, a3 = e6.prepend, d4 = e6.priority, i6 = d4 === void 0 ? 0 : d4, u6 = R(a3), f7 = u6 === "prependQueue", t3 = document.createElement("style");
  t3.setAttribute(h3, u6), f7 && i6 && t3.setAttribute(y, "".concat(i6)), n5 != null && n5.nonce && (t3.nonce = n5 == null ? void 0 : n5.nonce), t3.innerHTML = r6;
  var o5 = s3(e6), c5 = o5.firstChild;
  if (a3) {
    if (f7) {
      var v3 = (e6.styles || m4(o5)).filter(function(g) {
        if (!["prepend", "prependQueue"].includes(g.getAttribute(h3)))
          return false;
        var C2 = Number(g.getAttribute(y) || 0);
        return i6 >= C2;
      });
      if (v3.length)
        return o5.insertBefore(t3, v3[v3.length - 1].nextSibling), t3;
    }
    o5.insertBefore(t3, c5);
  } else
    o5.appendChild(t3);
  return t3;
}
function T(r6) {
  var e6 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, n5 = s3(e6);
  return (e6.styles || m4(n5)).find(function(a3) {
    return a3.getAttribute(b2(e6)) === r6;
  });
}
function M(r6, e6) {
  var n5 = l.get(r6);
  if (!n5 || !r3(document, n5)) {
    var a3 = A("", e6), d4 = a3.parentNode;
    l.set(r6, d4), r6.removeChild(a3);
  }
}
function L(r6, e6) {
  var n5 = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, a3 = s3(n5), d4 = m4(a3), i6 = i2(i2({}, n5), {}, {
    styles: d4
  });
  M(a3, i6);
  var u6 = T(e6, i6);
  if (u6) {
    var f7, t3;
    if ((f7 = i6.csp) !== null && f7 !== void 0 && f7.nonce && u6.nonce !== ((t3 = i6.csp) === null || t3 === void 0 ? void 0 : t3.nonce)) {
      var o5;
      u6.nonce = (o5 = i6.csp) === null || o5 === void 0 ? void 0 : o5.nonce;
    }
    return u6.innerHTML !== r6 && (u6.innerHTML = r6), u6;
  }
  var c5 = A(r6, i6);
  return c5.setAttribute(b2(i6), e6), c5;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/rc-util/es/Dom/shadow.es.js
function n3(o5) {
  var t3;
  return o5 == null || (t3 = o5.getRootNode) === null || t3 === void 0 ? void 0 : t3.call(o5);
}
function u4(o5) {
  return n3(o5) instanceof ShadowRoot;
}
function d2(o5) {
  return u4(o5) ? n3(o5) : null;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/rc-util/es/warning.es.js
var t2 = {};
var i3 = [];
var f4 = function(n5) {
  i3.push(n5);
};
function d3(e6, n5) {
  if (!e6 && console !== void 0) {
    var r6 = i3.reduce(function(o5, c5) {
      return c5(o5 ?? "", "warning");
    }, n5);
    r6 && console.error("Warning: ".concat(r6));
  }
}
function l2(e6, n5) {
  if (!e6 && console !== void 0) {
    var r6 = i3.reduce(function(o5, c5) {
      return c5(o5 ?? "", "note");
    }, n5);
    r6 && console.warn("Note: ".concat(r6));
  }
}
function p2() {
  t2 = {};
}
function a2(e6, n5, r6) {
  !n5 && !t2[r6] && (e6(false, r6), t2[r6] = true);
}
function u5(e6, n5) {
  a2(d3, e6, n5);
}
function v2(e6, n5) {
  a2(l2, e6, n5);
}
u5.preMessage = f4;
u5.resetWarned = p2;
u5.noteOnce = v2;

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/utils.es.js
var import_react3 = __toESM(require_sg3_mf_2_project_mf_2_name_loadShare_react_loadShare());
function C(n5) {
  return n5.replace(/-(.)/g, function(t3, e6) {
    return e6.toUpperCase();
  });
}
function O(n5, t3) {
  u5(n5, "[@ant-design/icons] ".concat(t3));
}
function D(n5) {
  return o2(n5) === "object" && typeof n5.name == "string" && typeof n5.theme == "string" && (o2(n5.icon) === "object" || typeof n5.icon == "function");
}
function f5() {
  var n5 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
  return Object.keys(n5).reduce(function(t3, e6) {
    var i6 = n5[e6];
    switch (e6) {
      case "class":
        t3.className = i6, delete t3.class;
        break;
      default:
        delete t3[e6], t3[C(e6)] = i6;
    }
    return t3;
  }, {});
}
function m5(n5, t3, e6) {
  return e6 ? import_react3.default.createElement(n5.tag, i2(i2({
    key: t3
  }, f5(n5.attrs)), e6), (n5.children || []).map(function(i6, r6) {
    return m5(i6, "".concat(t3, "-").concat(n5.tag, "-").concat(r6));
  })) : import_react3.default.createElement(n5.tag, i2({
    key: t3
  }, f5(n5.attrs)), (n5.children || []).map(function(i6, r6) {
    return m5(i6, "".concat(t3, "-").concat(n5.tag, "-").concat(r6));
  }));
}
function L2(n5) {
  return b(n5)[0];
}
function N(n5) {
  return n5 ? Array.isArray(n5) ? n5 : [n5] : [];
}
var S2 = `
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`;
var U = function(t3) {
  var e6 = (0, import_react3.useContext)(o3), i6 = e6.csp, r6 = e6.prefixCls, o5 = e6.layer, a3 = S2;
  r6 && (a3 = a3.replace(/anticon/g, r6)), o5 && (a3 = "@layer ".concat(o5, ` {
`).concat(a3, `
}`)), (0, import_react3.useEffect)(function() {
    var u6 = t3.current, p3 = d2(u6);
    L(a3, "@ant-design-icons", {
      prepend: !o5,
      csp: i6,
      attachTo: p3
    });
  }, []);
};

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/components/IconBase.es.js
var I = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"];
var n4 = {
  primaryColor: "#333",
  secondaryColor: "#E6E6E6",
  calculated: false
};
function N2(l5) {
  var o5 = l5.primaryColor, e6 = l5.secondaryColor;
  n4.primaryColor = o5, n4.secondaryColor = e6 || L2(o5), n4.calculated = !!e6;
}
function b3() {
  return i2({}, n4);
}
var i4 = function(o5) {
  var e6 = o5.icon, y2 = o5.className, d4 = o5.onClick, f7 = o5.style, t3 = o5.primaryColor, u6 = o5.secondaryColor, g = u(o5, I), s5 = T2.useRef(), c5 = n4;
  if (t3 && (c5 = {
    primaryColor: t3,
    secondaryColor: u6 || L2(t3)
  }), U(s5), O(D(e6), "icon should be icon definiton, but got ".concat(e6)), !D(e6))
    return null;
  var r6 = e6;
  return r6 && typeof r6.icon == "function" && (r6 = i2(i2({}, r6), {}, {
    icon: r6.icon(c5.primaryColor, c5.secondaryColor)
  })), m5(r6.icon, "svg-".concat(r6.name), i2(i2({
    className: y2,
    onClick: d4,
    style: f7,
    "data-icon": r6.name,
    width: "1em",
    height: "1em",
    fill: "currentColor",
    "aria-hidden": "true"
  }, g), {}, {
    ref: s5
  }));
};
i4.displayName = "IconReact";
i4.getTwoToneColors = b3;
i4.setTwoToneColors = N2;

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.es.js
function s4(o5) {
  var l5 = N(o5), r6 = n2(l5, 2), n5 = r6[0], a3 = r6[1];
  return i4.setTwoToneColors({
    primaryColor: n5,
    secondaryColor: a3
  });
}
function c4() {
  var o5 = i4.getTwoToneColors();
  return o5.calculated ? [o5.primaryColor, o5.secondaryColor] : o5.primaryColor;
}

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/components/AntdIcon.es.js
var h5 = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];
s4(f2.primary);
var i5 = r4.forwardRef(function(o5, f7) {
  var T3 = o5.className, e6 = o5.icon, x4 = o5.spin, a3 = o5.rotate, p3 = o5.tabIndex, m6 = o5.onClick, u6 = o5.twoToneColor, w2 = u(o5, h5), l5 = r4.useContext(o3), c5 = l5.prefixCls, t3 = c5 === void 0 ? "anticon" : c5, v3 = l5.rootClassName, y2 = h(v3, t3, i(i({}, "".concat(t3, "-").concat(e6.name), !!e6.name), "".concat(t3, "-spin"), !!x4 || e6.name === "loading"), T3), n5 = p3;
  n5 === void 0 && m6 && (n5 = -1);
  var I2 = a3 ? {
    msTransform: "rotate(".concat(a3, "deg)"),
    transform: "rotate(".concat(a3, "deg)")
  } : void 0, _ = N(u6), s5 = n2(_, 2), b4 = s5[0], g = s5[1];
  return r4.createElement("span", e({
    role: "img",
    "aria-label": e6.name
  }, w2, {
    ref: f7,
    tabIndex: n5,
    onClick: m6,
    className: y2
  }), r4.createElement(i4, {
    icon: e6,
    primaryColor: b4,
    secondaryColor: g,
    style: I2
  }));
});
i5.displayName = "AntdIcon";
i5.getTwoToneColor = c4;
i5.setTwoToneColor = s4;

// node_modules/@euroland/react-antd-draggable-select/dist/node_modules/@ant-design/icons/es/icons/CloseOutlined.es.js
var f6 = function(o5, r6) {
  return e5.createElement(i5, e({}, o5, {
    ref: r6,
    icon: c
  }));
};
var l4 = e5.forwardRef(f6);
l4.displayName = "CloseOutlined";

// node_modules/@euroland/react-antd-draggable-select/dist/libs/ReactAntdDraggableSelect.es.js
var { Option: G } = import_antd.Select;
var H2 = {
  rect: rectSortingStrategy,
  vertical: verticalListSortingStrategy,
  horizontal: horizontalListSortingStrategy
};
var J = ({
  defaultValue: v3,
  value: n5,
  options: c5 = [],
  onChange: l5,
  style: y2 = { width: "100%" },
  showSearch: u6 = false,
  caseSensitiveSearch: S3 = false,
  disabled: f7 = false,
  sortingStrategy: h6 = "rect",
  allowClear: b4 = false
}) => {
  const [a3, p3] = (0, import_react4.useState)(
    n5 || v3 || []
  );
  (0, import_react4.useEffect)(() => {
    n5 !== void 0 && p3(n5);
  }, [n5]);
  const D2 = (0, import_react4.useCallback)(
    (e6) => {
      const { active: t3, over: s5 } = e6;
      if (!s5 || t3.id === s5.id) return;
      const i6 = a3.slice(), d4 = i6.indexOf(t3.id), m6 = i6.indexOf(s5.id);
      if (d4 !== -1 && m6 !== -1) {
        const o5 = [...i6];
        o5.splice(d4, 1), o5.splice(m6, 0, t3.id), p3(o5), l5 == null || l5(o5, c5);
      }
    },
    [a3, l5, c5]
  ), I2 = (e6) => {
    p3(e6), l5 == null || l5(e6, c5);
  }, w2 = (e6, t3) => {
    if (!u6 || !t3) return false;
    const s5 = S3 ? e6 : e6.toLowerCase();
    return (S3 ? t3.children : t3.children.toLowerCase()).includes(s5);
  }, O2 = ({ label: e6, value: t3, closable: s5, onClose: i6 }) => {
    const {
      attributes: d4,
      listeners: m6,
      setNodeRef: o5,
      transform: g,
      transition: L3,
      isDragging: N3
    } = useSortable({
      id: t3,
      disabled: f7
    }), R2 = {
      opacity: N3 ? 0.5 : 1,
      transform: g ? `translate3d(${g.x}px, ${g.y}px, 0)` : void 0,
      transition: L3,
      cursor: f7 ? "not-allowed" : "move",
      marginRight: 3,
      display: "inline-block"
    };
    return (0, import_jsx_runtime.jsx)(
      "div",
      {
        ref: o5,
        style: R2,
        onMouseDown: (k2) => k2.stopPropagation(),
        title: e6,
        children: (0, import_jsx_runtime.jsxs)("span", { className: "ant-select-selection-item", children: [
          (0, import_jsx_runtime.jsx)(
            "span",
            {
              className: "ant-select-selection-item-content",
              ...d4,
              ...m6,
              children: e6
            }
          ),
          s5 && (0, import_jsx_runtime.jsx)(
            "span",
            {
              className: "ant-select-selection-item-remove",
              onClick: i6,
              "data-testid": "remove-item",
              children: (0, import_jsx_runtime.jsx)(l4, {})
            }
          )
        ] })
      }
    );
  };
  return (0, import_jsx_runtime.jsx)(DndContext, { onDragEnd: D2, children: (0, import_jsx_runtime.jsx)(
    SortableContext,
    {
      items: a3,
      strategy: H2[h6],
      children: (0, import_jsx_runtime.jsx)(
        import_antd.Select,
        {
          mode: "multiple",
          style: y2,
          value: a3,
          onChange: I2,
          showSearch: u6,
          disabled: f7,
          filterOption: w2,
          optionFilterProp: "children",
          tagRender: (e6) => (0, import_jsx_runtime.jsx)(O2, { ...e6 }),
          allowClear: b4,
          children: c5.map((e6) => (0, import_jsx_runtime.jsx)(G, { value: e6.value, children: e6.label }, e6.value))
        }
      )
    }
  ) });
};
var Y = J;
export {
  Y as ReactAntdDraggableSelect
};
/*! Bundled license information:

classnames/index.es.js:
  (*!
    Copyright (c) 2018 Jed Watson.
    Licensed under the MIT License (MIT), see
    http://jedwatson.github.io/classnames
  *)
*/
//# sourceMappingURL=@euroland_react-antd-draggable-select.js.map
