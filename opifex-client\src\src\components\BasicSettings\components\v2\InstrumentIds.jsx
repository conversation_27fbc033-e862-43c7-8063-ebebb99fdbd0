import {  useEffect, useMemo, useState } from "react";
import { Form, Input, Modal, Select, Table } from "antd";
import { useInstrumentsSetting } from "../../../../hooks/useSettings";
import useAppSelector from "../../../../hooks/useAppSelector";
import fuzzysort from 'fuzzysort'
const columns = [
  {
    title: "ID",
    dataIndex: "id",
  },
  {
    title: "Real time Data",
    dataIndex: "allowRealTime",
  },
  {
    title: "Name",
    dataIndex: "name",
  },
  {
    title: "Market name",
    dataIndex: "marketName",
  },
  {
    title: "Market Id",
    dataIndex: "marketId",
  },
  {
    title: "Time Zone",
    dataIndex: "timeZone",
  },
  {
    title: "ISIN",
    dataIndex: "isin",
  },
  {
    title: "Symbol",
    dataIndex: "symbol",
  },
];

const ModalInstrumentInfos = ({ open, onOk, onCancel, value: initValue, onChange }) => {
  const [{ instruments, loading }] = useInstrumentsSetting();
  const [search, searchSet] = useState('')
  const [value, valueSet] = useState(initValue);
  const filteredInstruments = useMemo(() => {
    if (!instruments || instruments.length === 0) return instruments;
    if (!search || search.length < 2) return instruments;

    return fuzzysort
      .go(
        search,
        instruments.map(item => ({
          ...item,
          keySearch: `${item.id} ${item.name} ${item.marketName} ${item.isin} ${item.symbol}`,
        })),
        { key: ["keySearch"] }
      )
      .map(item => item.obj);
  }, [search, instruments]);
  console.log({instruments});
  
  useEffect(() => {
    if (open) {
      valueSet(initValue);
    }
  }, [initValue, open]);

  const dataSource = useMemo(
    () =>
      filteredInstruments.map(item => ({
        ...item,
        key: item.id,
        allowRealTime: item.allowRealTime ? "Yes" : "No",
      })),
    [filteredInstruments]
  );

  const rowSelection = {
    selectedRowKeys: value,
    onSelect: (record, selected, selectedRows) => {
      valueSet(selectedRows.map(item => item.id));
    },
    onSelectAll: (selected, selectedRows) => {
      valueSet(selectedRows.map(item => item.id));
    },
  };
  const handleOk = () => {
    onChange(value);
    onOk();
  };
  return (
    <Modal
      centered
      width={1200}
      title="Instrument information"
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      loading={loading}
    >
      <div className="flex">
        <Input
          className="ml-auto"
          placeholder="Search..."
          value={search}
          style={{maxWidth: 300}}
          onChange={({target: {value }}) => searchSet(value)}
        />
      </div>
      <Table rowSelection={{ type: "checkbox", ...rowSelection }} columns={columns} dataSource={dataSource} />
    </Modal>
  );
};

const InstrumentIds = ({ value, id, onChange }) => {
  const form = Form.useFormInstance();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const companyCode = useAppSelector(state => state.forms.availableTools.initState?.companyCode);

  const handleOk = () => setIsModalOpen(false);
  const handleCancel = () => setIsModalOpen(false);

  const [{ instruments, companyCode: searchedCompanyCode }, { getInstrumentSetting }] =
    useInstrumentsSetting();

  const handleDropdownVisibleChange = async open => {
    if (open) {
      setIsModalOpen(true);
    }
  };

  useEffect(() => {
    if (!instruments?.length) return;
    if(!value?.length) {
      form.setFieldValue('timezone', '');
      return;
    };
    const selectedInstrument = instruments.find(inst => inst.id === value?.[0]);
    if (!selectedInstrument?.timeZone) return;
    const currentTimezone = form.getFieldValue('timezone');
    const newTimezone = selectedInstrument.timeZone;
    if (currentTimezone !== newTimezone) {
      form.setFieldValue('timezone', newTimezone);
    }
  }, [value, instruments, form]);

  useEffect(() => {
    if(!companyCode) return;
    if (searchedCompanyCode !== companyCode) getInstrumentSetting({ companyCode });
  }, [companyCode])
  return (<>
    <Select
      mode="multiple"
      style={{
        width: "100%",
      }}
      disabled={!companyCode}
      showSearch={false}
      placeholder="Please select"
      options={instruments.map(({ id }) => ({ label: id, value: id }))}
      dropdownStyle={{ display: "none" }}
      onDropdownVisibleChange={handleDropdownVisibleChange}
      id={id}
      value={value}
      onChange={onChange}
    />
    <ModalInstrumentInfos
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      onChange={onChange}
      value={value}
    />
  </>);
};

export default InstrumentIds;
