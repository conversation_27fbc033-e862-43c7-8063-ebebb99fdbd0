import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";
import { Form } from "antd";
import { useContext, useMemo } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { useGroupNames } from "../../hooks/useGroupNames";
import { VALIDATION_RULES } from "./constants";

const InstrumentGroupListing = ({
  form,
  instrumentGroups,
  availableLanguages,
  instrumentIdDefaults,
}) => {
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds = [], instrument } = basicSettings;
  const { getGroupName } = useGroupNames({ form, availableLanguages });

  const instrumentOptions = useMemo(() => {
    const availableInstruments =
      instrumentIds.length > 0 ? instrumentIds : instrumentIdDefaults;
    return availableInstruments.map((id) => ({
      value: id,
      label: <>{id} {instrument[id]?.marketName ?? ""}</>,
    }));
  }, [instrumentIds, instrumentIdDefaults, instrument]);

  const filterOption = useMemo(() => {
    return (input, option) =>
      option?.label?.toLowerCase().includes(input.toLowerCase());
  }, []);

  if (instrumentGroups.length === 0) {
    return null;
  }

  return (
    <div className="instrument-group-listing">
      {instrumentGroups.map((groupId, index) => {
        const groupName = getGroupName(groupId, index);

        return (
          <Form.Item
            key={`group-instruments-${groupId}`}
            name={["InstrumentGroups", index, "InstrumentIDs"]}
            label={`Instruments for Group ${index + 1}`}
            rules={[VALIDATION_RULES.INSTRUMENTS_REQUIRED]}
          >
            <ReactAntdDraggableSelect
              sortingStrategy="vertical"
              mode="multiple"
              placeholder={`Select instruments for ${groupName}`}
              options={instrumentOptions}
              showSearch
              filterOption={filterOption}
              aria-label={`Select instruments for ${groupName}`}
              notFoundContent="No instruments available"
            />
          </Form.Item>
        );
      })}
    </div>
  );
};

export default InstrumentGroupListing;
