import queryString from "query-string";
import { getAxiosInstance } from "@euroland/opifix-utils";

import { isObjectEmpty } from "../utils/common";
import { ACCOUNT_KEY } from "../constant/commom";

const handleExpiredSession = (errorCode) => {
  switch (errorCode) {
    case 401:
      localStorage.removeItem(ACCOUNT_KEY);
      break;
    default:
      break;
  }
};

const getAxios = async (path) => {
  try {
    const api = getAxiosInstance();
    return await api.get(path);
  } catch (error) {
    handleExpiredSession(error.response?.status);
    throw error;
  }
};

const postAxios = async (path, params = {}) => {
  try {
    const api = getAxiosInstance();
    return await api.post(path, params);
  } catch (error) {
    handleExpiredSession(error.response?.status);
    throw error;
  }
};

const deleteAxios = async (path) => {
  try {
    const api = getAxiosInstance();
    return await api.delete(path);
  } catch (error) {
    handleExpiredSession(error.response?.status);
    throw error;
  }
};

const apiGet = async (path, params = {}) => {
  if (isObjectEmpty(params)) {
    return await getAxios(path);
  }
  const query = queryString.stringify(params);
  return await getAxios(`${path}?${query}`);
};

const apiPost = (path, params = {}) => {
  return postAxios(path, params);
};

const apiDelete = (path, params = {}) => {
  return deleteAxios(`${path}/${params}`);
};

export default {
  GET: (path, params) => apiGet(path, params),
  POST: (path, params) => apiPost(path, params),
  DELETE: (path, params) => apiDelete(path, params),
};
