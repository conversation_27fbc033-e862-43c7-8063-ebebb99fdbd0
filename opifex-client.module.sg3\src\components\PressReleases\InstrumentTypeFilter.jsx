import { useState, useEffect, useContext } from "react";
import { Button, Flex, Form, Table, Tooltip } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { useInputStyle } from "../../hooks/useInputStyle";
import { SettingsContext } from "../../context/SettingsContext";
import { generateUniqueKey, getMessageTypeOptions, parseSourceMessageTypeString } from "../../utils/common";
import CustomInputTableCell from "../common/CustomInputTableCell";
const InstrumentTypeFilter = ({ onChange, form, sources, instrumentId }) => {
  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const [typeFilterDatas, setTypeFilterDatas] = useState([]);
  
  const sourceOptions = Object.values(sources)?.map((source) => ({
    label: source?.name,
    value: source?.id?.toString(),
  }));

  const editTypeFilterForm = Form.useWatch(
    ['InstrumentPressReleases', instrumentId, 'EditTypeFilter'],
    { form, preserve: true }
  );


  /**
   * Effect to handle type filter updates for instrument press releases.
   */
  useEffect(() => {
    const typeFilterArray = [];
    if (editTypeFilterForm) {
      Object.keys(editTypeFilterForm).forEach((key) => {
        const item = editTypeFilterForm[key];
        if (item?.Source && item?.MessageType) {
          // Clear invalid message types
          const source = sources[item.Source];
          if (source?.messageTypes && !source.messageTypes.some(mt => mt.messageTypeId?.toString() === item.MessageType)) {
            form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilter', key, 'MessageType'], undefined);
            return;
          }
          typeFilterArray.push(`${item.Source}|${item.MessageType}`);
        }
      });
    }
    const typeFilterString = typeFilterArray.join(';');
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'TypeFilter'], typeFilterString);
    onChange && onChange();
  }, [editTypeFilterForm, sources, form, onChange, instrumentId]);

  /**
   * Effect to initialize type filter data for instrument press releases.
   */
  useEffect(() => {
    const initState = settings?.initState?.Instruments?.Instrument?.find(
      inst => inst.Id?.toString() === instrumentId?.toString()
    );
    
    if (!form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys'])) {
      const editTypeFilter = parseSourceMessageTypeString(
        initState?.PressRelease?.TypeFilter || ''
      )
        ?.filter((item) => Object.keys(sources || {}).includes(item.Source))
        ?.reduce(
          (editTypeFilterObj, item) => ({ ...editTypeFilterObj, [generateUniqueKey()]: item }),
          {}
        );
      const typeFilterKeys = Object.keys(editTypeFilter || {});
      const initTypeFilterDatas = typeFilterKeys?.map((itemKey) => ({
        key: itemKey,
        value: editTypeFilter[itemKey],
      }));

      form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilter'], editTypeFilter);
      form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys'], typeFilterKeys);
      setTypeFilterDatas(initTypeFilterDatas || []);
      onChange && onChange();
    } else {
      const typeFilterKeys = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys']);
      const typeFilterDatas = typeFilterKeys?.map((itemKey) => ({
        key: itemKey,
        value: editTypeFilterForm?.[itemKey],
      }));
      setTypeFilterDatas(typeFilterDatas || []);
    }
  }, [sources, settings, form, onChange, instrumentId, editTypeFilterForm]);

  const addTypeFilter = () => {
    const newKey = generateUniqueKey();
    const currentKeys = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys']) || [];
    const newKeys = [...currentKeys, newKey];
    
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys'], newKeys);
    setTypeFilterDatas(prev => [...prev, { key: newKey, value: {} }]);
  };

  const removeTypeFilter = (keyToRemove) => {
    const currentKeys = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys']) || [];
    const newKeys = currentKeys.filter(key => key !== keyToRemove);
    
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilterKeys'], newKeys);
    
    const currentEditData = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilter']) || {};
    const newEditData = { ...currentEditData };
    delete newEditData[keyToRemove];
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditTypeFilter'], newEditData);
    
    setTypeFilterDatas(prev => prev.filter(item => item.key !== keyToRemove));
  };

  const typeFilterColumns = [
    {
      title: 'Source',
      align: 'left',
      minWidth: 300,
      dataIndex: '',
      key: '',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={sourceOptions}
          form={form}
          name={[
            'InstrumentPressReleases',
            instrumentId,
            'EditTypeFilter',
            `${record.key}`,
            'Source',
          ]}
          rules={[{ required: true, message: 'Source is required' }]}
          defaultValue={record.value?.['Source']}
          isDirectEdit={true}
        />
      ),
    },
    {
      title: 'Message Type',
      align: 'left',
      minWidth: 300,
      dataIndex: '',
      key: '',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={getMessageTypeOptions(sources, record.value?.['Source'])}
          form={form}
          name={[
            'InstrumentPressReleases',
            instrumentId,
            'EditTypeFilter',
            `${record.key}`,
            'MessageType',
          ]}
          rules={[{ required: true, message: 'Message Type is required' }]}
          defaultValue={record.value?.['MessageType']}
          isDirectEdit={true}
        />
      ),
    },
    {
      title: 'Action',
      align: 'center',
      width: 100,
      fixed: !isMobile ? 'right' : false,
      dataIndex: '',
      key: '',
      render: (_, record) => (
        <Tooltip title={`Delete this type filter`}>
          <Button
            type="primary"
            danger
            onClick={() => removeTypeFilter(record.key)}
            icon={<DeleteOutlined />}
          />
        </Tooltip>
      ),
    },
  ];

  return (
    <>
      <Flex justify="flex-start" align="center" className="mb-5">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addTypeFilter}
          >
            Add Type Filter
          </Button>
        </Flex>
        <Table
          columns={typeFilterColumns}
          dataSource={typeFilterDatas}
          size={isMobile ? 'small' : 'default'}
          bordered
          pagination={false}
          style={{ width: '100%', marginTop: 16, marginBottom: 24 }}
          scroll={{ x: 'max-content', y: 400 }}
          tableLayout="auto"
          rowKey="key"
        />
    </>
  );
};

export default InstrumentTypeFilter;
