import { FORM_FIELD_NAMES, DEFAULT_GROUP_NAME } from "../components/InstrumentGroups/constants";

/**
 * Utility functions for form operations
 */

/**
 * Get current form data in a structured format
 * @param {Object} form - Ant Design form instance
 * @returns {Object} Current form data
 */
export const getFormData = (form) => ({
  keys: form.getFieldValue(FORM_FIELD_NAMES.INSTRUMENT_GROUP_KEYS) || [],
  groups: form.getFieldValue(FORM_FIELD_NAMES.INSTRUMENT_GROUPS) || [],
  phrases: form.getFieldValue(FORM_FIELD_NAMES.EDIT_CUSTOM_PHRASES) || {},
});

/**
 * Check if form has no data
 * @param {Object} formData - Form data object
 * @returns {boolean} True if form has no data
 */
export const hasNoFormData = (formData) => {
  const { keys, groups, phrases } = formData;
  return (
    keys.length === 0 &&
    groups.length === 0 &&
    Object.keys(phrases).length === 0
  );
};

/**
 * Create default phrases for a group
 * @param {string} groupId - Group ID
 * @param {Array} availableLanguages - Available languages
 * @param {boolean} useDefaultName - Whether to use default name
 * @returns {Object} Default phrases object
 */
export const createDefaultPhrases = (groupId, availableLanguages, useDefaultName = false) => {
  const phrases = {};
  
  if (availableLanguages.length > 0) {
    phrases[groupId] = {};
    availableLanguages.forEach((lang) => {
      phrases[groupId][lang] = useDefaultName ? DEFAULT_GROUP_NAME : "";
    });
  }
  
  return phrases;
};

/**
 * Update form field values
 * @param {Object} form - Ant Design form instance
 * @param {Array} keys - Group keys
 * @param {Array} groups - Group data
 * @param {Object} phrases - Phrases data
 */
export const updateFormFields = (form, keys, groups, phrases) => {
  form.setFieldValue(FORM_FIELD_NAMES.INSTRUMENT_GROUP_KEYS, keys);
  form.setFieldValue(FORM_FIELD_NAMES.INSTRUMENT_GROUPS, groups);
  form.setFieldValue(FORM_FIELD_NAMES.EDIT_CUSTOM_PHRASES, phrases);
};

/**
 * Validate and sync form data consistency
 * @param {Object} form - Ant Design form instance
 * @param {Object} formData - Current form data
 * @param {Array} availableLanguages - Available languages
 * @param {Array} instrumentIdDefaults - Default instrument IDs
 * @param {boolean} shouldSetDefaults - Whether to set defaults
 * @param {boolean} defaultGroupCreated - Whether default group was created
 * @returns {boolean} True if changes were made
 */
export const syncFormDataConsistency = (
  form,
  formData,
  availableLanguages,
  instrumentIdDefaults,
  shouldSetDefaults = false,
  defaultGroupCreated = false
) => {
  const { keys, groups, phrases } = formData;
  let hasChanges = false;

  if (keys.length !== groups.length) {
    const syncedGroups = keys.map((_, index) => {
      if (groups[index]) {
        return groups[index];
      }
      return {
        [FORM_FIELD_NAMES.INSTRUMENT_IDS[0]]:
          index === 0 && shouldSetDefaults ? instrumentIdDefaults || [] : [],
      };
    });
    form.setFieldValue(FORM_FIELD_NAMES.INSTRUMENT_GROUPS, syncedGroups);
    hasChanges = true;
  }

  if (availableLanguages.length > 0) {
    const updatedPhrases = {};
    
    Object.keys(phrases || {})?.forEach(groupId => {
      updatedPhrases[groupId] = { ...phrases[groupId] };
    });
    
    let phrasesUpdated = false;

    keys.forEach((groupId, index) => {
      if (!updatedPhrases[groupId]) {
        updatedPhrases[groupId] = {};
        phrasesUpdated = true;
      }

      availableLanguages.forEach((lang) => {
        if (updatedPhrases[groupId][lang] === undefined) {
          const shouldSetDefaultName =
            index === 0 && defaultGroupCreated && shouldSetDefaults;
          updatedPhrases[groupId][lang] = shouldSetDefaultName
            ? DEFAULT_GROUP_NAME
            : "";
          phrasesUpdated = true;
        }
      });
    });

    if (phrasesUpdated) {
      form.setFieldValue(FORM_FIELD_NAMES.EDIT_CUSTOM_PHRASES, updatedPhrases);
      hasChanges = true;
    }
  }

  return hasChanges;
};