import { Col, Form, InputNumber, Row } from "antd";
import { useEffect, useImperativeHandle } from "react";

import AvailableLanguages from "../BasicSettings/components/v2/AvailableLanguages";
import InstrumentIds from "../BasicSettings/components/v2/InstrumentIds";
import Timezone from "../BasicSettings/components/v2/Timezone";
import InstrumentLocalizations from "../BasicSettings/components/v2/InstrumentLocalizations";
import InstrumentConfigurations from "../BasicSettings/components/v2/InstrumentConfigurations";
import ColorPaletteGenerator from "./commons/ColorPaletteGenerator";
import FormatByLanguage from "../BasicSettings/components/v2/FormatByLanguage";
import useDebounce from "../../hooks/useDebounce";

const BasicSettings = ({ handleRef, onDraftChange, initState }) => {
  const [form] = Form.useForm();
  const instrument = Form.useWatch("instrument", form);

  const onFormLayoutChange = () => {
    const cloneValues = structuredClone(form.getFieldsValue(true));

    if (cloneValues.instrument) {
      Object.keys(cloneValues.instrument).map(id => {
        const instrument = cloneValues.instrument[id];
        if (!instrument) return;
        const instrumentFields = Object.keys(instrument);

        instrumentFields.forEach(key => {
          if (instrument[key] === undefined) delete instrument[key];
        });

        if (Object.keys(instrument).length === 0) {
          delete cloneValues.instrument[id];
        }
      });
    }
    onDraftChange(cloneValues);
  };

  const onChangeInstrumentField = useDebounce(onFormLayoutChange);

  useEffect(() => {
    onChangeInstrumentField();
  }, [instrument]);

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      onFormLayoutChange(); // ensure everything is up to date
      return true;
    } catch (errorInfo) {
      console.log("Failed:", errorInfo);
      return false;
    }
  };

  useImperativeHandle(handleRef, () => ({ onSubmit: handleSubmit }), []);

  return (
    <Form
      className="basic-setting"
      layout={"vertical"}
      form={form}
      onValuesChange={onFormLayoutChange}
      initialValues={initState}
    >
      <Form.Item name="brandingColor">
        <ColorPaletteGenerator />
      </Form.Item>
      <Row gutter={16}>
        <Col lg={6} sm={12} xs={24}>
          <Form.Item
            name="availableLanguages"
            label="Available languages"
            required
            rules={[{ required: true }]}
          >
            <AvailableLanguages />
          </Form.Item>
        </Col>
        <Col lg={6} sm={12} xs={24}>
          <Form.Item name="instrumentIds" label="Instrument ids" required rules={[{ required: true }]}>
            <InstrumentIds />
          </Form.Item>
        </Col>
        <Col lg={6} sm={12} xs={24}>
          <Form.Item name="timezone" label="Time zone">
            <Timezone />
          </Form.Item>
        </Col>
        <Col lg={6} sm={12} xs={24}>
          <Form.Item name="dataRefreshRate" label="Data refresh rate" required rules={[{ required: true }]}>
            <InputNumber min={1} />
          </Form.Item>
        </Col>
      </Row>

      <FormatByLanguage />
      <Form.Item label="Instrument Localizations" name="instrument">
        <InstrumentLocalizations />
      </Form.Item>
      <Form.Item label="Instrument Configurations">
        <InstrumentConfigurations />
      </Form.Item>
    </Form>
  );
};

export default BasicSettings;
