import { useState, useEffect, useContext, useCallback } from "react";
import { Button, Flex, Form, Table, Tooltip } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import CustomInputTableCell from "../common/CustomInputTableCell";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";
import {
  createUniqueKeyGenerator,
  formatSourceMessageTypeToString,
  getMessageTypeOptions,
  parseSourceMessageTypeString,
} from "../../utils/common";
import { useMemo } from "react";
import LazyLoad from "../common/LazyLoading";
import SkeletonTable from "./SkeletionTable";

const generateUniqueKey = createUniqueKeyGenerator("ExTF");

const ExcludedTypeFilter = ({
  onChange,
  form,
  sources,
  fieldPrefix = ["PressReleases"],
}) => {
  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const [excludedTypeFilterDatas, setExcludedTypeFilterDatas] = useState([]);
  const sourceOptions = Object.values(sources)?.map((source) => ({
    label: source?.name,
    value: source?.id?.toString(),
  }));

  const editExcludedTypeFilterForm = Form.useWatch(
    [...fieldPrefix, "EditExcludedTypeFilter"],
    { form, preserve: true }
  );

  /**
   * Effect to handle excluded type filter updates for press releases.
   * Processes the editExcludedTypeFilterForm data to:
   * 1. Create an array of source and excluded message type pairs
   * 2. Validate each pair against available sources and message types
   * 3. Clear invalid excluded message types
   * 4. Format and update the form with the final excluded type filter string
   *
   * @effect
   * @dependencies {Object} editExcludedTypeFilterForm - Form data containing source and message type selections
   * @dependencies {Object} sources - Available sources with their message types
   * @dependencies {Function} form.setFieldValue - Antd form setter function
   * @dependencies {Function} onChange - Optional callback function to update the parent form
   */
  useEffect(() => {
    const excludedTypeFilterArray = [];
    const sourcesArray = Object.values(sources);
    Object.keys(editExcludedTypeFilterForm || {})
      ?.filter((itemKey) => !!editExcludedTypeFilterForm[itemKey])
      ?.forEach((itemKey) => {
        const sourceExcludedMessageType = {
          Source: editExcludedTypeFilterForm[itemKey]?.Source || "",
          ExcludedMessageType: "",
        };
        if (
          sourcesArray.find(
            (src) =>
              src.id?.toString() ===
                editExcludedTypeFilterForm[itemKey]?.Source &&
              src.messageTypes?.find(
                (mst) =>
                  mst.messageTypeId?.toString() ===
                  editExcludedTypeFilterForm[itemKey]?.ExcludedMessageType
              )
          )
        ) {
          sourceExcludedMessageType["ExcludedMessageType"] =
            editExcludedTypeFilterForm[itemKey]?.ExcludedMessageType || "";
        } else {
          form.setFieldValue(
            [
              "PressReleases",
              "EditExcludedTypeFilter",
              `${itemKey}`,
              "ExcludedMessageType",
            ],
            ""
          );
        }
        excludedTypeFilterArray.push(sourceExcludedMessageType);
      });
    const newExcludedTypeFilter = formatSourceMessageTypeToString(
      excludedTypeFilterArray,
      "ExcludedMessageType"
    );
    form.setFieldValue(
      ["PressReleases", "ExcludedTypeFilter"],
      newExcludedTypeFilter
    );
    onChange && onChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editExcludedTypeFilterForm]);

  /**
   * Effect to initialize excluded type filter data for press releases.
   * Handles two scenarios:
   * 1. Initial setup: When EditExcludedTypeFilterKeys is not present
   *    - Parses ExcludedTypeFilter string into structured data
   *    - Filters sources against available sources
   *    - Generates unique keys for each filter entry
   *    - Updates form with initial filter data and keys
   * 2. Existing setup: When EditExcludedTypeFilterKeys exists
   *    - Maps existing filter keys to their corresponding values
   *
   * @effect
   * @dependencies {Object} settings.initState.PressReleases - Initial press release settings
   * @dependencies {Object} sources - Available sources configuration
   * @dependencies {Function} form.setFieldValue - Antd form setter function
   * @dependencies {Function} setExcludedTypeFilterDatas - State setter for excluded type filter data
   * @dependencies {Function} onChange - Optional callback function to update the parent form
   */
  useEffect(() => {
    if (!settings?.initState?.PressReleases?.EditExcludedTypeFilterKeys) {
      const editExcludedTypeFilter = parseSourceMessageTypeString(
        settings?.initState?.PressReleases?.ExcludedTypeFilter,
        "ExcludedMessageType"
      )
        ?.filter((item) => Object.keys(sources || {}).includes(item.Source))
        ?.reduce(
          (editExcludedTypeFilterObj, item) => ({
            ...editExcludedTypeFilterObj,
            [generateUniqueKey()]: item,
          }),
          {}
        );
      const excludedTypeFilterKeys = Object.keys(editExcludedTypeFilter);
      const initExcludedTypeFilterDatas = excludedTypeFilterKeys?.map(
        (itemKey) => ({
          key: itemKey,
          value: editExcludedTypeFilter[itemKey],
        })
      );

      form.setFieldValue(
        ["PressReleases", "EditExcludedTypeFilterKeys"],
        excludedTypeFilterKeys
      );
      form.setFieldValue(
        ["PressReleases", "EditExcludedTypeFilter"],
        editExcludedTypeFilter
      );
      setExcludedTypeFilterDatas(initExcludedTypeFilterDatas);

      onChange && onChange();
    } else {
      setExcludedTypeFilterDatas(
        settings?.initState?.PressReleases?.EditExcludedTypeFilterKeys?.map(
          (itemKey) => ({
            key: itemKey,
            value:
              settings?.initState?.PressReleases?.EditExcludedTypeFilter?.[
                itemKey
              ],
          })
        )
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * Handles adding a new excluded type filter to the form.
   * 1. Generates a unique key for the new filter entry
   * 2. Adds the new filter entry to the excludedTypeFilterDatas state
   * 3. Updates the form with the new key and filter entry
   * 4. Calls the onChange callback function to update the parent form
   */
  const handleAddExcludedTypeFilter = () => {
    const newKey = generateUniqueKey();

    const newExcludedTypeFilterDatas = [
      ...excludedTypeFilterDatas,
      { key: newKey },
    ];
    setExcludedTypeFilterDatas(newExcludedTypeFilterDatas);
    const currentKeys =
      form.getFieldValue(["PressReleases", "EditExcludedTypeFilterKeys"]) || [];
    form.setFieldValue(
      ["PressReleases", "EditExcludedTypeFilterKeys"],
      [...currentKeys, newKey]
    );

    onChange && onChange();
  };

  /**
   * Handles deleting an excluded type filter from the form.
   * 1. Retrieves the key of the filter to be deleted
   * 2. Filters out the specified filter from the excludedTypeFilterDatas state
   * 3. Updates the form with the remaining filter keys
   * 4. Calls the onChange callback function to update the parent form
   *
   * @param {number} index - The index of the filter to be deleted
   */
  const handleDeleteExcludedTypeFilter = useCallback((index) => {
    const keyToDelete = excludedTypeFilterDatas[index]?.key;

    setExcludedTypeFilterDatas((excludedTypeFilterDatas) =>
      excludedTypeFilterDatas.filter((_, i) => i !== index)
    );
    const currentKeys =
      form.getFieldValue(["PressReleases", "EditExcludedTypeFilterKeys"]) || [];
    form.setFieldValue(
      ["PressReleases", "EditExcludedTypeFilterKeys"],
      currentKeys.filter((key) => key !== keyToDelete)
    );
    form.setFieldValue(
      ["PressReleases", "EditExcludedTypeFilter", keyToDelete],
      undefined
    );

    onChange && onChange();
  }, [excludedTypeFilterDatas, form, onChange]);

  /**
   * Defines the columns for the excluded type filter table.
   * 1. Source column: Displays a dropdown for selecting a source
   * 2. Excluded Message Type column: Displays a dropdown for selecting an excluded message type
   * 3. Action column: Displays a delete button for removing the filter
   *
   * @type {import("antd").TableProps<DataType>['columns']}
   */
  const excludedTypeFilterColumns = useMemo(
    () => [
      {
        title: "Source",
        align: "left",
        minWidth: 300,
        key: "Source",
        render: (_, record) => (
          <CustomInputTableCell
            type={"select"}
            options={sourceOptions}
            form={form}
            name={[
              "PressReleases",
              "EditExcludedTypeFilter",
              `${record.key}`,
              "Source",
            ]}
            defaultValue={record.value?.["Source"]}
            rules={[{ required: true, message: "Source is required" }]}
          />
        ),
      },
      {
        title: "Excluded Message Type",
        align: "left",
        minWidth: 300,
        key: "ExcludedMessageType",
        render: (_, record) => (
          <CustomInputTableCell
            type={"select"}
            options={getMessageTypeOptions(
              sources,
              editExcludedTypeFilterForm?.[record.key]?.["Source"]
            )}
            form={form}
            name={[
              "PressReleases",
              "EditExcludedTypeFilter",
              `${record.key}`,
              "ExcludedMessageType",
            ]}
            defaultValue={record.value?.["ExcludedMessageType"]}
            rules={[
              { required: true, message: "Excluded Message Type is required" },
            ]}
          />
        ),
      },
      {
        title: "Action",
        align: "center",
        width: 100,
        fixed: !isMobile ? "right" : false,
        key: "Action",
        render: (_, record, index) => (
          <Tooltip title={`Delete this excluded type filter`}>
            <Button
              type="primary"
              danger
              onClick={() => handleDeleteExcludedTypeFilter(index)}
              icon={<DeleteOutlined />}
            />
          </Tooltip>
        ),
      },
    ],
    [sourceOptions, sources, editExcludedTypeFilterForm, form, handleDeleteExcludedTypeFilter, isMobile]
  );

  return (
    <LazyLoad threshold={0.8} fallback={<SkeletonTable rowCount={1} />}>
      <Flex justify="flex-start" align="center" className="mb-5">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddExcludedTypeFilter}
        >
          Add excluded type filter
        </Button>
      </Flex>

      <Table
        key={`excluded-type-filter-${fieldPrefix?.join("-") || "default"}`}
        size={isMobile ? "small" : "default"}
        columns={excludedTypeFilterColumns}
        dataSource={excludedTypeFilterDatas}
        bordered
        pagination={false}
        style={{ width: "100%", marginTop: 16, marginBottom: 24 }}
        scroll={{ x: "max-content", y: 400 }}
        tableLayout="auto"
        rowKey={`excluded-type-filter-${fieldPrefix?.join("-") || "default"}`}
      />
    </LazyLoad>
  );
};

export default ExcludedTypeFilter;
