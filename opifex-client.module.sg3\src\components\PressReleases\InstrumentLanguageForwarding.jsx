import { useState, useEffect, useContext, Fragment } from "react";
import { Button, Flex, Form, Table, Tooltip } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { useInputStyle } from "../../hooks/useInputStyle";
import { SettingsContext } from "../../context/SettingsContext";
import { generateUniqueKey, parseLanguageForwardingString, formatLanguageForwardingToString } from "../../utils/common";
import CustomInputTableCell from "../common/CustomInputTableCell";
import { useMemo } from "react";

const InstrumentLanguageForwarding = ({ onChange, form, sources, instrumentId }) => {
  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const [languageForwardingDatas, setLanguageForwardingDatas] = useState([]);
  
  const languageForwardingSourceOptions = Object.values(sources)?.map(
    (source) => ({
      label: source?.name,
      value: source?.id?.toString(),
    })
  );
  const languageOptions = Object.values(settings?.availableLanguages || {});

  const editLanguageForwardingForm = Form.useWatch(
    ['InstrumentPressReleases', instrumentId, 'EditLanguageForwarding'],
    { form, preserve: true }
  );

  /**
   * Effect to handle language forwarding updates for instrument press releases.
   */
  useEffect(() => {
    const languageForwardingArray = [];
    if (editLanguageForwardingForm) {
      Object.keys(editLanguageForwardingForm).forEach((key) => {
        const item = editLanguageForwardingForm[key];
        if (item?.Source && item?.ForwardedLanguage && item?.DisplayLanguage) {
          languageForwardingArray.push({
            Source: item.Source,
            ForwardedLanguage: item.ForwardedLanguage, // Keep original field names
            DisplayLanguage: item.DisplayLanguage, // Keep original field names
          });
        }
      });
    }
    const languageForwardingString = formatLanguageForwardingToString(languageForwardingArray);
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'LanguageForwarding'], languageForwardingString);
    onChange && onChange();
  }, [editLanguageForwardingForm, form, onChange, instrumentId]);

  /**
   * Effect to initialize language forwarding data for instrument press releases.
   */
  useEffect(() => {
    const initState = settings?.initState?.Instruments?.Instrument?.find(
      inst => inst.Id?.toString() === instrumentId?.toString()
    );
    
    if (!form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys'])) {
      const editLangForward = parseLanguageForwardingString(
        initState?.PressRelease?.LanguageForwarding || ''
      )
        ?.filter((item) => Object.keys(sources || {}).includes(item.Source))
        ?.reduce(
          (editLangForwardObject, item) => ({ ...editLangForwardObject, [generateUniqueKey()]: item }),
          {}
        );
      const langForwardingKeys = Object.keys(editLangForward || {});
      const langForwardDatas = langForwardingKeys?.map((itemKey) => ({
        key: itemKey,
        value: editLangForward[itemKey],
      }));

      form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwarding'], editLangForward);
      form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys'], langForwardingKeys);
      setLanguageForwardingDatas(langForwardDatas || []);
      onChange && onChange();
    } else {
      const langForwardingKeys = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys']);
      const langForwardDatas = langForwardingKeys?.map((itemKey) => ({
        key: itemKey,
        value: editLanguageForwardingForm?.[itemKey],
      }));
      setLanguageForwardingDatas(langForwardDatas || []);
    }
  }, [sources, settings, form, onChange, instrumentId, editLanguageForwardingForm]);

  const addLanguageForwarding = () => {
    const newKey = generateUniqueKey();
    const currentKeys = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys']) || [];
    const newKeys = [...currentKeys, newKey];
    
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys'], newKeys);
    setLanguageForwardingDatas(prev => [...prev, { key: newKey, value: {} }]);

    // Trigger onChange to update parent form
    onChange && onChange();
  };

  const removeLanguageForwarding = (keyToRemove) => {
    const currentKeys = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys']) || [];
    const newKeys = currentKeys.filter(key => key !== keyToRemove);

    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwardingKeys'], newKeys);

    const currentEditData = form.getFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwarding']) || {};
    const newEditData = { ...currentEditData };
    delete newEditData[keyToRemove];
    form.setFieldValue(['InstrumentPressReleases', instrumentId, 'EditLanguageForwarding'], newEditData);

    setLanguageForwardingDatas(prev => prev.filter(item => item.key !== keyToRemove));

    // Trigger onChange to update parent form
    onChange && onChange();
  };

  const languageForwardingColumns = useMemo(() => [
    {
      title: 'Source',
      align: 'left',
      minWidth: 300,
      key: 'Source',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={languageForwardingSourceOptions}
          form={form}
          name={[
            'InstrumentPressReleases',
            instrumentId,
            'EditLanguageForwarding',
            `${record.key}`,
            'Source',
          ]}
          rules={[{ required: true, message: 'Source is required' }]}
          defaultValue={record.value?.['Source']}
        />
      ),
    },
    {
      title: 'Source Language',
      align: 'left',
      minWidth: 300,
      key: 'ForwardedLanguage',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={languageOptions}
          form={form}
          name={[
            'InstrumentPressReleases',
            instrumentId,
            'EditLanguageForwarding',
            `${record.key}`,
            'ForwardedLanguage',
          ]}
          rules={[{ required: true, message: 'Forwarded Language is required' }]}
          defaultValue={record.value?.['ForwardedLanguage']}
        />
      ),
    },
    {
      title: 'Destination Language',
      align: 'left',
      minWidth: 300,
      key: 'DisplayLanguage',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={languageOptions}
          form={form}
          name={[
            'InstrumentPressReleases',
            instrumentId,
            'EditLanguageForwarding',
            `${record.key}`,
            'DisplayLanguage',
          ]}
          rules={[{ required: true, message: 'Display Language is required' }]}
          defaultValue={record.value?.['DisplayLanguage']}
          
        />
      ),
    },
    {
      title: 'Action',
      align: 'center',
      width: 100,
      fixed: !isMobile ? 'right' : false,
      key: 'Action',
      render: (_, record) => (
        <Tooltip title={`Delete this language forwarding`}>
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => removeLanguageForwarding(record.key)}
        />
        </Tooltip>
      ),
    },
  ], []);

  return (
    <Fragment>
      <Flex justify="flex-start" align="center" className="mb-5">
        <Button
          type="primary"
          onClick={addLanguageForwarding}
          icon={<PlusOutlined />}
        >
          Add Language Forwarding
        </Button>
      </Flex>
      <Table
        key={`language-forwarding-${instrumentId}`} 
        columns={languageForwardingColumns}
        dataSource={languageForwardingDatas}
        size={isMobile ? 'small' : 'default'}
        bordered
        pagination={false}
        style={{ width: '100%', marginTop: 16, marginBottom: 24 }}
        scroll={{ x: 'max-content', y: 400 }}
        tableLayout="auto"
        rowKey={`language-forwarding-${instrumentId}`} 
      />
    </Fragment>
  );
};

export default InstrumentLanguageForwarding;
