.rule_gamma_template:
  environment:
    name: gamma
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v?\d+\.\d+\.\d+\-[a-zA-Z0-9\.\-]+$/'

prepare:gamma:
  tags:
    - ee-v-docker2-dind
  stage: prepare
  before_script:
    - apk add --no-cache git
    - chmod +x ./scripts/update-versions-script.sh
  script:
    - ./scripts/update-versions-script.sh
  artifacts:
    paths:
      - version.json
      - package.json
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v?\d+\.\d+\.\d+(-[a-zA-Z0-9\.\-]+)?$/'

build:gamma:
  extends:
    - .node_template
    - .rule_gamma_template
  tags:
    - ee-v-docker2-dind
  stage: build
  needs:
    - prepare:gamma
  script:
    - npm run build "--" --mode=production
  artifacts:
    paths:
      - dist/
    expire_in: 1 week

test:gamma:
  extends:
    - .node_template
    - .rule_gamma_template
  stage: test
  needs:
    - prepare:gamma
    - build:gamma
  tags:
    - ee-v-docker2-dind
  script:
    # - npm run test
    - echo "Missing npm test script..."

quality:gamma:
  extends:
    - .node_template
    - .rule_gamma_template
  tags:
    - ee-v-docker2-dind
  stage: quality
  needs:
    - prepare:gamma
    - build:gamma
  script:
    - npm run lint

package:gamma:
  extends:
    - .node_template
    - .rule_gamma_template
  tags:
    - ee-v-docker2-dind
  stage: package
  needs:
    - prepare:gamma
    - build:gamma
    - test:gamma
    - quality:gamma
  before_script:
    - apk add --no-cache git
    - chmod +x ./scripts/package-client-semantic.sh
  script:
    - echo "Packaging..."
    - './scripts/package-client-semantic.sh'
  artifacts:
    paths:
      - version.json
      - '*.tar.gz'
    expire_in: 1 week

deploy:gamma:
  extends:
    - .rule_gamma_template
    - .deploy_frontend_script_template
  tags:
    - ee-buildtest-powershell
  stage: deploy
  needs:
    - prepare:gamma
    - build:gamma
    - test:gamma
    - quality:gamma
    - package:gamma
  environment:
    name: gamma
    url: 'https://gamma.euroland.com/tools/opifex3/modules/ic2/version.txt'
  variables:
    ENVIRONMENT: 'Gamma'
    DEPLOY_SERVER1: 'ee-v-gamma1.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/opifex3/modules/ic2'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:GAMMA_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:GAMMA_DEPLOY_PSW"
