import {
  __commonJ<PERSON>
} from "./chunk-ZKAWKZG5.js";

// node_modules/__mf__virtual/sg3_mf_2_project_mf_2_name__mf_v__runtimeInit__mf_v__.js
var require_sg3_mf_2_project_mf_2_name_mf_v_runtimeInit_mf_v = __commonJS({
  "node_modules/__mf__virtual/sg3_mf_2_project_mf_2_name__mf_v__runtimeInit__mf_v__.js"(exports, module) {
    var initResolve;
    var initReject;
    var initPromise = new Promise((re, rj) => {
      initResolve = re;
      initReject = rj;
    });
    module.exports = {
      initPromise,
      initResolve,
      initReject
    };
  }
});

export {
  require_sg3_mf_2_project_mf_2_name_mf_v_runtimeInit_mf_v
};
//# sourceMappingURL=chunk-5XPW24QV.js.map
