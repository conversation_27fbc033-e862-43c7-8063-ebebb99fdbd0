import { useState } from "react";
import { Flex, Form, Button } from "antd";
import { BoldOutlined, ItalicOutlined, UnderlineOutlined } from "@ant-design/icons";

const FormatButton = ({ icon, formatValue, onChange,value, children }) => {
  const [isActive, setIsActive] = useState(()=>{
    return value === formatValue;
  });
  const handleClick = () => {
    setIsActive(!isActive);
    onChange(!isActive ? formatValue : null);
  };
  return (
    <Button type={isActive ? "primary" : "default"} shape="circle" icon={icon} onClick={handleClick}>
      {children}
    </Button>
  );
};

const TextTransformButton = ({ onChange, value }) => {
  const textTransForms = [
    { id: 0, label: "AA", formatValue: "uppercase" },
    { id: 1, label: "aa", formatValue: "lowercase" },
    { id: 2, label: "Aa", formatValue: "capitalize" },
  ];
  const [selectedId, setSelectedId] = useState(()=>{
    return textTransForms.findIndex((item) => item.formatValue === value);
  });
  const handleClick = (formatValue, id) => {
    if (selectedId !== id) {
      onChange(formatValue);
      setSelectedId(id);
    } else {
      onChange(null);
      setSelectedId(-1);
    }
  };
  return (
    <Flex justify="flex-start" align="center" gap={16} wrap>
      {textTransForms.map(({ label, formatValue, id }) => (
        <Button
          key={id}
          type={selectedId === id ? "primary" : "default"}
          shape="circle"
          onClick={() => {
            handleClick(formatValue, id);
          }}
        >
          <span className="underline decoration-2 font-normal opacity-95 mb-1">{label}</span>
        </Button>
      ))}
    </Flex>
  );
};

const FormItemTextFormat = ({ name, label = "", className }) => {
  const formatButtons = [
    { name: [...name, "font-weight"], icon: <BoldOutlined />, label: "Bold", formatValue: "bold" },
    {
      name: [...name, "font-style"],
      icon: <ItalicOutlined />,
      label: "Italic",
      formatValue: "italic",
    },
    {
      name: [...name, "text-decoration"],
      icon: <UnderlineOutlined />,
      label: "Underline",
      formatValue: "underline",
    },
  ];

  return (
    <Form.Item label={label} className={className}>
      <Flex justify="flex-start" align="center" gap={16} wrap>
        {formatButtons.map(({ icon, formatValue, name }) => (
          <Form.Item key={name} name={name}>
            <FormatButton icon={icon} formatValue={formatValue} />
          </Form.Item>
        ))}
        <Form.Item name={[...name, "text-transform"]}>
          <TextTransformButton />
        </Form.Item>
      </Flex>
    </Form.Item>
  );
};
export { FormItemTextFormat };
