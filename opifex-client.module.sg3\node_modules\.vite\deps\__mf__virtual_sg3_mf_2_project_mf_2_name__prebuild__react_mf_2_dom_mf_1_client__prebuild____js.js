import {
  require_sg3_mf_2_project_mf_2_name_loadShare_react_mf_2_dom_loadShare
} from "./chunk-5TDOOKFX.js";
import "./chunk-5XPW24QV.js";
import "./chunk-2WHSENDH.js";
import {
  __commonJS
} from "./chunk-ZKAWKZG5.js";

// node_modules/react-dom/client.js
var require_client = __commonJS({
  "node_modules/react-dom/client.js"(exports) {
    var m = require_sg3_mf_2_project_mf_2_name_loadShare_react_mf_2_dom_loadShare();
    if (false) {
      exports.createRoot = m.createRoot;
      exports.hydrateRoot = m.hydrateRoot;
    } else {
      i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
      exports.createRoot = function(c, o) {
        i.usingClientEntryPoint = true;
        try {
          return m.createRoot(c, o);
        } finally {
          i.usingClientEntryPoint = false;
        }
      };
      exports.hydrateRoot = function(c, h, o) {
        i.usingClientEntryPoint = true;
        try {
          return m.hydrateRoot(c, h, o);
        } finally {
          i.usingClientEntryPoint = false;
        }
      };
    }
    var i;
  }
});
export default require_client();
//# sourceMappingURL=__mf__virtual_sg3_mf_2_project_mf_2_name__prebuild__react_mf_2_dom_mf_1_client__prebuild____js.js.map
