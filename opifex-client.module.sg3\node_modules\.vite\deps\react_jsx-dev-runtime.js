import {
  require_sg3_mf_2_project_mf_2_name_mf_v_runtimeInit_mf_v
} from "./chunk-5XPW24QV.js";
import {
  require_index_cjs
} from "./chunk-2WHSENDH.js";
import {
  __commonJS
} from "./chunk-ZKAWKZG5.js";

// node_modules/__mf__virtual/sg3_mf_2_project_mf_2_name__loadShare__react_mf_1_jsx_mf_2_dev_mf_2_runtime__loadShare__.js
var require_sg3_mf_2_project_mf_2_name_loadShare_react_mf_1_jsx_mf_2_dev_mf_2_runtime_loadShare = __commonJS({
  "node_modules/__mf__virtual/sg3_mf_2_project_mf_2_name__loadShare__react_mf_1_jsx_mf_2_dev_mf_2_runtime__loadShare__.js"(exports, module) {
    var { loadShare } = require_index_cjs();
    var { initPromise } = require_sg3_mf_2_project_mf_2_name_mf_v_runtimeInit_mf_v();
    var res = initPromise.then((_) => loadShare("react/jsx-dev-runtime", {
      customShareInfo: { shareConfig: {
        singleton: true,
        strictVersion: false,
        requiredVersion: "^18.3.1"
      } }
    }));
    var exportModule = (
      /*mf top-level-await placeholder replacement mf*/
      res.then((factory) => factory())
    );
    module.exports = exportModule;
  }
});
export default require_sg3_mf_2_project_mf_2_name_loadShare_react_mf_1_jsx_mf_2_dev_mf_2_runtime_loadShare();
//# sourceMappingURL=react_jsx-dev-runtime.js.map
