.rule_develop:
  environment:
    name: qa
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'

build:develop:
  extends:
    - .node_template
    - .rule_develop
  tags:
    - vn-v-docker-dind
  stage: build
  script:
    - npm run build "--" --mode=qa
  artifacts:
    paths:
      - dist/
    expire_in: 1 week

test:develop:
  extends:
    - .node_template
    - .rule_develop
  stage: test
  tags:
    - vn-v-docker-dind
  needs:
    - build:develop
  script:
    #- npm run test
    - echo "Missing npm test script..."

quality:develop:
  extends:
    - .node_template
    - .rule_develop
  tags:
    - vn-v-docker-dind
  stage: quality
  needs:
    - build:develop
    - test:develop
  script:
    - npm run lint

package:develop:
  extends:
    - .node_template
    - .rule_develop
  tags:
    - vn-v-docker-dind
  stage: package
  needs:
    - build:develop
    - test:develop
    - quality:develop
  before_script:
    - apk add --no-cache git
    - chmod +x ./scripts/package-client-semantic.sh
  script:
    - echo "Packaging..."
    - './scripts/package-client-semantic.sh'
  artifacts:
    paths:
      - version.json
      - '*.tar.gz'
    expire_in: 1 week

deploy:develop:
  extends:
    - .rule_develop
    - .deploy_frontend_script_template
  tags:
    - vietnam-buildtest-powershell
  stage: deploy
  needs:
    - build:develop
    - test:develop
    - package:develop
  environment:
    name: qa
    url: 'https://dev.vn.euroland.com/tools/tools/opifex3/modules/ic2/version.txt'
  variables:
    ENVIRONMENT: 'QA'
    DEPLOY_SERVER1: 'BINGO'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'tools site'
    APP_PATH: '/tools/opifex3/modules/ic2'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:DEV_VN_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:DEV_VN_DEPLOY_PSW"
