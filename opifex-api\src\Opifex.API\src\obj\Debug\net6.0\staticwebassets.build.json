{"Version": 1, "Hash": "5lFFP+f42TIHyw21Wmq06eWJJMbHP+wo8uN3vnU2+iU=", "Source": "Opifex", "BasePath": "_content/Opifex", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Opifex\\wwwroot", "Source": "Opifex", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-nda\\v1.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-nda/v1.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-nda\\v1.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-nda\\v1.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-nda/v1.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-nda\\v1.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-nda\\v133.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-nda/v133.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-nda\\v133.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-nda\\v133.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-nda/v133.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-nda\\v133.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\custom.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/custom.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\custom.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\dig.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/dig.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\dig.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\dig.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/dig.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\dig.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v1_default.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v1_default.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v1_default.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v1.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v1.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v1.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v1.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v1.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v1.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v2.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v2.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v2.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v2.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v2.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v2.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v3.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v3.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v3.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v3.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v3.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v3.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v4.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v4.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v4.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-secu\\v4.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-secu/v4.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-secu\\v4.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\font\\aeonik-bold.otf", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/font/aeonik-bold.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\font\\aeonik-bold.otf"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\font\\aeonik-light.otf", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/font/aeonik-light.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\font\\aeonik-light.otf"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\font\\montserrat-regular.ttf", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/font/montserrat-regular.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\font\\montserrat-regular.ttf"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\image\\ChatGPTImage.png", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/image/ChatGPTImage.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\image\\ChatGPTImage.png"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\image\\image.png", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/image/image.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\image\\image.png"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-clone_default.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/v-clone_default.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-clone_default.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-clone.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/v-clone.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-clone.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-clone.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/v-clone.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-clone.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-sg.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/v-sg.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-sg.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-sg.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/v-sg.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\v-sg.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\Config\\Company\\s-volv\\v1_default.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/Config/Company/s-volv/v1_default.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\Config\\Company\\s-volv\\v1_default.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\fragulizer\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "fragulizer/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fragulizer\\field_validation.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\investmentCal2\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "investmentCal2/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\investmentCal2\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\investmentCal2\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "investmentCal2/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\investmentCal2\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\investmentCal2\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "investmentCal2/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\investmentCal2\\field_validation.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\investmentCal3\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "investmentCal3/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\investmentCal3\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\investmentCal3\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "investmentCal3/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\investmentCal3\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\investmentCal3\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "investmentCal3/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\investmentCal3\\field_validation.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\lst3\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "lst3/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lst3\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\lst3\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "lst3/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lst3\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\lst3\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "lst3/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lst3\\field_validation.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v133.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-nda/v133.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v133.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v133.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-nda/v133.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v133.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v6.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-nda/v6.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v6.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v6.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-nda/v6.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-nda\\v6.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-secu\\v-sg2.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-secu/v-sg2.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-secu\\v-sg2.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-secu\\v-sg2.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-secu/v-sg2.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-secu\\v-sg2.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-volv\\v-sg.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-volv/v-sg.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-volv\\v-sg.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company\\s-volv\\v-sg.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/Config/Company/s-volv/v-sg.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\Config\\Company\\s-volv\\v-sg.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "ShareGraph3/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ShareGraph3\\field_validation.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v1222.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/Config/Company/dk-cbg/v1222.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v1222.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v1222.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/Config/Company/dk-cbg/v1222.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v1222.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v12321.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/Config/Company/dk-cbg/v12321.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v12321.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v12321.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/Config/Company/dk-cbg/v12321.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\Config\\Company\\dk-cbg\\v12321.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\Config\\Company\\s-nda\\v44.css", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/Config/Company/s-nda/v44.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\Config\\Company\\s-nda\\v44.css"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\Config\\Company\\s-nda\\v44.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/Config/Company/s-nda/v44.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\Config\\Company\\s-nda\\v44.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\shareseries2\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "shareseries2/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\shareseries2\\field_validation.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\splookup2\\field_default_value.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "splookup2/field_default_value.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\splookup2\\field_default_value.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\splookup2\\field_editable.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "splookup2/field_editable.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\splookup2\\field_editable.xml"}, {"Identity": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\splookup2\\field_validation.xml", "SourceId": "Opifex", "SourceType": "Discovered", "ContentRoot": "C:\\Publish\\Opifexs\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\", "BasePath": "_content/Opifex", "RelativePath": "splookup2/field_validation.xml", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\splookup2\\field_validation.xml"}]}