import { DeleteOutlined } from "@ant-design/icons";
import { Button, Form, Table, Tooltip } from "antd";
import { useContext, useEffect, useState } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import CustomPhrasesSearch from "./CustomPhrasesSearch";
import CustomInputTableCell from "../common/CustomInputTableCell";
import { getCustomePhraseApi } from "../../services";
import { useInputStyle } from "../../hooks/useInputStyle";

export const CustomPhrases = ({ form, onChange }) => {
  const [customPhrasesList, setCustomPhrasesList] = useState([]);
  const [selectCustomPhrases, setSelectCustomPhrases] = useState([]);
  const [openModalSelectCP, setOpenModalSelectCP] = useState(false);

  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();

  /**
   * @type {import("antd").TableProps<DataType>['columns']}
   */
  const columns = [
    {
      title: "Keywords",
      align: "left",
      minWidth: 200,
      fixed: !isMobile ? "left" : false,
      dataIndex: "key",
      key: "key",
      showSorterTooltip: {
        target: "full-header",
      },
      sorter: (a, b) => {
        if (a.key === b.key) {
          return 0;
        } else if (a.key > b.key) {
          return 1;
        } else {
          return -1;
        }
      },
      render: (key) => key,
    },
    ...(settings?.basicSettings?.availableLanguages
      ? settings.basicSettings.availableLanguages.map((lang) => ({
          title: lang,
          align: "left",
          minWidth: 300,
          dataIndex: "",
          key: "",
          render: (_, record) => (
            <CustomInputTableCell
              form={form}
              name={["EditCustomPhrases", `${record.key}`, `${lang}`]}
              defaultValue={record?.value?.[lang] ?? ""}
            />
          ),
        }))
      : []),
    {
      title: "Action",
      align: "center",
      width: 100,
      fixed: !isMobile ? "right" : false,
      dataIndex: "",
      key: "",
      render: (_, record) => (
        <Tooltip title={`Delete phrase ${record.key}`}>
          <Button
            type="primary"
            danger
            onClick={() => handleDeleteCustomPhrases(record.key)}
            icon={<DeleteOutlined />}
          />
        </Tooltip>
      ),
    },
  ];

  const handleDeleteCustomPhrases = (key) => {
    setSelectCustomPhrases((selectCustomPhrases) =>
      selectCustomPhrases.filter((i) => i.key !== key)
    );
    const newCustomPhrases = { ...form.getFieldsValue(true)?.CustomPhrases };
    if (newCustomPhrases && newCustomPhrases[key]) {
      newCustomPhrases[key] = {};
      delete newCustomPhrases[key];
      form.setFieldValue(["CustomPhrases"], newCustomPhrases);
    }
    const newEditCustomPhrases = {
      ...form.getFieldsValue(true)?.EditCustomPhrases,
    };
    if (newEditCustomPhrases && newEditCustomPhrases[key]) {
      newEditCustomPhrases[key] = {};
      delete newEditCustomPhrases[key];
      form.setFieldValue(["EditCustomPhrases"], newEditCustomPhrases);
    }
  };

  const getCustomPhrasesList = async () => {
    const { data } = await getCustomePhraseApi({
      tools: [settings?.toolName],
      languages: settings?.basicSettings?.availableLanguages,
    });
    if (data && data.data && data.statusCode === 200) {
      const customPhrasesList = data.data
        ?.find((item) => item.tool === settings?.toolName)
        .translations.reduce((acc, curr) => {
          curr.customPhrases.forEach((cp) => {
            if (acc[cp.key]) {
              acc[cp.key].value[curr.languageCode] = cp.value;
            } else {
              acc[cp.key] = {
                key: cp.key,
                value: { [curr.languageCode]: cp.value },
              };
            }
          });
          return acc;
        }, {});
      setCustomPhrasesList(customPhrasesList);
    }
  };

  useEffect(() => {
    getCustomPhrasesList();
    return () => {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [settings?.basicSettings?.availableLanguages]);

  useEffect(() => {
    if (Object.keys(customPhrasesList).length > 0) {
      setSelectCustomPhrases((selectCustomPhrases) => {
        return selectCustomPhrases
          .map((cp) => customPhrasesList[cp.key])
          .filter((cp) => cp);
      });
    }
  }, [customPhrasesList]);

  useEffect(() => {
    if (settings?.initState?.CustomPhrases) {
      setSelectCustomPhrases(() =>
        Object.keys({ ...settings?.initState?.CustomPhrases }).map((key) => ({
          key,
          value: { ...settings?.initState?.CustomPhrases[key] },
        }))
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const curSelectCustomPhrases = {
      ...form.getFieldsValue(true)?.CustomPhrases,
    };
    const newSelectCustomPhrases = selectCustomPhrases.reduce((acc, curr) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});

    const newCustomPhrases = Object.keys(newSelectCustomPhrases).reduce(
      (acc, key) => {
        acc[key] = Array.from(
          new Set([
            ...Object.keys(newSelectCustomPhrases[key] ?? {}),
            ...Object.keys(curSelectCustomPhrases[key] ?? {}),
          ])
        ).reduce((a, c) => {
          if (curSelectCustomPhrases?.[key]?.[c] === undefined) {
            a[c] = newSelectCustomPhrases[key][c];
          } else {
            a[c] = curSelectCustomPhrases[key][c];
          }
          return a;
        }, {});
        return acc;
      },
      {}
    );

    form.setFieldValue(["CustomPhrases"], newCustomPhrases);
    onChange && onChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectCustomPhrases]);
  return (
    <>
      <Button type="primary" onClick={() => setOpenModalSelectCP(true)}>
        Add Custom Phrases
      </Button>
      <Form.Item noStyle>
        <Table
          size={isMobile ? "small" : "middle"}
          dataSource={selectCustomPhrases}
          columns={columns}
          bordered
          pagination={false}
          style={{ width: "100%", marginTop: 16 }}
          scroll={{ x: "max-content", y: 500 }}
          tableLayout="auto"
          showSorterTooltip={{
            target: "sorter-icon",
          }}
        />
      </Form.Item>
      <CustomPhrasesSearch
        isOpen={openModalSelectCP}
        onClose={() => setOpenModalSelectCP(false)}
        customPhrasesList={customPhrasesList}
        selectCustomPhrases={selectCustomPhrases}
        onSelectCustomPhrases={setSelectCustomPhrases}
      />
    </>
  );
};
