import {
  addBorderEdgeSolidDefaultStyle,
  addBorderRadiusDefaultStyle,
  addBorderSolidDefaultStyle,
  markImportantCssProperty,
} from "../utils/common";
import { SPECIAL_PROPERTIES } from "../constant/selectorApiCss";

export function getGeneralSettings({
  generalTools,
  companyFonts,
  basicSettings,
  state,
}) {
  console.log("generalTools", generalTools);
  const { mainFont } = basicSettings;
  const {
    tab = {},
    tabActive = {},
    hyperlinkActive = {},
    tabAlignment = {},
    tabPadding = {},
    tabBorderRadius = {},
    tabFont = {},
    tabMargin = {},
    buttonNormal = {},
    buttonFont = {},
    tableBody = {},
    tableBodyDataColumn = {},
    tableBodyNameColumn = {},
    tableBorder = {},
    tableBorderRadius = {},
    tableHeaderNameColumn = {},
    tableHeader = {},
    heading = {},
    calendarActiveButton = {},
    calendarActiveDate = {},
    calendarBorderRadius = {},
    calendarButton = {},
    calendarDate = {},
    calendarFont = {},
    calendarPadding = {},
    hyperlinkNormal = {},
    buttonBorder = {},
    buttonBorderRadius = {},
    buttonPadding = {},
    buttonActive = {},
    tableOddRowColor = {},
    tableEvenRowColor = {},
    tableBodyNormal = {},
    tableBodyActive = {},
    tableHeaderBorder = {},
    tableBodyBorder = {},
    tableHeaderDataColumn = {},
    checkboxNormal = {},
    input = {},
    inputBorder = {},
    inputBorderRadius = {},
    inputPadding = {},
    dropdownButton = {},
    dropdownButtonBorder = {},
    dropdownButtonBorderRadius = {},
    dropdownButtonPadding = {},
    dropdownNormal = {},
    dropdownActive = {},
  } = generalTools;

  const textColor = mainFont?.color;
  const fontFamily = mainFont?.fontFamily;
  const fontSize = mainFont?.fontSize && `${mainFont?.fontSize}px`;
  const result = {
    body: {
      fontFamily: fontFamily,
      fontSize: fontSize,
      color: textColor,
    },
    "@font-face": companyFonts?.map((item) => item.css),
    ".tabs-control .tabs": {
      display: "flex",
      justifyContent:
        tabAlignment?.verticalAlign === "left"
          ? "flex-start"
          : tabAlignment?.verticalAlign === "right"
          ? "flex-end"
          : tabAlignment?.verticalAlign,
    },
    ".tabs-control .tabs .tabItem, button.chart-type, button.chart-type": {
      ...addBorderRadiusDefaultStyle(tabBorderRadius),
      ...(tabFont || {}),
      ...(tabMargin || {}),
      ...(tabPadding || {}),
      color: tab?.color,
      backgroundColor: tab?.backgroundColor,
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: tab?.borderTopWidth,
        borderColor: tab?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: tab?.borderBottomWidth,
        borderColor: tab?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: tab?.borderLeftWidth,
        borderColor: tab?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: tab?.borderRightWidth,
        borderColor: tab?.borderRightColor,
      }),
    },
    ".tabs-control .tabs .active, .tabs-control .tabs .tabItem:hover, button.chart-type:not(.chart-type-disabled)": {
      color: tabActive?.color,
      backgroundColor: tabActive?.backgroundColor,
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: tabActive?.borderTopWidth,
        borderColor: tabActive?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: tabActive?.borderBottomWidth,
        borderColor: tabActive?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: tabActive?.borderLeftWidth,
        borderColor: tabActive?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: tabActive?.borderRightWidth,
        borderColor: tabActive?.borderRightColor,
      }),
    },
    " .tabs-control .tabs .tabItem:hover": {
      ...(tabActive?.backgroundColor ? markImportantCssProperty({ backgroundColor: tabActive?.backgroundColor}) : {})
    },
    ".table-share thead tr .table-share-header, .table-share-header-date": {
      ...(tableHeaderNameColumn || {}),
      ...tableHeader,
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: tableHeaderBorder?.borderTopWidth,
        borderColor: tableHeaderBorder?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: tableHeaderBorder?.borderBottomWidth,
        borderColor: tableHeaderBorder?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: tableHeaderBorder?.borderLeftWidth,
        borderColor: tableHeaderBorder?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: tableHeaderBorder?.borderRightWidth,
        borderColor: tableHeaderBorder?.borderRightColor,
      }),
    },
    "body[dir='rtl'] .table-share thead tr .table-share-header, body[dir='rtl'] .table-share-header-date": {
      textAlign: tableHeaderNameColumn?.textAlign === 'left' ? 'right' : tableHeaderNameColumn?.textAlign === 'right' ? 'left' : tableHeaderNameColumn?.textAlign,
    },
    ".table-share thead tr .table-share-header:not(.table-share-header-name)": {
      ...(tableHeaderDataColumn || {}),
    },
    ".table-share tbody tr td": {
      ...(tableBodyNormal || {}),
      ...tableBody,
      ...tableBodyDataColumn,
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: tableBodyBorder?.borderTopWidth,
        borderColor: tableBodyBorder?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: tableBodyBorder?.borderBottomWidth,
        borderColor: tableBodyBorder?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: tableBodyBorder?.borderLeftWidth,
        borderColor: tableBodyBorder?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: tableBodyBorder?.borderRightWidth,
        borderColor: tableBodyBorder?.borderRightColor,
      }),
    },
    "body[dir='rtl'] .table-share tbody tr td": {
      textAlign: tableBodyDataColumn?.textAlign === 'left' ? 'right' : tableBodyDataColumn?.textAlign === 'right' ? 'left' : tableBodyDataColumn?.textAlign,
    },
    ".table-share tbody tr td:hover": {
      ...tableBodyActive,
    },
    ".table-share tbody tr .table-share-row-odd": {
      ...(tableOddRowColor || {}),
    },
    ".table-share tbody tr .table-share-row-even": {
      ...(tableEvenRowColor || {}),
    },
    ".table-share tbody tr:hover .table-share-row-even, .table-share tbody tr:hover .table-share-row-odd":
      {
        ...markImportantCssProperty(tableBodyActive),
      },
    ".table-share tbody tr": {
      ...tableBody,
      ...addBorderSolidDefaultStyle(tableBodyBorder),
    },
    ".table-share tbody tr .table-share-row-name, .table-share tbody tr .checkbox-wrapper":
      {
        ...tableBodyNameColumn,
      },
    ".table-share tbody tr td:not(.table-share-row-name, .checkbox-wrapper)": {
      ...(tableBodyDataColumn || {}),
    },
    ".template-quadrata table tbody tr": {},
    ".wrapper .main-heading": {
      ...(heading || {}),
    },
    ".parameter-item-label label, .parameter-item label": {
      fontFamily: fontFamily,
      fontSize: fontSize,
      color: textColor,
    },
    ".footer": {
      fontFamily: fontFamily,
      fontSize: '13px !important',
      color: textColor,
      lineHeight: '16px',
    },
    ".share-selection .table-share": {
      ...(tableBorder || {}),
      ...addBorderRadiusDefaultStyle(tableBorderRadius),
      ...(tableBorder?.borderWidth && { borderStyle: "solid" }),
    },
    ".div-checkbox .checkbox input": {
      ...(checkboxNormal || {}),
    },
    ".footer .disclaimer-box a, .footer .cookies-box a": {
      ...hyperlinkNormal,
      fontWeight: hyperlinkNormal?.fontWeight ?? "inherit",
      fontStyle: hyperlinkNormal?.fontStyle ?? "inherit",
      textTransform: hyperlinkNormal?.textTransform ?? "inherit",
      ...markImportantCssProperty({
        textDecoration: state?.LinkUnderline
          ? "underline"
          : hyperlinkNormal?.textDecoration ?? "inherit",
      }),
    },
    ".footer .disclaimer-box a:hover, .footer .cookies-box a:hover":
      {
        ...hyperlinkActive,
        fontWeight: hyperlinkActive?.fontWeight ?? "inherit",
        fontStyle: hyperlinkActive?.fontStyle ?? "inherit",
        textTransform: hyperlinkActive?.textTransform ?? "inherit",
        textDecoration: hyperlinkActive?.textDecoration ?? "inherit",
        ...markImportantCssProperty({
          textDecoration: state?.LinkUnderline
            ? "underline"
            : hyperlinkActive?.textDecoration ?? "inherit",
        }),
      },
    ".footer .hyperlink": {
      backgroundImage: 'url(https://tools.euroland.com/tools/data/logo-transparent.png)',
      backgroundPosition: '100%',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'contain',
      color: 'transparent !important',
      display: 'inline-block',
      height: '19px',
      width: '6.5rem',
    },
    ".footer .hyperlink:hover": {
      color: 'transparent !important',
    },
    ".parameter-item input": {
      ...markImportantCssProperty({height: fontSize}),
      ...input,
      ...inputPadding,
      ...addBorderRadiusDefaultStyle(inputBorderRadius),
      border: "unset",
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderColor: inputBorder.borderTopColor,
        borderWidth: inputBorder.borderTopWidth,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderColor: inputBorder.borderBottomColor,
        borderWidth: inputBorder.borderBottomWidth,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderColor: inputBorder.borderLeftColor,
        borderWidth: inputBorder.borderLeftWidth,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderColor: inputBorder.borderRightColor,
        borderWidth: inputBorder.borderRightWidth,
      }),
    },
    ".parameter-item input:focus, .parameter-item input:focus-visible": {
      outline: "none",
    },
    ".currency-selection .ac_input, .parameter-item-input .ac_input": {
      ...dropdownButton,
      ...dropdownButtonPadding,
      ...addBorderRadiusDefaultStyle(dropdownButtonBorderRadius),
      border: "unset",
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderColor: dropdownButtonBorder.borderTopColor,
        borderWidth: dropdownButtonBorder.borderTopWidth,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderColor: dropdownButtonBorder.borderBottomColor,
        borderWidth: dropdownButtonBorder.borderBottomWidth,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderColor: dropdownButtonBorder.borderLeftColor,
        borderWidth: dropdownButtonBorder.borderLeftWidth,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderColor: dropdownButtonBorder.borderRightColor,
        borderWidth: dropdownButtonBorder.borderRightWidth,
      }),
    },
    ".dropdown-item, .dropdown-item-table, .arrow-wrapper, .ac-arrow, .ac-arrow button": {
      ...markImportantCssProperty({height: fontSize}),
      lineHeight: 'unset !important'
    },
    ".currency-selection .ac_input .dropdown-item .dropdown-td-text, .parameter-item-input .ac_input .dropdown-item .dropdown-td-text":
      {
        color: dropdownButton?.color,
      },
    ".parameter-item-input .date-text": {
      position: 'relative',
    },
    ".parameter-item-input .EUCalendar-topCont": {
      top: "38px !important",
      left: "0px !important",
      right: 'unset !important',
    },
    "body[dir='rtl'] .parameter-item-input .EUCalendar-topCont": {
      top: "38px !important",
      right: "0px !important",
      left: 'unset !important',
    },
    ".eur-picker-date .fs-calendar::before": {
      color: input?.color || textColor,
    },
    ".data-button-wrapper .data-button": {
      backgroundColor: buttonNormal?.backgroundColor,
      color: buttonNormal?.color,
      ...(buttonPadding || {}),
      ...(buttonBorderRadius || {}),
      ...(buttonFont || {}),
      fontSize: fontSize,
      fontWeight: buttonFont?.fontWeight || "unset",
      textDecoration: buttonFont?.textDecoration || "unset",
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: buttonBorder?.borderTopWidth,
        borderColor: buttonNormal?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: buttonBorder?.borderBottomWidth,
        borderColor: buttonNormal?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: buttonBorder?.borderLeftWidth,
        borderColor: buttonNormal?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: buttonBorder?.borderRightWidth,
        borderColor: buttonNormal?.borderRightColor,
      }),
    },
    ".data-button-wrapper .data-button:hover": {
      ...(buttonActive || {}),
    },
    ".calculation-parameters .EUCalendar .EUCalendar-day-selected": {
      ...calendarDate,
      backgroundColor: calendarDate?.backgroundColor,
      ...(calendarDate?.color
        ? {
            color: `${calendarDate.color} !important`,
          }
        : {}),
      ...(calendarDate?.borderWidth && calendarDate?.borderColor
        ? {
            border: `${calendarDate?.borderWidth} solid ${calendarDate?.borderColor}`,
          }
        : {}),
    },
    ".calculation-parameters .EUCalendar .EUCalendar-hover-date": {
      backgroundColor: calendarActiveDate?.backgroundColor,
      color: calendarActiveDate?.color,
      ...addBorderRadiusDefaultStyle(calendarActiveDate),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: calendarActiveDate?.borderTopWidth,
        borderColor: calendarActiveDate?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: calendarActiveDate?.borderBottomWidth,
        borderColor: calendarActiveDate?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: calendarActiveDate?.borderLeftWidth,
        borderColor: calendarActiveDate?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: calendarActiveDate?.borderRightWidth,
        borderColor: calendarActiveDate?.borderRightColor,
      }),
    },
    ".calculation-parameters .EUCalendar .EUCalendar-bottomBar .EUCalendar-bottomBar-today, .calculation-parameters .EUCalendar .EUCalendar-menu-today":
      {
        ...calendarButton,
        ...calendarFont,
        fontSize: calendarFont?.fontSize ?? fontSize,
        backgroundColor: calendarButton?.backgroundColor,
        color: calendarButton?.color,
        ...addBorderRadiusDefaultStyle(calendarButton),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Top",
          borderWidth: calendarButton?.borderTopWidth,
          borderColor: calendarButton?.borderTopColor,
        }),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Bottom",
          borderWidth: calendarButton?.borderBottomWidth,
          borderColor: calendarButton?.borderBottomColor,
        }),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Left",
          borderWidth: calendarButton?.borderLeftWidth,
          borderColor: calendarButton?.borderLeftColor,
        }),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Right",
          borderWidth: calendarButton?.borderRightWidth,
          borderColor: calendarButton?.borderRightColor,
        }),
      },
    ".calculation-parameters .EUCalendar .EUCalendar-bottomBar .EUCalendar-bottomBar-today-hover, .calculation-parameters .EUCalendar .EUCalendar-menu-today-hover":
      {
        backgroundColor: calendarActiveButton?.backgroundColor,
        color: calendarActiveButton?.color,
        ...addBorderRadiusDefaultStyle(calendarActiveButton),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Top",
          borderWidth: calendarActiveButton?.borderTopWidth,
          borderColor: calendarActiveButton?.borderTopColor,
        }),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Bottom",
          borderWidth: calendarActiveButton?.borderBottomWidth,
          borderColor: calendarActiveButton?.borderBottomColor,
        }),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Left",
          borderWidth: calendarActiveButton?.borderLeftWidth,
          borderColor: calendarActiveButton?.borderLeftColor,
        }),
        ...addBorderEdgeSolidDefaultStyle({
          edge: "Right",
          borderWidth: calendarActiveButton?.borderRightWidth,
          borderColor: calendarActiveButton?.borderRightColor,
        }),
      },
    ".calculation-parameters .EUCalendar": {
      ...calendarFont,
      ...calendarPadding,
      ...calendarBorderRadius,
      ...markImportantCssProperty({ fontSize: calendarFont?.fontSize ?? fontSize }),
      textShadow: 'unset',
    },
    ".EUCalendar-menu-yearLabel div, .EUCalendar-title div": {
      textShadow: 'unset',
    },
    ".EUCalendar-day-selected, .EUCalendar-hover-date": {
      padding: 'unset',
    },
    ".EUCalendar-hover-week": {
      backgroundColor: 'unset',
    },
    ".EUCalendar-day-today": {
      fontWeight: 'unset',
      color: 'unset',
    },
    ".EUCalendar-dayNames div, .EUCalendar-day, .EUCalendar-weekNumber": {
      width: '1.875rem',
      height: '1.875rem',
      lineHeight: '1.875rem',
      fontSize: '0.875rem'
    },
    ".ac_results": {
      boxShadow: "unset",
      "-webkit-box-shadow": "unset",
    },
    ".ac_results li": {
      ...dropdownNormal,
      border: "unset",
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: dropdownNormal?.borderWidth,
        borderColor: dropdownNormal?.borderColor,
      }),
    },
    ".ac_results li[aria-selected=true], .ac_results li.ac_over": {
      ...dropdownActive,
      border: "unset",
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: dropdownActive?.borderWidth,
        borderColor: dropdownActive?.borderColor,
      }),
    },
    "body[dir='rtl'] .highcharts-container .highcharts-tooltip > span": {
      direction: 'rtl',
      textAlign: 'right'
    },
    ".chart-type": {
      borderRadius: 'unset',
      position: 'relative',
      background: 'unset',
    },
    "button.chart-type.cumulative::before": {
      content: "'*'",
      color: 'transparent',
      width: '30px',
      height: '30px',
      position: 'absolute',
      top: '50%',
      right: '50%',
      background: 'transparent url("//dev.vn.euroland.com/tools/investmentcal2/Content/Images/chart-type.gif") no-repeat',
      transform: "translate(15px, -15px)",
    },
    "button.chart-type.periodical::before": {
      content: "'*'",
      color: 'transparent',
      width: '30px',
      height: '30px',
      position: 'absolute',
      top: '50%',
      right: '50%',
      background: 'transparent url("//dev.vn.euroland.com/tools/investmentcal2/Content/Images/chart-type.gif") no-repeat -30px',
      transform: "translate(15px, -15px)",
    }
  };
  console.log("result", result);
  return result;
}

export function convertFormDataToCssSelector(evaluatedSettings) {
  const output = [];

  // Iterate over each selector in evaluatedSettings
  for (const selector in evaluatedSettings) {
    const propertiesObj = evaluatedSettings[selector];
    const properties = [];

    if (!propertiesObj) continue;

    // Iterate over each property in the propertiesObj
    for (const cssPropertyName in propertiesObj) {
      if (cssPropertyName === SPECIAL_PROPERTIES.MEDIA) continue;
      const value = propertiesObj[cssPropertyName];

      if (!value) continue;

      properties.push({ name: cssPropertyName, value });
    }

    if (!properties.length) continue;

    // Create new selector object
    const newSelector = {
      selector,
      properties,
    };

    // If media property exists in propertiesObj, add it to newSelector
    if (propertiesObj[SPECIAL_PROPERTIES.MEDIA]) {
      newSelector.media = propertiesObj[SPECIAL_PROPERTIES.MEDIA];
    }

    output.push(newSelector);
  }

  return output;
}
