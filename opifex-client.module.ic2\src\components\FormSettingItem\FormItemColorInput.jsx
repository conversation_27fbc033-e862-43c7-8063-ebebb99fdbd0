import { useEffect, useState } from "react";
import { ColorPicker, Form } from "antd";
import useDebounce from "../../hooks/useDebounce";

const FormItemColorInput = ({ name, label = "", className }) => {
  return (
    <Form.Item name={name} label={label} className={className}>
      <HexColorInputPicker showText className="w-[100%] flex justify-start" />
    </Form.Item>
  );
};

const HexColorInputPicker = ({ onChange, value }) => {
  const [selectedColor, setSelectedColor] = useState(value);

  const debouncedOnColorChange = useDebounce(() => {
    onChange(selectedColor?.toHexString());
  });

  const handleColorChange = (color) => {
    const changedColor = color.cleared ? undefined : color;
    setSelectedColor(changedColor);
    debouncedOnColorChange();
  };

  useEffect(() => {
    setSelectedColor(value);
  }, [value]);

  return (
    <ColorPicker
      value={selectedColor}
      onChange={handleColorChange}
      showText={(color) =>
        !color?.cleared ? color.toHexString().toUpperCase() : "Pick color"
      }
      allowClear
      className="w-[100%] flex justify-start"
    />
  );
};

export { FormItemColorInput };
