import {
  addBorderEdgeSolidDefaultStyle,
  addBorderRadiusDefaultStyle,
  addBorderSolidDefaultStyle,
  markImportantCssProperty,
} from "../utils/common";
import { SPECIAL_PROPERTIES } from "../constant/selectorApiCss";

export function getGeneralSettings({
  generalTools,
  companyFonts,
  basicSettings,
}) {
  console.log("generalTools", generalTools);
  const { mainFont } = basicSettings;
  const {
    // Period buttons (Live, 1M, 3M, etc.)
    buttonNormal,
    buttonActive,
    buttonFont,
    buttonBorder,
    buttonBorderRadius,
    buttonPadding,
    
    // Tables (share info, market share)
    tableHeader,
    tableHeaderBorder,
    tableHeaderDataColumn,
    // tableHeaderNameColumn,
    // tableBody,
    tableBodyBorder,
    tableBodyDataColumn,
    // tableBodyNameColumn,
    tableBodyNormal,
    tableBodyActive,
    tableOddRowColor,
    tableEvenRowColor,
    tableBorder,
    tableBorderRadius,
    
    // Headings
    heading,
    
    // Links
    hyperlinkNormal,
    hyperlinkActive,
    
    // Inputs and form elements
    // input = {},
    // inputBorder = {},
    // inputBorderRadius = {},
    // inputPadding = {},
    checkboxNormal = {},
    
    // Calendar
    calendarActiveButton,
    calendarActiveDate,
    // calendarBorderRadius,
    calendarButton,
    calendarDate,
    calendarFont,
    calendarPadding,
    tab,
    tabActive,
    tabPadding,
    tabFont,
    tabAlignment,
    tabBorderRadius,
  } = generalTools;

  const textColor = mainFont?.color;
  const fontFamily = mainFont?.fontFamily;
  const fontSize = mainFont?.fontSize && `${mainFont?.fontSize}px`;
  const result = {
    // Base body styles
    body: {
      fontFamily: fontFamily,
      fontSize: fontSize,
      color: textColor,
    },
    
    // Font faces
    "@font-face": companyFonts?.map((item) => item.css),
    
    // Main heading
    ".main-heading": {
      ...(heading || {}),
    },
    
    // Second level headings (Fragmentation, Market share, Activity trend)
    ".title.second-heading": {
      ...(heading || {}),
    },

    ".highcharts-tooltip *": {
      ...(fontFamily && markImportantCssProperty({fontFamily})),
      ...(fontSize && markImportantCssProperty({fontSize})),
      ...(textColor && markImportantCssProperty({color: textColor})),
    },

    ".highcharts-data-labels *": {
      ...(fontFamily && markImportantCssProperty({fontFamily})),
      ...(fontSize && markImportantCssProperty({fontSize})),
      ...(textColor && markImportantCssProperty({color: textColor})),
    },

    ".highcharts-axis-labels *": {
      ...(fontFamily && markImportantCssProperty({fontFamily})),
      ...(fontSize && markImportantCssProperty({fontSize})),
      ...(textColor && markImportantCssProperty({color: textColor})),
    },
    
    // Share type selection table
    ".share-type-selection table": {
      ...(tableBorder || {}),
      ...addBorderRadiusDefaultStyle(tableBorderRadius),
      ...(tableBorder?.borderWidth && { borderStyle: "solid" }),
    },
    
    ".share-type-selection .table-header": {
      ...(tableHeader || {}),
      ...(tableHeaderBorder || {}),
    },
    
    ".share-type-selection .table-row": {
      ...(tableBodyNormal || {}),
      ...(tableBodyBorder || {}),
    },
    
    ".share-type-selection .table-row:hover": {
      ...(tableBodyActive || {}),
    },
    
    // Share info table
    ".table-share-info": {
      ...(tableBorder || {}),
      ...addBorderRadiusDefaultStyle(tableBorderRadius),
      ...(tableBorder?.borderWidth && { borderStyle: "solid" }),
    },
    
    ".table-share-info .table-header": {
      ...(tableHeader || {}),
      ...(tableHeaderBorder || {}),
    },
    
    ".table-share-info .table-of-instruments-header": {
      ...(tableHeaderDataColumn || {}),
    },
    
    ".table-share-info .table-row": {
      ...(tableBodyNormal || {}),
      ...(tableBodyBorder || {}),
    },
    
    ".table-share-info .table-row:hover": {
      ...(tableBodyActive || {}),
    },
    
    ".table-share-info .table-of-instrument-row": {
      ...(tableBodyDataColumn || {}),
    },
    
    // Row alternating colors
    ".table-share-row-odd": {
      ...(tableOddRowColor || {}),
    },
    
    ".table-share-row-even": {
      ...(tableEvenRowColor || {}),
    },
    
    // Market share table
    ".table-market-share": {
      ...(tableBorder || {}),
      ...addBorderRadiusDefaultStyle(tableBorderRadius),
      ...(tableBorder?.borderWidth && { borderStyle: "solid" }),
    },
    
    ".table-market-share .table-header": {
      ...(tableHeader || {}),
      ...(tableHeaderBorder || {}),
    },
    
    ".table-market-share .table-of-market-share-header": {
      ...(tableHeaderDataColumn || {}),
    },
    
    ".table-market-share .table-row": {
      ...(tableBodyNormal || {}),
      ...(tableBodyBorder || {}),
    },
    
    ".table-market-share .market-share-row": {
      ...(tableBodyDataColumn || {}),
    },
    

    ".fragmentation .period-button": {
      ...addBorderSolidDefaultStyle(tabActive),
      ...addBorderRadiusDefaultStyle(tabBorderRadius),
      ...(tabPadding || {}),
      ...(tabFont || {}),
      ...tabAlignment,
      backgroundColor: tab?.backgroundColor,
      color: tab?.color,
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: tab?.borderTopWidth,
        borderColor: tab?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: tab?.borderBottomWidth,
        borderColor: tab?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: tab?.borderLeftWidth,
        borderColor: tab?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: tab?.borderRightWidth,
        borderColor: tab?.borderRightColor,
      }),
    },
    
    ".fragmentation .period-button.period-selected": {
      color: tabActive?.color,
      ...(tabActive || {}),
      ...markImportantCssProperty(addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: tabActive?.borderTopWidth,
        borderColor: tabActive?.borderTopColor,
      })),
      ...markImportantCssProperty(addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: tabActive?.borderBottomWidth,
        borderColor: tabActive?.borderBottomColor,
      })),
      ...markImportantCssProperty(addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: tabActive?.borderLeftWidth,
        borderColor: tabActive?.borderLeftColor,
      })),
      ...markImportantCssProperty(addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: tabActive?.borderRightWidth,
        borderColor: tabActive?.borderRightColor,
      })),
      ...markImportantCssProperty({backgroundColor: tabActive?.backgroundColor}),
    },
    
    // Custom range calendar button
    ".show-data": {
      backgroundColor: buttonNormal?.backgroundColor,
      color: buttonNormal?.color,
      ...(buttonPadding || {}),
      ...(buttonFont || {}),
      ...addBorderRadiusDefaultStyle(buttonBorderRadius),
      ...addBorderSolidDefaultStyle(buttonBorder),
    },
    
    ".show-data:hover": {
      ...(buttonActive || {}),
    },
    
    // Radio buttons and checkboxes
    ".radio input, input[type='radio']": {
      ...(checkboxNormal || {}),
    },
    
    // Links and hyperlinks
    ".hyperlink, div.disclaimer-box a, div.cookies-box a": {
      ...markImportantCssProperty(hyperlinkNormal),
      textDecoration: hyperlinkNormal?.textDecoration ?? "unset",
    },
    
    "a:hover, .hyperlink:hover": {
      ...markImportantCssProperty(hyperlinkActive),
    },
    
    // Footer text
    ".footer": {
      fontFamily: fontFamily,
      fontSize: fontSize,
      color: textColor,
    },
    
    // Time and date labels
    ".time-label, .time, .selected-period": {
      fontFamily: fontFamily,
      fontSize: fontSize,
      color: textColor,
    },
    
    // Calendar styles
    ".EUCalendar .EUCalendar-day-selected": {
      ...calendarDate,
      backgroundColor: calendarDate?.backgroundColor,
      ...(calendarDate?.color
        ? {
            color: `${calendarDate.color} !important`,
          }
        : {}),
      ...(calendarDate?.borderWidth && calendarDate?.borderColor
        ? {
            border: `${calendarDate?.borderWidth} solid ${calendarDate?.borderColor}`,
          }
        : {}),
    },
    
    ".EUCalendar .EUCalendar-hover-date": {
      backgroundColor: calendarActiveDate?.backgroundColor,
      color: calendarActiveDate?.color,
      ...addBorderSolidDefaultStyle(calendarActiveDate),
    },
    
    ".EUCalendar .EUCalendar-bottomBar-today, .EUCalendar .EUCalendar-menu-today": {
      backgroundColor: calendarButton?.backgroundColor,
      color: calendarButton?.color,
      ...addBorderRadiusDefaultStyle(calendarButton),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: calendarButton?.borderTopWidth,
        borderColor: calendarButton?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: calendarButton?.borderBottomWidth,
        borderColor: calendarButton?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: calendarButton?.borderLeftWidth,
        borderColor: calendarButton?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: calendarButton?.borderRightWidth,
        borderColor: calendarButton?.borderRightColor,
      }),
    },
    
    ".EUCalendar .EUCalendar-bottomBar-today-hover, .EUCalendar .EUCalendar-menu-today-hover": {
      backgroundColor: calendarActiveButton?.backgroundColor,
      color: calendarActiveButton?.color,
      ...addBorderRadiusDefaultStyle(calendarActiveButton),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Top",
        borderWidth: calendarActiveButton?.borderTopWidth,
        borderColor: calendarActiveButton?.borderTopColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Bottom",
        borderWidth: calendarActiveButton?.borderBottomWidth,
        borderColor: calendarActiveButton?.borderBottomColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Left",
        borderWidth: calendarActiveButton?.borderLeftWidth,
        borderColor: calendarActiveButton?.borderLeftColor,
      }),
      ...addBorderEdgeSolidDefaultStyle({
        edge: "Right",
        borderWidth: calendarActiveButton?.borderRightWidth,
        borderColor: calendarActiveButton?.borderRightColor,
      }),
    },
    
    ".EUCalendar": {
      ...calendarFont,
      ...calendarPadding,
    },
  };
  
  console.log("result", result);
  return result;
}

export function convertFormDataToCssSelector(evaluatedSettings) {
  const output = [];

  // Iterate over each selector in evaluatedSettings
  for (const selector in evaluatedSettings) {
    const propertiesObj = evaluatedSettings[selector];
    const properties = [];

    if (!propertiesObj) continue;

    // Iterate over each property in the propertiesObj
    for (const cssPropertyName in propertiesObj) {
      if (cssPropertyName === SPECIAL_PROPERTIES.MEDIA) continue;
      const value = propertiesObj[cssPropertyName];

      if (!value) continue;

      properties.push({ name: cssPropertyName, value });
    }

    if (!properties.length) continue;

    // Create new selector object
    const newSelector = {
      selector,
      properties,
    };

    // If media property exists in propertiesObj, add it to newSelector
    if (propertiesObj[SPECIAL_PROPERTIES.MEDIA]) {
      newSelector.media = propertiesObj[SPECIAL_PROPERTIES.MEDIA];
    }

    output.push(newSelector);
  }

  return output;
}
