import { useState, useEffect, useRef, Suspense } from 'react';

const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const targetRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsIntersecting(true);
        setHasIntersected(true);
        if (options.once !== false) {
          observer.unobserve(entry.target);
        }
      } else {
        setIsIntersecting(false);
      }
    }, {
      threshold: options.threshold || 0.1,
      rootMargin: options.rootMargin || '50px',
      ...options
    });

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => {
      if (targetRef.current) {
        observer.unobserve(targetRef.current);
      }
    };
  }, [options.threshold, options.rootMargin, options.once]);

  return [targetRef, isIntersecting, hasIntersected];
};

const LazyLoad = ({ 
  children, 
  fallback = <div className="w-full h-32 bg-gray-100 animate-pulse rounded"></div>,
  threshold = 0.1,
  rootMargin = '50px',
  once = true,
  placeholder,
  className = ''
}) => {
  const [ref, isIntersecting, hasIntersected] = useIntersectionObserver({
    threshold,
    rootMargin,
    once
  });

  const shouldRender = once ? hasIntersected : isIntersecting;

  return (
    <div ref={ref} className={className}>
      {shouldRender ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        placeholder || fallback
      )}
    </div>
  );
};

export default LazyLoad;