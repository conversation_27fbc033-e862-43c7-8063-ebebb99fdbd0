{"version": 3, "sources": ["../../__mf__virtual/sg3_mf_2_project_mf_2_name__loadShare__react__loadShare__.js"], "sourcesContent": ["\n    \n    ;() => import(\"__mf__virtual/sg3_mf_2_project_mf_2_name__prebuild__react__prebuild__.js\").catch(() => {});\n    // dev uses dynamic import to separate chunks\n    ;() => import(\"react\").catch(() => {});\n    const {loadShare} = require(\"@module-federation/runtime\")\n    const {initPromise} = require(\"__mf__virtual/sg3_mf_2_project_mf_2_name__mf_v__runtimeInit__mf_v__.js\")\n    const res = initPromise.then(_ => loadShare(\"react\", {\n    customShareInfo: {shareConfig:{\n      singleton: true,\n      strictVersion: false,\n      requiredVersion: \"^18.3.1\"\n    }}}))\n    const exportModule = /*mf top-level-await placeholder replacement mf*/res.then(factory => factory())\n    module.exports = exportModule\n  "], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAKI,QAAM,EAAC,UAAS,IAAI;AACpB,QAAM,EAAC,YAAW,IAAI;AACtB,QAAM,MAAM,YAAY,KAAK,OAAK,UAAU,SAAS;AAAA,MACrD,iBAAiB,EAAC,aAAY;AAAA,QAC5B,WAAW;AAAA,QACX,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB,EAAC;AAAA,IAAC,CAAC,CAAC;AACJ,QAAM;AAAA;AAAA,MAAgE,IAAI,KAAK,aAAW,QAAQ,CAAC;AAAA;AACnG,WAAO,UAAU;AAAA;AAAA;", "names": []}