import { useContext, useRef } from "react";

import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";
import { SettingsContext } from "../../context/SettingsContext";

const CurrencySelect = ({ value, onChange, form }) => {
  const oldValue = useRef([]);
  const { inputStyle } = useInputStyle();
  const { currencies, basicSettings } = useContext(SettingsContext);
  const { availableLanguages } = basicSettings;

  const handleChange = (_value) => {
    const changedValue = _value.filter(
      (item) => !oldValue.current.includes(item)
    );
    changedValue.forEach((currencyCode) => {
      form.setFieldValue(["currencyEdit", currencyCode, "DecimalDigits"], 2);
      availableLanguages.forEach((lang) => {
        form.setFieldValue(
          ["currencyEdit", currencyCode, "Text", lang],
          currencyCode
        );
      });
    });
    onChange?.(_value);

    // Save the old value
    oldValue.current = [..._value];
  };

  return (
    <ReactAntdDraggableSelect
      sortingStrategy="vertical"
      showSearch
      value={value}
      placeholder="Select a currency"
      optionFilterProp="children"
      onChange={handleChange}
      style={inputStyle}
      options={currencies.map((option) => ({
        value: option.id,
        label: option.label,
      }))}
      allowClear
    />
  );
};

export default CurrencySelect;
