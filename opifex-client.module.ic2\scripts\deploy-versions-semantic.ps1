# Get current release version from semantic-release
# This assumes the version is available in environment variable or you can get it from semantic-release output

<#
.SYNOPSIS
Deploys an application to a server based on the current release version.

.DESCRIPTION
This script retrieves the current release version from an environment variable or a JSON file, converts it to a comparable format, and checks against existing versions to determine if it is the highest. It then deploys the application to a specific version folder and optionally to a 'latest' folder if it is the highest version.

.PARAMETER source
The source of the deployment files.

.PARAMETER siteName
The name of the site to deploy.

.PARAMETER appPath
The path where the application will be deployed.

.PARAMETER environment
The deployment environment (e.g., production, staging).

.PARAMETER user
The username for server authentication.

.PARAMETER passwd
The password for server authentication.

.PARAMETER server
The server address for deployment.

.PARAMETER port
The port number for server connection.

.NOTES
Ensure that semantic-release has run successfully to provide the current version. The script uses Git tags or GitLab API to retrieve existing versions.

.EXAMPLE
.\deploy.ps1 -source "src" -siteName "MySite" -appPath "/var/www" -environment "production" -user "admin" -passwd "password" -server "***********" -port 22
#>
param(
  [string]$source,
  [string]$siteName,
  [string]$appPath,
  [string]$environment,
  [string]$user,
  [string]$passwd,
  [string]$server,
  [int]$port
)

# Check if we're on a Git tag
if ($env:CI_COMMIT_TAG) {
    $currentVersion = $env:CI_COMMIT_TAG -replace '^v|-[a-zA-Z0-9\.\-]+$', ''
    Write-Host "Using version from Git tag: $currentVersion"
}
# Check if we're on a release branch
# elseif ((git symbolic-ref --short HEAD) -match '^release/v') {
#     $currentVersion = (git symbolic-ref --short HEAD) -replace '^release/v', ''
#     Write-Host "Using version from release branch: $currentVersion"
# }
else {
    # Use version.json
    $currentVersion = (Get-Content -Raw -Path version.json | ConvertFrom-Json).version
    Write-Host "Using development version: $currentVersion"
}

# # Get version information
# if ($env:CI_COMMIT_TAG) {
#   $currentVersion = $env:CI_COMMIT_TAG -replace '^v|-[a-zA-Z0-9\.\-]+$', ''
# }
# else {
#   $currentVersion = (Get-Content -Raw -Path version.json | ConvertFrom-Json).version
# }


if (-not $currentVersion) {
  Write-Error "Current version not found. Make sure semantic-release has run successfully."
  exit 1
}

Write-Host "Current release version: $currentVersion"

# Convert version to comparable format (e.g., "1.0.0" -> 100, "1.2.3" -> 123)
function ConvertTo-VersionNumber {
  param([string]$version)

  # Remove 'v' prefix if present
  $cleanVersion = $version -replace '^v|-[a-zA-Z0-9\.\-]+$', ''

  # Split by '-' to separate main version from pre-release
  $versionParts = $cleanVersion.Split('-', 2)

  $mainVersion = $versionParts[0]

  # Split version into parts
  $parts = $mainVersion.Split('.')

  # Ensure we have at least 3 parts (major.minor.patch)
  while ($parts.Length -lt 3) {
    $parts += "0"
  }

  # Convert to integer representation
  $major = [int]$parts[0]
  $minor = [int]$parts[1]
  $patch = [int]$parts[2]

  return ($major * 100) + ($minor * 10) + $patch
}

# Get current version number
$currentVersionNumber = ConvertTo-VersionNumber -version $currentVersion

# Function to get all existing version numbers from your deployment location
function Get-ExistingVersions {
  # Using Git tags to get latest release versions
  $gitTags = git tag -l --sort=-version:refname | Where-Object { $_ -match '^v?\d+\.\d+\.\d+(-[a-zA-Z0-9\.\-]+)?$' }
  $existingVersions = $gitTags | ForEach-Object { ConvertTo-VersionNumber -version $_ }

  # Option 3: If using GitLab API to get existing releases
  # $projectId = $env:CI_PROJECT_ID
  # $gitlabToken = $env:GITLAB_TOKEN
  # $apiUrl = "https://gitlab.euroland.com/api/v4/projects/$projectId/releases"
  # $headers = @{ "PRIVATE-TOKEN" = $gitlabToken }
  # $releases = Invoke-RestMethod -Uri $apiUrl -Headers $headers
  # $existingVersions = $releases | ForEach-Object { ConvertTo-VersionNumber -version $_.tag_name }

  return $existingVersions
}

# Get all existing versions
$existingVersions = Get-ExistingVersions

Write-Host "[INFO] Existing versions: $($existingVersions -join ', ')"
Write-Host "[INFO] Current version number: $currentVersionNumber"

# Check if current version is the highest
$isHighestVersion = $true
if ($existingVersions -and $existingVersions.Count -gt 0) {
  $highestExistingVersion = ($existingVersions | Measure-Object -Maximum).Maximum
  Write-Host "[INFO] Highest existing version: $highestExistingVersion"

  if ($currentVersionNumber -le $highestExistingVersion) {
    $isHighestVersion = $false
  }
}

$appPath = $appPath -replace '\/$', ''  # Ensure appPath does not end with a slash

$deployParams = @{
  source      = $source
  siteName    = $siteName
  appPath     = $appPath
  environment = $environment
  user        = $user
  passwd      = $passwd
  server      = $server
  port        = $port
}

# Set deployment strategy
if ($environment -eq "QA" -or $isHighestVersion) {
  # Just keeps deploying latest version to QA environment
  if($environment -ne "QA") {
    # Deployment commands for specific version
    Write-Host "[SUCCESS] Current version is the highest. Will deploy to both specific version and latest."
    $env:DEPLOY_TO_LATEST = "true"
    $deployParams.appPath = $appPath + "/$currentVersionNumber";
    Write-Host "[INFO] Deploying to version folder: $currentVersionNumber"

    & ".\scripts\ms_deploy.ps1" @deployParams
  }

  # Deployment commands for latest
  $deployParams.appPath = $appPath + "/latest";
  Write-Host "[INFO] Deploying to latest folder"
  & ".\scripts\ms_deploy.ps1" @deployParams

}
else {
  Write-Host "[INFO] Current version is not the highest. Will deploy to specific version only."
  $env:DEPLOY_TO_LATEST = "false"
  $deployParams.appPath = $appPath + "/$currentVersionNumber";

  # Deployment commands for specific version only
  Write-Host "Deploying to version folder: $currentVersionNumber"
  & ".\scripts\ms_deploy.ps1" @deployParams
}

Write-Host "[SUCCESS] Deployment completed successfully!"
