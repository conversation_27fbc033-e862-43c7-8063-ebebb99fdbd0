import { FormSetting } from "../FormSettingItem";
import { FORM_ITEM_TYPES } from "../FormSettingItem/type";

export const CssColor = () => {
  const HYPER_LINK_SETTINGS = [
    {
      title: "Main Text",
      content: [
        {
          label: "Font Size",
          name: ["MainTextFontSize"],
          type: FORM_ITEM_TYPES.SIZE,
        },
        {
          label: "Color",
          name: ["MainTextColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Font family",
          name: ["MainTextFont"],
          type: FORM_ITEM_TYPES.SELECT_FONT_FAMILY,
        },
      ],
    },
    {
      title: "Main Heading",
      content: [
        {
          label: "Font Size",
          name: ["MainHeadingFontSize"],
          type: FORM_ITEM_TYPES.SIZE,
        },
        {
          label: "Color",
          name: ["MainHeadingColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Font family",
          name: ["MainHeadingFont"],
          type: FORM_ITEM_TYPES.SELECT_FONT_FAMILY,
        },
      ],
    },
    {
      title: "Share Price",
      content: [
        {
          label: "Share Price Value Increase Color",
          name: ["SharePriceValueIncreaseColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Share Price Value Decrease Color",
          name: ["SharePriceValueDecreaseColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Dividend Color",
          name: ["DividendColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
      ],
    },
    {
      title: "Link",
      content: [
        {
          label: "Link Color",
          name: ["LinkColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Link Hover Color",
          name: ["LinkHoverColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Link Underline",
          name: ["LinkUnderline"],
          type: FORM_ITEM_TYPES.CHECKBOX,
        },
      ],
    },
    {
      title: "Table",
      content: [
        {
          label: "Table Odd Row Background Color",
          name: ["TableOddRowColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Table Even Row Background Color",
          name: ["TableEvenRowColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
      ],
    },
    {
      title: "Tab Background Color",
      content: [
        {
          label: "Tab Active Background Color",
          name: ["TabActiveColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Tab Deactive Background Color",
          name: ["TabDeactiveColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
      ],
    },
    {
      title: "Table Inline",
      content: [
        {
          label: "Border Enable",
          name: ["TableInlineBorderEnable"],
          type: FORM_ITEM_TYPES.CHECKBOX,
        },
        {
          label: "Border Color",
          name: ["TableInlineBorderColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
      ],
    },
    {
      title: "Table Outline",
      content: [
        {
          label: "Border Enable",
          name: ["TableOutlineBorderEnable"],
          type: FORM_ITEM_TYPES.CHECKBOX,
        },
        {
          label: "Border Color",
          name: ["TableOutlineBorderColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
      ],
    },
    {
      title: "Table Head",
      content: [
        {
          label: "Font Size",
          name: ["TableHeadFontSize"],
          type: FORM_ITEM_TYPES.SIZE,
        },
        {
          label: "Text Color",
          name: ["TableHeadColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Background Color",
          name: ["TableHeadBackgroundColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
        {
          label: "Font family",
          name: ["TableHeadFont"],
          type: FORM_ITEM_TYPES.SELECT_FONT_FAMILY,
        },
      ],
    },
    {
      title: "Chart",
      content: [
        {
          label: "Chart Colors",
          name: ["ChartColors"],
          type: FORM_ITEM_TYPES.MULTIPLECOLOR,
        },
        {
          label: "Value Color",
          name: ["ValueColor"],
          type: FORM_ITEM_TYPES.COLOR,
        },
      ],
    },
  ];
  return (
    <div>
      <FormSetting settings={HYPER_LINK_SETTINGS} />
    </div>
  );
};
