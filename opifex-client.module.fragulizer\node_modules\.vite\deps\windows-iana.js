import "./chunk-ZKAWKZG5.js";

// node_modules/windows-iana/dist/windows-iana.esm.js
var IANA_ALIAS_MAP = [{
  name: "adalv",
  description: "Andorra",
  alias: ["Europe/Andorra"]
}, {
  name: "aedxb",
  description: "Dubai, United Arab Emirates",
  alias: ["Asia/Dubai"]
}, {
  name: "afkbl",
  description: "Kabul, Afghanistan",
  alias: ["Asia/Kabul"]
}, {
  name: "aganu",
  description: "Antigua",
  alias: ["America/Antigua"]
}, {
  name: "aia<PERSON>",
  description: "<PERSON><PERSON><PERSON>",
  alias: ["America/Anguilla"]
}, {
  name: "al<PERSON>",
  description: "Tirane, Albania",
  alias: ["Europe/Tirane"]
}, {
  name: "amevn",
  description: "Yerevan, Armenia",
  alias: ["Asia/Yerevan"]
}, {
  name: "ancur",
  description: "Curaçao",
  alias: ["America/Curacao"]
}, {
  name: "aola<PERSON>",
  description: "Luanda, Angola",
  alias: ["Africa/Luanda"]
}, {
  name: "aq<PERSON>",
  description: "Casey Station, Bailey Peninsula",
  alias: ["Antarctica/Casey"]
}, {
  name: "aqdav",
  description: "Davis Station, Vestfold Hills",
  alias: ["Antarctica/Davis"]
}, {
  name: "aqddu",
  description: "Dumont d'Urville Station, Terre Adélie",
  alias: ["Antarctica/DumontDUrville"]
}, {
  name: "aqmaw",
  description: "Mawson Station, Holme Bay",
  alias: ["Antarctica/Mawson"]
}, {
  name: "aqmcm",
  description: "McMurdo Station, Ross Island",
  alias: ["Antarctica/McMurdo"]
}, {
  name: "aqplm",
  description: "Palmer Station, Anvers Island",
  alias: ["Antarctica/Palmer"]
}, {
  name: "aqrot",
  description: "Rothera Station, Adelaide Island",
  alias: ["Antarctica/Rothera"]
}, {
  name: "aqsyw",
  description: "Syowa Station, East Ongul Island",
  alias: ["Antarctica/Syowa"]
}, {
  name: "aqtrl",
  description: "Troll Station, Queen Maud Land",
  alias: ["Antarctica/Troll"]
}, {
  name: "aqvos",
  description: "Vostok Station, Lake Vostok",
  alias: ["Antarctica/Vostok"]
}, {
  name: "arbue",
  description: "Buenos Aires, Argentina",
  alias: ["America/Buenos_Aires", "America/Argentina/Buenos_Aires"]
}, {
  name: "arcor",
  description: "Córdoba, Argentina",
  alias: ["America/Cordoba", "America/Argentina/Cordoba", "America/Rosario"]
}, {
  name: "arctc",
  description: "Catamarca, Argentina",
  alias: ["America/Catamarca", "America/Argentina/Catamarca", "America/Argentina/ComodRivadavia"]
}, {
  name: "arirj",
  description: "La Rioja, Argentina",
  alias: ["America/Argentina/La_Rioja"]
}, {
  name: "arjuj",
  description: "Jujuy, Argentina",
  alias: ["America/Jujuy", "America/Argentina/Jujuy"]
}, {
  name: "arluq",
  description: "San Luis, Argentina",
  alias: ["America/Argentina/San_Luis"]
}, {
  name: "armdz",
  description: "Mendoza, Argentina",
  alias: ["America/Mendoza", "America/Argentina/Mendoza"]
}, {
  name: "arrgl",
  description: "Río Gallegos, Argentina",
  alias: ["America/Argentina/Rio_Gallegos"]
}, {
  name: "arsla",
  description: "Salta, Argentina",
  alias: ["America/Argentina/Salta"]
}, {
  name: "artuc",
  description: "Tucumán, Argentina",
  alias: ["America/Argentina/Tucuman"]
}, {
  name: "aruaq",
  description: "San Juan, Argentina",
  alias: ["America/Argentina/San_Juan"]
}, {
  name: "arush",
  description: "Ushuaia, Argentina",
  alias: ["America/Argentina/Ushuaia"]
}, {
  name: "asppg",
  description: "Pago Pago, American Samoa",
  alias: ["Pacific/Pago_Pago", "Pacific/Samoa", "US/Samoa"]
}, {
  name: "atvie",
  description: "Vienna, Austria",
  alias: ["Europe/Vienna"]
}, {
  name: "auadl",
  description: "Adelaide, Australia",
  alias: ["Australia/Adelaide", "Australia/South"]
}, {
  name: "aubhq",
  description: "Broken Hill, Australia",
  alias: ["Australia/Broken_Hill", "Australia/Yancowinna"]
}, {
  name: "aubne",
  description: "Brisbane, Australia",
  alias: ["Australia/Brisbane", "Australia/Queensland"]
}, {
  name: "audrw",
  description: "Darwin, Australia",
  alias: ["Australia/Darwin", "Australia/North"]
}, {
  name: "aueuc",
  description: "Eucla, Australia",
  alias: ["Australia/Eucla"]
}, {
  name: "auhba",
  description: "Hobart, Australia",
  alias: ["Australia/Hobart", "Australia/Tasmania"]
}, {
  name: "aukns",
  description: "Currie, Australia",
  alias: ["Australia/Currie"]
}, {
  name: "auldc",
  description: "Lindeman Island, Australia",
  alias: ["Australia/Lindeman"]
}, {
  name: "auldh",
  description: "Lord Howe Island, Australia",
  alias: ["Australia/Lord_Howe", "Australia/LHI"]
}, {
  name: "aumel",
  description: "Melbourne, Australia",
  alias: ["Australia/Melbourne", "Australia/Victoria"]
}, {
  name: "aumqi",
  description: "Macquarie Island Station, Macquarie Island",
  alias: ["Antarctica/Macquarie"]
}, {
  name: "auper",
  description: "Perth, Australia",
  alias: ["Australia/Perth", "Australia/West"]
}, {
  name: "ausyd",
  description: "Sydney, Australia",
  alias: ["Australia/Sydney", "Australia/ACT", "Australia/Canberra", "Australia/NSW"]
}, {
  name: "awaua",
  description: "Aruba",
  alias: ["America/Aruba"]
}, {
  name: "azbak",
  description: "Baku, Azerbaijan",
  alias: ["Asia/Baku"]
}, {
  name: "basjj",
  description: "Sarajevo, Bosnia and Herzegovina",
  alias: ["Europe/Sarajevo"]
}, {
  name: "bbbgi",
  description: "Barbados",
  alias: ["America/Barbados"]
}, {
  name: "bddac",
  description: "Dhaka, Bangladesh",
  alias: ["Asia/Dhaka", "Asia/Dacca"]
}, {
  name: "bebru",
  description: "Brussels, Belgium",
  alias: ["Europe/Brussels"]
}, {
  name: "bfoua",
  description: "Ouagadougou, Burkina Faso",
  alias: ["Africa/Ouagadougou"]
}, {
  name: "bgsof",
  description: "Sofia, Bulgaria",
  alias: ["Europe/Sofia"]
}, {
  name: "bhbah",
  description: "Bahrain",
  alias: ["Asia/Bahrain"]
}, {
  name: "bibjm",
  description: "Bujumbura, Burundi",
  alias: ["Africa/Bujumbura"]
}, {
  name: "bjptn",
  description: "Porto-Novo, Benin",
  alias: ["Africa/Porto-Novo"]
}, {
  name: "bmbda",
  description: "Bermuda",
  alias: ["Atlantic/Bermuda"]
}, {
  name: "bnbwn",
  description: "Brunei",
  alias: ["Asia/Brunei"]
}, {
  name: "bolpb",
  description: "La Paz, Bolivia",
  alias: ["America/La_Paz"]
}, {
  name: "bqkra",
  description: "Bonaire, Sint Estatius and Saba",
  alias: ["America/Kralendijk"]
}, {
  name: "braux",
  description: "Araguaína, Brazil",
  alias: ["America/Araguaina"]
}, {
  name: "brbel",
  description: "Belém, Brazil",
  alias: ["America/Belem"]
}, {
  name: "brbvb",
  description: "Boa Vista, Brazil",
  alias: ["America/Boa_Vista"]
}, {
  name: "brcgb",
  description: "Cuiabá, Brazil",
  alias: ["America/Cuiaba"]
}, {
  name: "brcgr",
  description: "Campo Grande, Brazil",
  alias: ["America/Campo_Grande"]
}, {
  name: "brern",
  description: "Eirunepé, Brazil",
  alias: ["America/Eirunepe"]
}, {
  name: "brfen",
  description: "Fernando de Noronha, Brazil",
  alias: ["America/Noronha", "Brazil/DeNoronha"]
}, {
  name: "brfor",
  description: "Fortaleza, Brazil",
  alias: ["America/Fortaleza"]
}, {
  name: "brmao",
  description: "Manaus, Brazil",
  alias: ["America/Manaus", "Brazil/West"]
}, {
  name: "brmcz",
  description: "Maceió, Brazil",
  alias: ["America/Maceio"]
}, {
  name: "brpvh",
  description: "Porto Velho, Brazil",
  alias: ["America/Porto_Velho"]
}, {
  name: "brrbr",
  description: "Rio Branco, Brazil",
  alias: ["America/Rio_Branco", "America/Porto_Acre", "Brazil/Acre"]
}, {
  name: "brrec",
  description: "Recife, Brazil",
  alias: ["America/Recife"]
}, {
  name: "brsao",
  description: "São Paulo, Brazil",
  alias: ["America/Sao_Paulo", "Brazil/East"]
}, {
  name: "brssa",
  description: "Bahia, Brazil",
  alias: ["America/Bahia"]
}, {
  name: "brstm",
  description: "Santarém, Brazil",
  alias: ["America/Santarem"]
}, {
  name: "bsnas",
  description: "Nassau, Bahamas",
  alias: ["America/Nassau"]
}, {
  name: "btthi",
  description: "Thimphu, Bhutan",
  alias: ["Asia/Thimphu", "Asia/Thimbu"]
}, {
  name: "bwgbe",
  description: "Gaborone, Botswana",
  alias: ["Africa/Gaborone"]
}, {
  name: "bymsq",
  description: "Minsk, Belarus",
  alias: ["Europe/Minsk"]
}, {
  name: "bzbze",
  description: "Belize",
  alias: ["America/Belize"]
}, {
  name: "cacfq",
  description: "Creston, Canada",
  alias: ["America/Creston"]
}, {
  name: "caedm",
  description: "Edmonton, Canada",
  alias: ["America/Edmonton", "Canada/Mountain"]
}, {
  name: "caffs",
  description: "Rainy River, Canada",
  alias: ["America/Rainy_River"]
}, {
  name: "cafne",
  description: "Fort Nelson, Canada",
  alias: ["America/Fort_Nelson"]
}, {
  name: "caglb",
  description: "Glace Bay, Canada",
  alias: ["America/Glace_Bay"]
}, {
  name: "cagoo",
  description: "Goose Bay, Canada",
  alias: ["America/Goose_Bay"]
}, {
  name: "cahal",
  description: "Halifax, Canada",
  alias: ["America/Halifax", "Canada/Atlantic"]
}, {
  name: "caiql",
  description: "Iqaluit, Canada",
  alias: ["America/Iqaluit"]
}, {
  name: "camon",
  description: "Moncton, Canada",
  alias: ["America/Moncton"]
}, {
  name: "capnt",
  description: "Pangnirtung, Canada",
  alias: ["America/Pangnirtung"]
}, {
  name: "careb",
  description: "Resolute, Canada",
  alias: ["America/Resolute"]
}, {
  name: "careg",
  description: "Regina, Canada",
  alias: ["America/Regina", "Canada/East-Saskatchewan", "Canada/Saskatchewan"]
}, {
  name: "casjf",
  description: "St. John's, Canada",
  alias: ["America/St_Johns", "Canada/Newfoundland"]
}, {
  name: "canpg",
  description: "Nipigon, Canada",
  alias: ["America/Nipigon"]
}, {
  name: "cathu",
  description: "Thunder Bay, Canada",
  alias: ["America/Thunder_Bay"]
}, {
  name: "cator",
  description: "Toronto, Canada",
  alias: ["America/Toronto", "Canada/Eastern"]
}, {
  name: "cavan",
  description: "Vancouver, Canada",
  alias: ["America/Vancouver", "Canada/Pacific"]
}, {
  name: "cawnp",
  description: "Winnipeg, Canada",
  alias: ["America/Winnipeg", "Canada/Central"]
}, {
  name: "caybx",
  description: "Blanc-Sablon, Canada",
  alias: ["America/Blanc-Sablon"]
}, {
  name: "caycb",
  description: "Cambridge Bay, Canada",
  alias: ["America/Cambridge_Bay"]
}, {
  name: "cayda",
  description: "Dawson, Canada",
  alias: ["America/Dawson"]
}, {
  name: "caydq",
  description: "Dawson Creek, Canada",
  alias: ["America/Dawson_Creek"]
}, {
  name: "cayek",
  description: "Rankin Inlet, Canada",
  alias: ["America/Rankin_Inlet"]
}, {
  name: "cayev",
  description: "Inuvik, Canada",
  alias: ["America/Inuvik"]
}, {
  name: "cayxy",
  description: "Whitehorse, Canada",
  alias: ["America/Whitehorse", "Canada/Yukon"]
}, {
  name: "cayyn",
  description: "Swift Current, Canada",
  alias: ["America/Swift_Current"]
}, {
  name: "cayzf",
  description: "Yellowknife, Canada",
  alias: ["America/Yellowknife"]
}, {
  name: "cayzs",
  description: "Atikokan, Canada",
  alias: ["America/Coral_Harbour", "America/Atikokan"]
}, {
  name: "cccck",
  description: "Cocos (Keeling) Islands",
  alias: ["Indian/Cocos"]
}, {
  name: "cdfbm",
  description: "Lubumbashi, Democratic Republic of the Congo",
  alias: ["Africa/Lubumbashi"]
}, {
  name: "cdfih",
  description: "Kinshasa, Democratic Republic of the Congo",
  alias: ["Africa/Kinshasa"]
}, {
  name: "cfbgf",
  description: "Bangui, Central African Republic",
  alias: ["Africa/Bangui"]
}, {
  name: "cgbzv",
  description: "Brazzaville, Republic of the Congo",
  alias: ["Africa/Brazzaville"]
}, {
  name: "chzrh",
  description: "Zurich, Switzerland",
  alias: ["Europe/Zurich"]
}, {
  name: "ciabj",
  description: "Abidjan, Côte d'Ivoire",
  alias: ["Africa/Abidjan"]
}, {
  name: "ckrar",
  description: "Rarotonga, Cook Islands",
  alias: ["Pacific/Rarotonga"]
}, {
  name: "clipc",
  description: "Easter Island, Chile",
  alias: ["Pacific/Easter", "Chile/EasterIsland"]
}, {
  name: "clpuq",
  description: "Punta Arenas, Chile",
  alias: ["America/Punta_Arenas"]
}, {
  name: "clscl",
  description: "Santiago, Chile",
  alias: ["America/Santiago", "Chile/Continental"]
}, {
  name: "cmdla",
  description: "Douala, Cameroon",
  alias: ["Africa/Douala"]
}, {
  name: "cnsha",
  description: "Shanghai, China",
  alias: ["Asia/Shanghai", "Asia/Chongqing", "Asia/Chungking", "Asia/Harbin", "PRC"]
}, {
  name: "cnurc",
  description: "Ürümqi, China",
  alias: ["Asia/Urumqi", "Asia/Kashgar"]
}, {
  name: "cobog",
  description: "Bogotá, Colombia",
  alias: ["America/Bogota"]
}, {
  name: "crsjo",
  description: "Costa Rica",
  alias: ["America/Costa_Rica"]
}, {
  name: "cst6cdt",
  description: "POSIX style time zone for US Central Time",
  alias: ["CST6CDT"]
}, {
  name: "cuhav",
  description: "Havana, Cuba",
  alias: ["America/Havana", "Cuba"]
}, {
  name: "cvrai",
  description: "Cape Verde",
  alias: ["Atlantic/Cape_Verde"]
}, {
  name: "cxxch",
  description: "Christmas Island",
  alias: ["Indian/Christmas"]
}, {
  name: "cyfmg",
  description: "Famagusta, Cyprus",
  alias: ["Asia/Famagusta"]
}, {
  name: "cynic",
  description: "Nicosia, Cyprus",
  alias: ["Asia/Nicosia", "Europe/Nicosia"]
}, {
  name: "czprg",
  description: "Prague, Czech Republic",
  alias: ["Europe/Prague"]
}, {
  name: "deber",
  description: "Berlin, Germany",
  alias: ["Europe/Berlin"]
}, {
  name: "debsngn",
  description: "Busingen, Germany",
  alias: ["Europe/Busingen"]
}, {
  name: "djjib",
  description: "Djibouti",
  alias: ["Africa/Djibouti"]
}, {
  name: "dkcph",
  description: "Copenhagen, Denmark",
  alias: ["Europe/Copenhagen"]
}, {
  name: "dmdom",
  description: "Dominica",
  alias: ["America/Dominica"]
}, {
  name: "dosdq",
  description: "Santo Domingo, Dominican Republic",
  alias: ["America/Santo_Domingo"]
}, {
  name: "dzalg",
  description: "Algiers, Algeria",
  alias: ["Africa/Algiers"]
}, {
  name: "ecgps",
  description: "Galápagos Islands, Ecuador",
  alias: ["Pacific/Galapagos"]
}, {
  name: "ecgye",
  description: "Guayaquil, Ecuador",
  alias: ["America/Guayaquil"]
}, {
  name: "eetll",
  description: "Tallinn, Estonia",
  alias: ["Europe/Tallinn"]
}, {
  name: "egcai",
  description: "Cairo, Egypt",
  alias: ["Africa/Cairo", "Egypt"]
}, {
  name: "eheai",
  description: "El Aaiún, Western Sahara",
  alias: ["Africa/El_Aaiun"]
}, {
  name: "erasm",
  description: "Asmara, Eritrea",
  alias: ["Africa/Asmera", "Africa/Asmara"]
}, {
  name: "esceu",
  description: "Ceuta, Spain",
  alias: ["Africa/Ceuta"]
}, {
  name: "eslpa",
  description: "Canary Islands, Spain",
  alias: ["Atlantic/Canary"]
}, {
  name: "esmad",
  description: "Madrid, Spain",
  alias: ["Europe/Madrid"]
}, {
  name: "est5edt",
  description: "POSIX style time zone for US Eastern Time",
  alias: ["EST5EDT"]
}, {
  name: "etadd",
  description: "Addis Ababa, Ethiopia",
  alias: ["Africa/Addis_Ababa"]
}, {
  name: "fihel",
  description: "Helsinki, Finland",
  alias: ["Europe/Helsinki"]
}, {
  name: "fimhq",
  description: "Mariehamn, Åland, Finland",
  alias: ["Europe/Mariehamn"]
}, {
  name: "fjsuv",
  description: "Fiji",
  alias: ["Pacific/Fiji"]
}, {
  name: "fkpsy",
  description: "Stanley, Falkland Islands",
  alias: ["Atlantic/Stanley"]
}, {
  name: "fmksa",
  description: "Kosrae, Micronesia",
  alias: ["Pacific/Kosrae"]
}, {
  name: "fmpni",
  description: "Pohnpei, Micronesia",
  alias: ["Pacific/Ponape", "Pacific/Pohnpei"]
}, {
  name: "fmtkk",
  description: "Chuuk, Micronesia",
  alias: ["Pacific/Truk", "Pacific/Chuuk", "Pacific/Yap"]
}, {
  name: "fotho",
  description: "Faroe Islands",
  alias: ["Atlantic/Faeroe", "Atlantic/Faroe"]
}, {
  name: "frpar",
  description: "Paris, France",
  alias: ["Europe/Paris"]
}, {
  name: "galbv",
  description: "Libreville, Gabon",
  alias: ["Africa/Libreville"]
}, {
  name: "gazastrp",
  description: "Gaza Strip, Palestinian Territories",
  alias: ["Asia/Gaza"]
}, {
  name: "gblon",
  description: "London, United Kingdom",
  alias: ["Europe/London", "Europe/Belfast", "GB", "GB-Eire"]
}, {
  name: "gdgnd",
  description: "Grenada",
  alias: ["America/Grenada"]
}, {
  name: "getbs",
  description: "Tbilisi, Georgia",
  alias: ["Asia/Tbilisi"]
}, {
  name: "gfcay",
  description: "Cayenne, French Guiana",
  alias: ["America/Cayenne"]
}, {
  name: "gggci",
  description: "Guernsey",
  alias: ["Europe/Guernsey"]
}, {
  name: "ghacc",
  description: "Accra, Ghana",
  alias: ["Africa/Accra"]
}, {
  name: "gigib",
  description: "Gibraltar",
  alias: ["Europe/Gibraltar"]
}, {
  name: "gldkshvn",
  description: "Danmarkshavn, Greenland",
  alias: ["America/Danmarkshavn"]
}, {
  name: "glgoh",
  description: "Nuuk (Godthåb), Greenland",
  alias: ["America/Godthab", "America/Nuuk"]
}, {
  name: "globy",
  description: "Ittoqqortoormiit (Scoresbysund), Greenland",
  alias: ["America/Scoresbysund"]
}, {
  name: "glthu",
  description: "Qaanaaq (Thule), Greenland",
  alias: ["America/Thule"]
}, {
  name: "gmbjl",
  description: "Banjul, Gambia",
  alias: ["Africa/Banjul"]
}, {
  name: "gmt",
  description: "Greenwich Mean Time",
  alias: ["Etc/GMT", "Etc/GMT+0", "Etc/GMT-0", "Etc/GMT0", "Etc/Greenwich", "GMT", "GMT+0", "GMT-0", "GMT0", "Greenwich"]
}, {
  name: "gncky",
  description: "Conakry, Guinea",
  alias: ["Africa/Conakry"]
}, {
  name: "gpbbr",
  description: "Guadeloupe",
  alias: ["America/Guadeloupe"]
}, {
  name: "gpmsb",
  description: "Marigot, Saint Martin",
  alias: ["America/Marigot"]
}, {
  name: "gpsbh",
  description: "Saint Barthélemy",
  alias: ["America/St_Barthelemy"]
}, {
  name: "gqssg",
  description: "Malabo, Equatorial Guinea",
  alias: ["Africa/Malabo"]
}, {
  name: "grath",
  description: "Athens, Greece",
  alias: ["Europe/Athens"]
}, {
  name: "gsgrv",
  description: "South Georgia and the South Sandwich Islands",
  alias: ["Atlantic/South_Georgia"]
}, {
  name: "gtgua",
  description: "Guatemala",
  alias: ["America/Guatemala"]
}, {
  name: "gugum",
  description: "Guam",
  alias: ["Pacific/Guam"]
}, {
  name: "gwoxb",
  description: "Bissau, Guinea-Bissau",
  alias: ["Africa/Bissau"]
}, {
  name: "gygeo",
  description: "Guyana",
  alias: ["America/Guyana"]
}, {
  name: "hebron",
  description: "West Bank, Palestinian Territories",
  alias: ["Asia/Hebron"]
}, {
  name: "hkhkg",
  description: "Hong Kong SAR China",
  alias: ["Asia/Hong_Kong", "Hongkong"]
}, {
  name: "hntgu",
  description: "Tegucigalpa, Honduras",
  alias: ["America/Tegucigalpa"]
}, {
  name: "hrzag",
  description: "Zagreb, Croatia",
  alias: ["Europe/Zagreb"]
}, {
  name: "htpap",
  description: "Port-au-Prince, Haiti",
  alias: ["America/Port-au-Prince"]
}, {
  name: "hubud",
  description: "Budapest, Hungary",
  alias: ["Europe/Budapest"]
}, {
  name: "iddjj",
  description: "Jayapura, Indonesia",
  alias: ["Asia/Jayapura"]
}, {
  name: "idjkt",
  description: "Jakarta, Indonesia",
  alias: ["Asia/Jakarta"]
}, {
  name: "idmak",
  description: "Makassar, Indonesia",
  alias: ["Asia/Makassar", "Asia/Ujung_Pandang"]
}, {
  name: "idpnk",
  description: "Pontianak, Indonesia",
  alias: ["Asia/Pontianak"]
}, {
  name: "iedub",
  description: "Dublin, Ireland",
  alias: ["Europe/Dublin", "Eire"]
}, {
  name: "imdgs",
  description: "Isle of Man",
  alias: ["Europe/Isle_of_Man"]
}, {
  name: "inccu",
  description: "Kolkata, India",
  alias: ["Asia/Calcutta", "Asia/Kolkata"]
}, {
  name: "iodga",
  description: "Chagos Archipelago",
  alias: ["Indian/Chagos"]
}, {
  name: "iqbgw",
  description: "Baghdad, Iraq",
  alias: ["Asia/Baghdad"]
}, {
  name: "irthr",
  description: "Tehran, Iran",
  alias: ["Asia/Tehran", "Iran"]
}, {
  name: "isrey",
  description: "Reykjavik, Iceland",
  alias: ["Atlantic/Reykjavik", "Iceland"]
}, {
  name: "itrom",
  description: "Rome, Italy",
  alias: ["Europe/Rome"]
}, {
  name: "jeruslm",
  description: "Jerusalem",
  alias: ["Asia/Jerusalem", "Asia/Tel_Aviv", "Israel"]
}, {
  name: "jesth",
  description: "Jersey",
  alias: ["Europe/Jersey"]
}, {
  name: "jmkin",
  description: "Jamaica",
  alias: ["America/Jamaica", "Jamaica"]
}, {
  name: "joamm",
  description: "Amman, Jordan",
  alias: ["Asia/Amman"]
}, {
  name: "jptyo",
  description: "Tokyo, Japan",
  alias: ["Asia/Tokyo", "Japan"]
}, {
  name: "kenbo",
  description: "Nairobi, Kenya",
  alias: ["Africa/Nairobi"]
}, {
  name: "kgfru",
  description: "Bishkek, Kyrgyzstan",
  alias: ["Asia/Bishkek"]
}, {
  name: "khpnh",
  description: "Phnom Penh, Cambodia",
  alias: ["Asia/Phnom_Penh"]
}, {
  name: "kicxi",
  description: "Kiritimati, Kiribati",
  alias: ["Pacific/Kiritimati"]
}, {
  name: "kipho",
  description: "Enderbury Island, Kiribati",
  alias: ["Pacific/Enderbury"]
}, {
  name: "kitrw",
  description: "Tarawa, Kiribati",
  alias: ["Pacific/Tarawa"]
}, {
  name: "kmyva",
  description: "Comoros",
  alias: ["Indian/Comoro"]
}, {
  name: "knbas",
  description: "Saint Kitts",
  alias: ["America/St_Kitts"]
}, {
  name: "kpfnj",
  description: "Pyongyang, North Korea",
  alias: ["Asia/Pyongyang"]
}, {
  name: "krsel",
  description: "Seoul, South Korea",
  alias: ["Asia/Seoul", "ROK"]
}, {
  name: "kwkwi",
  description: "Kuwait",
  alias: ["Asia/Kuwait"]
}, {
  name: "kygec",
  description: "Cayman Islands",
  alias: ["America/Cayman"]
}, {
  name: "kzaau",
  description: "Aqtau, Kazakhstan",
  alias: ["Asia/Aqtau"]
}, {
  name: "kzakx",
  description: "Aqtobe, Kazakhstan",
  alias: ["Asia/Aqtobe"]
}, {
  name: "kzala",
  description: "Almaty, Kazakhstan",
  alias: ["Asia/Almaty"]
}, {
  name: "kzguw",
  description: "Atyrau (Guryev), Kazakhstan",
  alias: ["Asia/Atyrau"]
}, {
  name: "kzksn",
  description: "Qostanay (Kostanay), Kazakhstan",
  alias: ["Asia/Qostanay"]
}, {
  name: "kzkzo",
  description: "Kyzylorda, Kazakhstan",
  alias: ["Asia/Qyzylorda"]
}, {
  name: "kzura",
  description: "Oral, Kazakhstan",
  alias: ["Asia/Oral"]
}, {
  name: "lavte",
  description: "Vientiane, Laos",
  alias: ["Asia/Vientiane"]
}, {
  name: "lbbey",
  description: "Beirut, Lebanon",
  alias: ["Asia/Beirut"]
}, {
  name: "lccas",
  description: "Saint Lucia",
  alias: ["America/St_Lucia"]
}, {
  name: "livdz",
  description: "Vaduz, Liechtenstein",
  alias: ["Europe/Vaduz"]
}, {
  name: "lkcmb",
  description: "Colombo, Sri Lanka",
  alias: ["Asia/Colombo"]
}, {
  name: "lrmlw",
  description: "Monrovia, Liberia",
  alias: ["Africa/Monrovia"]
}, {
  name: "lsmsu",
  description: "Maseru, Lesotho",
  alias: ["Africa/Maseru"]
}, {
  name: "ltvno",
  description: "Vilnius, Lithuania",
  alias: ["Europe/Vilnius"]
}, {
  name: "lulux",
  description: "Luxembourg",
  alias: ["Europe/Luxembourg"]
}, {
  name: "lvrix",
  description: "Riga, Latvia",
  alias: ["Europe/Riga"]
}, {
  name: "lytip",
  description: "Tripoli, Libya",
  alias: ["Africa/Tripoli", "Libya"]
}, {
  name: "macas",
  description: "Casablanca, Morocco",
  alias: ["Africa/Casablanca"]
}, {
  name: "mcmon",
  description: "Monaco",
  alias: ["Europe/Monaco"]
}, {
  name: "mdkiv",
  description: "Chişinău, Moldova",
  alias: ["Europe/Chisinau", "Europe/Tiraspol"]
}, {
  name: "metgd",
  description: "Podgorica, Montenegro",
  alias: ["Europe/Podgorica"]
}, {
  name: "mgtnr",
  description: "Antananarivo, Madagascar",
  alias: ["Indian/Antananarivo"]
}, {
  name: "mhkwa",
  description: "Kwajalein, Marshall Islands",
  alias: ["Pacific/Kwajalein", "Kwajalein"]
}, {
  name: "mhmaj",
  description: "Majuro, Marshall Islands",
  alias: ["Pacific/Majuro"]
}, {
  name: "mkskp",
  description: "Skopje, Macedonia",
  alias: ["Europe/Skopje"]
}, {
  name: "mlbko",
  description: "Bamako, Mali",
  alias: ["Africa/Bamako", "Africa/Timbuktu"]
}, {
  name: "mmrgn",
  description: "Yangon (Rangoon), Burma",
  alias: ["Asia/Rangoon", "Asia/Yangon"]
}, {
  name: "mncoq",
  description: "Choibalsan, Mongolia",
  alias: ["Asia/Choibalsan"]
}, {
  name: "mnhvd",
  description: "Khovd (Hovd), Mongolia",
  alias: ["Asia/Hovd"]
}, {
  name: "mnuln",
  description: "Ulaanbaatar (Ulan Bator), Mongolia",
  alias: ["Asia/Ulaanbaatar", "Asia/Ulan_Bator"]
}, {
  name: "momfm",
  description: "Macau SAR China",
  alias: ["Asia/Macau", "Asia/Macao"]
}, {
  name: "mpspn",
  description: "Saipan, Northern Mariana Islands",
  alias: ["Pacific/Saipan"]
}, {
  name: "mqfdf",
  description: "Martinique",
  alias: ["America/Martinique"]
}, {
  name: "mrnkc",
  description: "Nouakchott, Mauritania",
  alias: ["Africa/Nouakchott"]
}, {
  name: "msmni",
  description: "Montserrat",
  alias: ["America/Montserrat"]
}, {
  name: "mst7mdt",
  description: "POSIX style time zone for US Mountain Time",
  alias: ["MST7MDT"]
}, {
  name: "mtmla",
  description: "Malta",
  alias: ["Europe/Malta"]
}, {
  name: "muplu",
  description: "Mauritius",
  alias: ["Indian/Mauritius"]
}, {
  name: "mvmle",
  description: "Maldives",
  alias: ["Indian/Maldives"]
}, {
  name: "mwblz",
  description: "Blantyre, Malawi",
  alias: ["Africa/Blantyre"]
}, {
  name: "mxchi",
  description: "Chihuahua, Mexico",
  alias: ["America/Chihuahua"]
}, {
  name: "mxcun",
  description: "Cancún, Mexico",
  alias: ["America/Cancun"]
}, {
  name: "mxhmo",
  description: "Hermosillo, Mexico",
  alias: ["America/Hermosillo"]
}, {
  name: "mxmam",
  description: "Matamoros, Mexico",
  alias: ["America/Matamoros"]
}, {
  name: "mxmex",
  description: "Mexico City, Mexico",
  alias: ["America/Mexico_City", "Mexico/General"]
}, {
  name: "mxmid",
  description: "Mérida, Mexico",
  alias: ["America/Merida"]
}, {
  name: "mxmty",
  description: "Monterrey, Mexico",
  alias: ["America/Monterrey"]
}, {
  name: "mxmzt",
  description: "Mazatlán, Mexico",
  alias: ["America/Mazatlan", "Mexico/BajaSur"]
}, {
  name: "mxoji",
  description: "Ojinaga, Mexico",
  alias: ["America/Ojinaga"]
}, {
  name: "mxpvr",
  description: "Bahía de Banderas, Mexico",
  alias: ["America/Bahia_Banderas"]
}, {
  name: "mxstis",
  description: "Santa Isabel (Baja California), Mexico",
  alias: ["America/Santa_Isabel"]
}, {
  name: "mxtij",
  description: "Tijuana, Mexico",
  alias: ["America/Tijuana", "America/Ensenada", "Mexico/BajaNorte"]
}, {
  name: "mykch",
  description: "Kuching, Malaysia",
  alias: ["Asia/Kuching"]
}, {
  name: "mykul",
  description: "Kuala Lumpur, Malaysia",
  alias: ["Asia/Kuala_Lumpur"]
}, {
  name: "mzmpm",
  description: "Maputo, Mozambique",
  alias: ["Africa/Maputo"]
}, {
  name: "nawdh",
  description: "Windhoek, Namibia",
  alias: ["Africa/Windhoek"]
}, {
  name: "ncnou",
  description: "Noumea, New Caledonia",
  alias: ["Pacific/Noumea"]
}, {
  name: "nenim",
  description: "Niamey, Niger",
  alias: ["Africa/Niamey"]
}, {
  name: "nfnlk",
  description: "Norfolk Island",
  alias: ["Pacific/Norfolk"]
}, {
  name: "nglos",
  description: "Lagos, Nigeria",
  alias: ["Africa/Lagos"]
}, {
  name: "nimga",
  description: "Managua, Nicaragua",
  alias: ["America/Managua"]
}, {
  name: "nlams",
  description: "Amsterdam, Netherlands",
  alias: ["Europe/Amsterdam"]
}, {
  name: "noosl",
  description: "Oslo, Norway",
  alias: ["Europe/Oslo"]
}, {
  name: "npktm",
  description: "Kathmandu, Nepal",
  alias: ["Asia/Katmandu", "Asia/Kathmandu"]
}, {
  name: "nrinu",
  description: "Nauru",
  alias: ["Pacific/Nauru"]
}, {
  name: "nuiue",
  description: "Niue",
  alias: ["Pacific/Niue"]
}, {
  name: "nzakl",
  description: "Auckland, New Zealand",
  alias: ["Pacific/Auckland", "Antarctica/South_Pole", "NZ"]
}, {
  name: "nzcht",
  description: "Chatham Islands, New Zealand",
  alias: ["Pacific/Chatham", "NZ-CHAT"]
}, {
  name: "ommct",
  description: "Muscat, Oman",
  alias: ["Asia/Muscat"]
}, {
  name: "papty",
  description: "Panama",
  alias: ["America/Panama"]
}, {
  name: "pelim",
  description: "Lima, Peru",
  alias: ["America/Lima"]
}, {
  name: "pfgmr",
  description: "Gambiera Islands, French Polynesia",
  alias: ["Pacific/Gambier"]
}, {
  name: "pfnhv",
  description: "Marquesas Islands, French Polynesia",
  alias: ["Pacific/Marquesas"]
}, {
  name: "pfppt",
  description: "Tahiti, French Polynesia",
  alias: ["Pacific/Tahiti"]
}, {
  name: "pgpom",
  description: "Port Moresby, Papua New Guinea",
  alias: ["Pacific/Port_Moresby"]
}, {
  name: "pgraw",
  description: "Bougainville, Papua New Guinea",
  alias: ["Pacific/Bougainville"]
}, {
  name: "phmnl",
  description: "Manila, Philippines",
  alias: ["Asia/Manila"]
}, {
  name: "pkkhi",
  description: "Karachi, Pakistan",
  alias: ["Asia/Karachi"]
}, {
  name: "plwaw",
  description: "Warsaw, Poland",
  alias: ["Europe/Warsaw", "Poland"]
}, {
  name: "pmmqc",
  description: "Saint Pierre and Miquelon",
  alias: ["America/Miquelon"]
}, {
  name: "pnpcn",
  description: "Pitcairn Islands",
  alias: ["Pacific/Pitcairn"]
}, {
  name: "prsju",
  description: "Puerto Rico",
  alias: ["America/Puerto_Rico"]
}, {
  name: "pst8pdt",
  description: "POSIX style time zone for US Pacific Time",
  alias: ["PST8PDT"]
}, {
  name: "ptfnc",
  description: "Madeira, Portugal",
  alias: ["Atlantic/Madeira"]
}, {
  name: "ptlis",
  description: "Lisbon, Portugal",
  alias: ["Europe/Lisbon", "Portugal"]
}, {
  name: "ptpdl",
  description: "Azores, Portugal",
  alias: ["Atlantic/Azores"]
}, {
  name: "pwror",
  description: "Palau",
  alias: ["Pacific/Palau"]
}, {
  name: "pyasu",
  description: "Asunción, Paraguay",
  alias: ["America/Asuncion"]
}, {
  name: "qadoh",
  description: "Qatar",
  alias: ["Asia/Qatar"]
}, {
  name: "rereu",
  description: "Réunion",
  alias: ["Indian/Reunion"]
}, {
  name: "robuh",
  description: "Bucharest, Romania",
  alias: ["Europe/Bucharest"]
}, {
  name: "rsbeg",
  description: "Belgrade, Serbia",
  alias: ["Europe/Belgrade"]
}, {
  name: "ruasf",
  description: "Astrakhan, Russia",
  alias: ["Europe/Astrakhan"]
}, {
  name: "rubax",
  description: "Barnaul, Russia",
  alias: ["Asia/Barnaul"]
}, {
  name: "ruchita",
  description: "Chita Zabaykalsky, Russia",
  alias: ["Asia/Chita"]
}, {
  name: "rudyr",
  description: "Anadyr, Russia",
  alias: ["Asia/Anadyr"]
}, {
  name: "rugdx",
  description: "Magadan, Russia",
  alias: ["Asia/Magadan"]
}, {
  name: "ruikt",
  description: "Irkutsk, Russia",
  alias: ["Asia/Irkutsk"]
}, {
  name: "rukgd",
  description: "Kaliningrad, Russia",
  alias: ["Europe/Kaliningrad"]
}, {
  name: "rukhndg",
  description: "Khandyga Tomponsky, Russia",
  alias: ["Asia/Khandyga"]
}, {
  name: "rukra",
  description: "Krasnoyarsk, Russia",
  alias: ["Asia/Krasnoyarsk"]
}, {
  name: "rukuf",
  description: "Samara, Russia",
  alias: ["Europe/Samara"]
}, {
  name: "rukvx",
  description: "Kirov, Russia",
  alias: ["Europe/Kirov"]
}, {
  name: "rumow",
  description: "Moscow, Russia",
  alias: ["Europe/Moscow", "W-SU"]
}, {
  name: "runoz",
  description: "Novokuznetsk, Russia",
  alias: ["Asia/Novokuznetsk"]
}, {
  name: "ruoms",
  description: "Omsk, Russia",
  alias: ["Asia/Omsk"]
}, {
  name: "ruovb",
  description: "Novosibirsk, Russia",
  alias: ["Asia/Novosibirsk"]
}, {
  name: "rupkc",
  description: "Kamchatka Peninsula, Russia",
  alias: ["Asia/Kamchatka"]
}, {
  name: "rurtw",
  description: "Saratov, Russia",
  alias: ["Europe/Saratov"]
}, {
  name: "rusred",
  description: "Srednekolymsk, Russia",
  alias: ["Asia/Srednekolymsk"]
}, {
  name: "rutof",
  description: "Tomsk, Russia",
  alias: ["Asia/Tomsk"]
}, {
  name: "ruuly",
  description: "Ulyanovsk, Russia",
  alias: ["Europe/Ulyanovsk"]
}, {
  name: "ruunera",
  description: "Ust-Nera Oymyakonsky, Russia",
  alias: ["Asia/Ust-Nera"]
}, {
  name: "ruuus",
  description: "Sakhalin, Russia",
  alias: ["Asia/Sakhalin"]
}, {
  name: "ruvog",
  description: "Volgograd, Russia",
  alias: ["Europe/Volgograd"]
}, {
  name: "ruvvo",
  description: "Vladivostok, Russia",
  alias: ["Asia/Vladivostok"]
}, {
  name: "ruyek",
  description: "Yekaterinburg, Russia",
  alias: ["Asia/Yekaterinburg"]
}, {
  name: "ruyks",
  description: "Yakutsk, Russia",
  alias: ["Asia/Yakutsk"]
}, {
  name: "rwkgl",
  description: "Kigali, Rwanda",
  alias: ["Africa/Kigali"]
}, {
  name: "saruh",
  description: "Riyadh, Saudi Arabia",
  alias: ["Asia/Riyadh"]
}, {
  name: "sbhir",
  description: "Guadalcanal, Solomon Islands",
  alias: ["Pacific/Guadalcanal"]
}, {
  name: "scmaw",
  description: "Mahé, Seychelles",
  alias: ["Indian/Mahe"]
}, {
  name: "sdkrt",
  description: "Khartoum, Sudan",
  alias: ["Africa/Khartoum"]
}, {
  name: "sesto",
  description: "Stockholm, Sweden",
  alias: ["Europe/Stockholm"]
}, {
  name: "sgsin",
  description: "Singapore",
  alias: ["Asia/Singapore", "Singapore"]
}, {
  name: "shshn",
  description: "Saint Helena",
  alias: ["Atlantic/St_Helena"]
}, {
  name: "silju",
  description: "Ljubljana, Slovenia",
  alias: ["Europe/Ljubljana"]
}, {
  name: "sjlyr",
  description: "Longyearbyen, Svalbard",
  alias: ["Arctic/Longyearbyen", "Atlantic/Jan_Mayen"]
}, {
  name: "skbts",
  description: "Bratislava, Slovakia",
  alias: ["Europe/Bratislava"]
}, {
  name: "slfna",
  description: "Freetown, Sierra Leone",
  alias: ["Africa/Freetown"]
}, {
  name: "smsai",
  description: "San Marino",
  alias: ["Europe/San_Marino"]
}, {
  name: "sndkr",
  description: "Dakar, Senegal",
  alias: ["Africa/Dakar"]
}, {
  name: "somgq",
  description: "Mogadishu, Somalia",
  alias: ["Africa/Mogadishu"]
}, {
  name: "srpbm",
  description: "Paramaribo, Suriname",
  alias: ["America/Paramaribo"]
}, {
  name: "ssjub",
  description: "Juba, South Sudan",
  alias: ["Africa/Juba"]
}, {
  name: "sttms",
  description: "São Tomé, São Tomé and Príncipe",
  alias: ["Africa/Sao_Tome"]
}, {
  name: "svsal",
  description: "El Salvador",
  alias: ["America/El_Salvador"]
}, {
  name: "sxphi",
  description: "Sint Maarten",
  alias: ["America/Lower_Princes"]
}, {
  name: "sydam",
  description: "Damascus, Syria",
  alias: ["Asia/Damascus"]
}, {
  name: "szqmn",
  description: "Mbabane, Swaziland",
  alias: ["Africa/Mbabane"]
}, {
  name: "tcgdt",
  description: "Grand Turk, Turks and Caicos Islands",
  alias: ["America/Grand_Turk"]
}, {
  name: "tdndj",
  description: "N'Djamena, Chad",
  alias: ["Africa/Ndjamena"]
}, {
  name: "tfpfr",
  description: "Kerguelen Islands, French Southern Territories",
  alias: ["Indian/Kerguelen"]
}, {
  name: "tglfw",
  description: "Lomé, Togo",
  alias: ["Africa/Lome"]
}, {
  name: "thbkk",
  description: "Bangkok, Thailand",
  alias: ["Asia/Bangkok"]
}, {
  name: "tjdyu",
  description: "Dushanbe, Tajikistan",
  alias: ["Asia/Dushanbe"]
}, {
  name: "tkfko",
  description: "Fakaofo, Tokelau",
  alias: ["Pacific/Fakaofo"]
}, {
  name: "tldil",
  description: "Dili, East Timor",
  alias: ["Asia/Dili"]
}, {
  name: "tmasb",
  description: "Ashgabat, Turkmenistan",
  alias: ["Asia/Ashgabat", "Asia/Ashkhabad"]
}, {
  name: "tntun",
  description: "Tunis, Tunisia",
  alias: ["Africa/Tunis"]
}, {
  name: "totbu",
  description: "Tongatapu, Tonga",
  alias: ["Pacific/Tongatapu"]
}, {
  name: "trist",
  description: "Istanbul, Turkey",
  alias: ["Europe/Istanbul", "Asia/Istanbul", "Turkey"]
}, {
  name: "ttpos",
  description: "Port of Spain, Trinidad and Tobago",
  alias: ["America/Port_of_Spain"]
}, {
  name: "tvfun",
  description: "Funafuti, Tuvalu",
  alias: ["Pacific/Funafuti"]
}, {
  name: "twtpe",
  description: "Taipei, Taiwan",
  alias: ["Asia/Taipei", "ROC"]
}, {
  name: "tzdar",
  description: "Dar es Salaam, Tanzania",
  alias: ["Africa/Dar_es_Salaam"]
}, {
  name: "uaiev",
  description: "Kiev, Ukraine",
  alias: ["Europe/Kiev"]
}, {
  name: "uaozh",
  description: "Zaporizhia (Zaporozhye), Ukraine",
  alias: ["Europe/Zaporozhye"]
}, {
  name: "uasip",
  description: "Simferopol, Ukraine",
  alias: ["Europe/Simferopol"]
}, {
  name: "uauzh",
  description: "Uzhhorod (Uzhgorod), Ukraine",
  alias: ["Europe/Uzhgorod"]
}, {
  name: "ugkla",
  description: "Kampala, Uganda",
  alias: ["Africa/Kampala"]
}, {
  name: "umawk",
  description: "Wake Island, U.S. Minor Outlying Islands",
  alias: ["Pacific/Wake"]
}, {
  name: "umjon",
  description: "Johnston Atoll, U.S. Minor Outlying Islands",
  alias: ["Pacific/Johnston"]
}, {
  name: "ummdy",
  description: "Midway Islands, U.S. Minor Outlying Islands",
  alias: ["Pacific/Midway"]
}, {
  name: "unk",
  description: "Unknown time zone",
  alias: ["Etc/Unknown"]
}, {
  name: "usadk",
  description: "Adak (Alaska), United States",
  alias: ["America/Adak", "America/Atka", "US/Aleutian"]
}, {
  name: "usaeg",
  description: "Marengo (Indiana), United States",
  alias: ["America/Indiana/Marengo"]
}, {
  name: "usanc",
  description: "Anchorage, United States",
  alias: ["America/Anchorage", "US/Alaska"]
}, {
  name: "usboi",
  description: "Boise (Idaho), United States",
  alias: ["America/Boise"]
}, {
  name: "uschi",
  description: "Chicago, United States",
  alias: ["America/Chicago", "US/Central"]
}, {
  name: "usden",
  description: "Denver, United States",
  alias: ["America/Denver", "America/Shiprock", "Navajo", "US/Mountain"]
}, {
  name: "usdet",
  description: "Detroit, United States",
  alias: ["America/Detroit", "US/Michigan"]
}, {
  name: "ushnl",
  description: "Honolulu, United States",
  alias: ["Pacific/Honolulu", "US/Hawaii"]
}, {
  name: "usind",
  description: "Indianapolis, United States",
  alias: ["America/Indianapolis", "America/Fort_Wayne", "America/Indiana/Indianapolis", "US/East-Indiana"]
}, {
  name: "usinvev",
  description: "Vevay (Indiana), United States",
  alias: ["America/Indiana/Vevay"]
}, {
  name: "usjnu",
  description: "Juneau (Alaska), United States",
  alias: ["America/Juneau"]
}, {
  name: "usknx",
  description: "Knox (Indiana), United States",
  alias: ["America/Indiana/Knox", "America/Knox_IN", "US/Indiana-Starke"]
}, {
  name: "uslax",
  description: "Los Angeles, United States",
  alias: ["America/Los_Angeles", "US/Pacific", "US/Pacific-New"]
}, {
  name: "uslui",
  description: "Louisville (Kentucky), United States",
  alias: ["America/Louisville", "America/Kentucky/Louisville"]
}, {
  name: "usmnm",
  description: "Menominee (Michigan), United States",
  alias: ["America/Menominee"]
}, {
  name: "usmtm",
  description: "Metlakatla (Alaska), United States",
  alias: ["America/Metlakatla"]
}, {
  name: "usmoc",
  description: "Monticello (Kentucky), United States",
  alias: ["America/Kentucky/Monticello"]
}, {
  name: "usndcnt",
  description: "Center (North Dakota), United States",
  alias: ["America/North_Dakota/Center"]
}, {
  name: "usndnsl",
  description: "New Salem (North Dakota), United States",
  alias: ["America/North_Dakota/New_Salem"]
}, {
  name: "usnyc",
  description: "New York, United States",
  alias: ["America/New_York", "US/Eastern"]
}, {
  name: "usoea",
  description: "Vincennes (Indiana), United States",
  alias: ["America/Indiana/Vincennes"]
}, {
  name: "usome",
  description: "Nome (Alaska), United States",
  alias: ["America/Nome"]
}, {
  name: "usphx",
  description: "Phoenix, United States",
  alias: ["America/Phoenix", "US/Arizona"]
}, {
  name: "ussit",
  description: "Sitka (Alaska), United States",
  alias: ["America/Sitka"]
}, {
  name: "ustel",
  description: "Tell City (Indiana), United States",
  alias: ["America/Indiana/Tell_City"]
}, {
  name: "uswlz",
  description: "Winamac (Indiana), United States",
  alias: ["America/Indiana/Winamac"]
}, {
  name: "uswsq",
  description: "Petersburg (Indiana), United States",
  alias: ["America/Indiana/Petersburg"]
}, {
  name: "usxul",
  description: "Beulah (North Dakota), United States",
  alias: ["America/North_Dakota/Beulah"]
}, {
  name: "usyak",
  description: "Yakutat (Alaska), United States",
  alias: ["America/Yakutat"]
}, {
  name: "utc",
  description: "UTC (Coordinated Universal Time)",
  alias: ["Etc/UTC", "Etc/UCT", "Etc/Universal", "Etc/Zulu", "UCT", "UTC", "Universal", "Zulu"]
}, {
  name: "utce01",
  description: "1 hour ahead of UTC",
  alias: ["Etc/GMT-1"]
}, {
  name: "utce02",
  description: "2 hours ahead of UTC",
  alias: ["Etc/GMT-2"]
}, {
  name: "utce03",
  description: "3 hours ahead of UTC",
  alias: ["Etc/GMT-3"]
}, {
  name: "utce04",
  description: "4 hours ahead of UTC",
  alias: ["Etc/GMT-4"]
}, {
  name: "utce05",
  description: "5 hours ahead of UTC",
  alias: ["Etc/GMT-5"]
}, {
  name: "utce06",
  description: "6 hours ahead of UTC",
  alias: ["Etc/GMT-6"]
}, {
  name: "utce07",
  description: "7 hours ahead of UTC",
  alias: ["Etc/GMT-7"]
}, {
  name: "utce08",
  description: "8 hours ahead of UTC",
  alias: ["Etc/GMT-8"]
}, {
  name: "utce09",
  description: "9 hours ahead of UTC",
  alias: ["Etc/GMT-9"]
}, {
  name: "utce10",
  description: "10 hours ahead of UTC",
  alias: ["Etc/GMT-10"]
}, {
  name: "utce11",
  description: "11 hours ahead of UTC",
  alias: ["Etc/GMT-11"]
}, {
  name: "utce12",
  description: "12 hours ahead of UTC",
  alias: ["Etc/GMT-12"]
}, {
  name: "utce13",
  description: "13 hours ahead of UTC",
  alias: ["Etc/GMT-13"]
}, {
  name: "utce14",
  description: "14 hours ahead of UTC",
  alias: ["Etc/GMT-14"]
}, {
  name: "utcw01",
  description: "1 hour behind UTC",
  alias: ["Etc/GMT+1"]
}, {
  name: "utcw02",
  description: "2 hours behind UTC",
  alias: ["Etc/GMT+2"]
}, {
  name: "utcw03",
  description: "3 hours behind UTC",
  alias: ["Etc/GMT+3"]
}, {
  name: "utcw04",
  description: "4 hours behind UTC",
  alias: ["Etc/GMT+4"]
}, {
  name: "utcw05",
  description: "5 hours behind UTC",
  alias: ["Etc/GMT+5", "EST"]
}, {
  name: "utcw06",
  description: "6 hours behind UTC",
  alias: ["Etc/GMT+6"]
}, {
  name: "utcw07",
  description: "7 hours behind UTC",
  alias: ["Etc/GMT+7", "MST"]
}, {
  name: "utcw08",
  description: "8 hours behind UTC",
  alias: ["Etc/GMT+8"]
}, {
  name: "utcw09",
  description: "9 hours behind UTC",
  alias: ["Etc/GMT+9"]
}, {
  name: "utcw10",
  description: "10 hours behind UTC",
  alias: ["Etc/GMT+10", "HST"]
}, {
  name: "utcw11",
  description: "11 hours behind UTC",
  alias: ["Etc/GMT+11"]
}, {
  name: "utcw12",
  description: "12 hours behind UTC",
  alias: ["Etc/GMT+12"]
}, {
  name: "uymvd",
  description: "Montevideo, Uruguay",
  alias: ["America/Montevideo"]
}, {
  name: "uzskd",
  description: "Samarkand, Uzbekistan",
  alias: ["Asia/Samarkand"]
}, {
  name: "uztas",
  description: "Tashkent, Uzbekistan",
  alias: ["Asia/Tashkent"]
}, {
  name: "vavat",
  description: "Vatican City",
  alias: ["Europe/Vatican"]
}, {
  name: "vcsvd",
  description: "Saint Vincent, Saint Vincent and the Grenadines",
  alias: ["America/St_Vincent"]
}, {
  name: "veccs",
  description: "Caracas, Venezuela",
  alias: ["America/Caracas"]
}, {
  name: "vgtov",
  description: "Tortola, British Virgin Islands",
  alias: ["America/Tortola"]
}, {
  name: "vistt",
  description: "Saint Thomas, U.S. Virgin Islands",
  alias: ["America/St_Thomas", "America/Virgin"]
}, {
  name: "vnsgn",
  description: "Ho Chi Minh City, Vietnam",
  alias: ["Asia/Saigon", "Asia/Ho_Chi_Minh"]
}, {
  name: "vuvli",
  description: "Efate, Vanuatu",
  alias: ["Pacific/Efate"]
}, {
  name: "wfmau",
  description: "Wallis Islands, Wallis and Futuna",
  alias: ["Pacific/Wallis"]
}, {
  name: "wsapw",
  description: "Apia, Samoa",
  alias: ["Pacific/Apia"]
}, {
  name: "yeade",
  description: "Aden, Yemen",
  alias: ["Asia/Aden"]
}, {
  name: "ytmam",
  description: "Mayotte",
  alias: ["Indian/Mayotte"]
}, {
  name: "zajnb",
  description: "Johannesburg, South Africa",
  alias: ["Africa/Johannesburg"]
}, {
  name: "zmlun",
  description: "Lusaka, Zambia",
  alias: ["Africa/Lusaka"]
}, {
  name: "zwhre",
  description: "Harare, Zimbabwe",
  alias: ["Africa/Harare"]
}];
var WINDOWS_TO_IANA_MAP = [{
  windowsName: "Dateline Standard Time",
  territory: "001",
  iana: ["Etc/GMT+12"]
}, {
  windowsName: "Dateline Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+12"]
}, {
  windowsName: "UTC-11",
  territory: "001",
  iana: ["Etc/GMT+11"]
}, {
  windowsName: "UTC-11",
  territory: "AS",
  iana: ["Pacific/Pago_Pago"]
}, {
  windowsName: "UTC-11",
  territory: "NU",
  iana: ["Pacific/Niue"]
}, {
  windowsName: "UTC-11",
  territory: "UM",
  iana: ["Pacific/Midway"]
}, {
  windowsName: "UTC-11",
  territory: "ZZ",
  iana: ["Etc/GMT+11"]
}, {
  windowsName: "Aleutian Standard Time",
  territory: "001",
  iana: ["America/Adak"]
}, {
  windowsName: "Aleutian Standard Time",
  territory: "US",
  iana: ["America/Adak"]
}, {
  windowsName: "Hawaiian Standard Time",
  territory: "001",
  iana: ["Pacific/Honolulu"]
}, {
  windowsName: "Hawaiian Standard Time",
  territory: "CK",
  iana: ["Pacific/Rarotonga"]
}, {
  windowsName: "Hawaiian Standard Time",
  territory: "PF",
  iana: ["Pacific/Tahiti"]
}, {
  windowsName: "Hawaiian Standard Time",
  territory: "UM",
  iana: ["Pacific/Johnston"]
}, {
  windowsName: "Hawaiian Standard Time",
  territory: "US",
  iana: ["Pacific/Honolulu"]
}, {
  windowsName: "Hawaiian Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+10"]
}, {
  windowsName: "Marquesas Standard Time",
  territory: "001",
  iana: ["Pacific/Marquesas"]
}, {
  windowsName: "Marquesas Standard Time",
  territory: "PF",
  iana: ["Pacific/Marquesas"]
}, {
  windowsName: "Alaskan Standard Time",
  territory: "001",
  iana: ["America/Anchorage"]
}, {
  windowsName: "Alaskan Standard Time",
  territory: "US",
  iana: ["America/Anchorage", "America/Juneau", "America/Metlakatla", "America/Nome", "America/Sitka", "America/Yakutat"]
}, {
  windowsName: "UTC-09",
  territory: "001",
  iana: ["Etc/GMT+9"]
}, {
  windowsName: "UTC-09",
  territory: "PF",
  iana: ["Pacific/Gambier"]
}, {
  windowsName: "UTC-09",
  territory: "ZZ",
  iana: ["Etc/GMT+9"]
}, {
  windowsName: "Pacific Standard Time (Mexico)",
  territory: "001",
  iana: ["America/Tijuana"]
}, {
  windowsName: "Pacific Standard Time (Mexico)",
  territory: "MX",
  iana: ["America/Tijuana", "America/Santa_Isabel"]
}, {
  windowsName: "UTC-08",
  territory: "001",
  iana: ["Etc/GMT+8"]
}, {
  windowsName: "UTC-08",
  territory: "PN",
  iana: ["Pacific/Pitcairn"]
}, {
  windowsName: "UTC-08",
  territory: "ZZ",
  iana: ["Etc/GMT+8"]
}, {
  windowsName: "Pacific Standard Time",
  territory: "001",
  iana: ["America/Los_Angeles"]
}, {
  windowsName: "Pacific Standard Time",
  territory: "CA",
  iana: ["America/Vancouver"]
}, {
  windowsName: "Pacific Standard Time",
  territory: "US",
  iana: ["America/Los_Angeles"]
}, {
  windowsName: "Pacific Standard Time",
  territory: "ZZ",
  iana: ["PST8PDT"]
}, {
  windowsName: "US Mountain Standard Time",
  territory: "001",
  iana: ["America/Phoenix"]
}, {
  windowsName: "US Mountain Standard Time",
  territory: "CA",
  iana: ["America/Creston", "America/Dawson_Creek", "America/Fort_Nelson"]
}, {
  windowsName: "US Mountain Standard Time",
  territory: "MX",
  iana: ["America/Hermosillo"]
}, {
  windowsName: "US Mountain Standard Time",
  territory: "US",
  iana: ["America/Phoenix"]
}, {
  windowsName: "US Mountain Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+7"]
}, {
  windowsName: "Mountain Standard Time (Mexico)",
  territory: "001",
  iana: ["America/Chihuahua"]
}, {
  windowsName: "Mountain Standard Time (Mexico)",
  territory: "MX",
  iana: ["America/Chihuahua", "America/Mazatlan"]
}, {
  windowsName: "Mountain Standard Time",
  territory: "001",
  iana: ["America/Denver"]
}, {
  windowsName: "Mountain Standard Time",
  territory: "CA",
  iana: ["America/Edmonton", "America/Cambridge_Bay", "America/Inuvik", "America/Yellowknife"]
}, {
  windowsName: "Mountain Standard Time",
  territory: "MX",
  iana: ["America/Ojinaga"]
}, {
  windowsName: "Mountain Standard Time",
  territory: "US",
  iana: ["America/Denver", "America/Boise"]
}, {
  windowsName: "Mountain Standard Time",
  territory: "ZZ",
  iana: ["MST7MDT"]
}, {
  windowsName: "Yukon Standard Time",
  territory: "001",
  iana: ["America/Whitehorse"]
}, {
  windowsName: "Yukon Standard Time",
  territory: "CA",
  iana: ["America/Whitehorse", "America/Dawson"]
}, {
  windowsName: "Central America Standard Time",
  territory: "001",
  iana: ["America/Guatemala"]
}, {
  windowsName: "Central America Standard Time",
  territory: "BZ",
  iana: ["America/Belize"]
}, {
  windowsName: "Central America Standard Time",
  territory: "CR",
  iana: ["America/Costa_Rica"]
}, {
  windowsName: "Central America Standard Time",
  territory: "EC",
  iana: ["Pacific/Galapagos"]
}, {
  windowsName: "Central America Standard Time",
  territory: "GT",
  iana: ["America/Guatemala"]
}, {
  windowsName: "Central America Standard Time",
  territory: "HN",
  iana: ["America/Tegucigalpa"]
}, {
  windowsName: "Central America Standard Time",
  territory: "NI",
  iana: ["America/Managua"]
}, {
  windowsName: "Central America Standard Time",
  territory: "SV",
  iana: ["America/El_Salvador"]
}, {
  windowsName: "Central America Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+6"]
}, {
  windowsName: "Central Standard Time",
  territory: "001",
  iana: ["America/Chicago"]
}, {
  windowsName: "Central Standard Time",
  territory: "CA",
  iana: ["America/Winnipeg", "America/Rainy_River", "America/Rankin_Inlet", "America/Resolute"]
}, {
  windowsName: "Central Standard Time",
  territory: "MX",
  iana: ["America/Matamoros"]
}, {
  windowsName: "Central Standard Time",
  territory: "US",
  iana: ["America/Chicago", "America/Indiana/Knox", "America/Indiana/Tell_City", "America/Menominee", "America/North_Dakota/Beulah", "America/North_Dakota/Center", "America/North_Dakota/New_Salem"]
}, {
  windowsName: "Central Standard Time",
  territory: "ZZ",
  iana: ["CST6CDT"]
}, {
  windowsName: "Easter Island Standard Time",
  territory: "001",
  iana: ["Pacific/Easter"]
}, {
  windowsName: "Easter Island Standard Time",
  territory: "CL",
  iana: ["Pacific/Easter"]
}, {
  windowsName: "Central Standard Time (Mexico)",
  territory: "001",
  iana: ["America/Mexico_City"]
}, {
  windowsName: "Central Standard Time (Mexico)",
  territory: "MX",
  iana: ["America/Mexico_City", "America/Bahia_Banderas", "America/Merida", "America/Monterrey"]
}, {
  windowsName: "Canada Central Standard Time",
  territory: "001",
  iana: ["America/Regina"]
}, {
  windowsName: "Canada Central Standard Time",
  territory: "CA",
  iana: ["America/Regina", "America/Swift_Current"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "001",
  iana: ["America/Bogota"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "BR",
  iana: ["America/Rio_Branco", "America/Eirunepe"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "CA",
  iana: ["America/Coral_Harbour"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "CO",
  iana: ["America/Bogota"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "EC",
  iana: ["America/Guayaquil"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "JM",
  iana: ["America/Jamaica"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "KY",
  iana: ["America/Cayman"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "PA",
  iana: ["America/Panama"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "PE",
  iana: ["America/Lima"]
}, {
  windowsName: "SA Pacific Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+5"]
}, {
  windowsName: "Eastern Standard Time (Mexico)",
  territory: "001",
  iana: ["America/Cancun"]
}, {
  windowsName: "Eastern Standard Time (Mexico)",
  territory: "MX",
  iana: ["America/Cancun"]
}, {
  windowsName: "Eastern Standard Time",
  territory: "001",
  iana: ["America/New_York"]
}, {
  windowsName: "Eastern Standard Time",
  territory: "BS",
  iana: ["America/Nassau"]
}, {
  windowsName: "Eastern Standard Time",
  territory: "CA",
  iana: ["America/Toronto", "America/Iqaluit", "America/Montreal", "America/Nipigon", "America/Pangnirtung", "America/Thunder_Bay"]
}, {
  windowsName: "Eastern Standard Time",
  territory: "US",
  iana: ["America/New_York", "America/Detroit", "America/Indiana/Petersburg", "America/Indiana/Vincennes", "America/Indiana/Winamac", "America/Kentucky/Monticello", "America/Louisville"]
}, {
  windowsName: "Eastern Standard Time",
  territory: "ZZ",
  iana: ["EST5EDT"]
}, {
  windowsName: "Haiti Standard Time",
  territory: "001",
  iana: ["America/Port-au-Prince"]
}, {
  windowsName: "Haiti Standard Time",
  territory: "HT",
  iana: ["America/Port-au-Prince"]
}, {
  windowsName: "Cuba Standard Time",
  territory: "001",
  iana: ["America/Havana"]
}, {
  windowsName: "Cuba Standard Time",
  territory: "CU",
  iana: ["America/Havana"]
}, {
  windowsName: "US Eastern Standard Time",
  territory: "001",
  iana: ["America/Indianapolis"]
}, {
  windowsName: "US Eastern Standard Time",
  territory: "US",
  iana: ["America/Indianapolis", "America/Indiana/Marengo", "America/Indiana/Vevay"]
}, {
  windowsName: "Turks And Caicos Standard Time",
  territory: "001",
  iana: ["America/Grand_Turk"]
}, {
  windowsName: "Turks And Caicos Standard Time",
  territory: "TC",
  iana: ["America/Grand_Turk"]
}, {
  windowsName: "Paraguay Standard Time",
  territory: "001",
  iana: ["America/Asuncion"]
}, {
  windowsName: "Paraguay Standard Time",
  territory: "PY",
  iana: ["America/Asuncion"]
}, {
  windowsName: "Atlantic Standard Time",
  territory: "001",
  iana: ["America/Halifax"]
}, {
  windowsName: "Atlantic Standard Time",
  territory: "BM",
  iana: ["Atlantic/Bermuda"]
}, {
  windowsName: "Atlantic Standard Time",
  territory: "CA",
  iana: ["America/Halifax", "America/Glace_Bay", "America/Goose_Bay", "America/Moncton"]
}, {
  windowsName: "Atlantic Standard Time",
  territory: "GL",
  iana: ["America/Thule"]
}, {
  windowsName: "Venezuela Standard Time",
  territory: "001",
  iana: ["America/Caracas"]
}, {
  windowsName: "Venezuela Standard Time",
  territory: "VE",
  iana: ["America/Caracas"]
}, {
  windowsName: "Central Brazilian Standard Time",
  territory: "001",
  iana: ["America/Cuiaba"]
}, {
  windowsName: "Central Brazilian Standard Time",
  territory: "BR",
  iana: ["America/Cuiaba", "America/Campo_Grande"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "001",
  iana: ["America/La_Paz"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "AG",
  iana: ["America/Antigua"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "AI",
  iana: ["America/Anguilla"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "AW",
  iana: ["America/Aruba"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "BB",
  iana: ["America/Barbados"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "BL",
  iana: ["America/St_Barthelemy"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "BO",
  iana: ["America/La_Paz"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "BQ",
  iana: ["America/Kralendijk"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "BR",
  iana: ["America/Manaus", "America/Boa_Vista", "America/Porto_Velho"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "CA",
  iana: ["America/Blanc-Sablon"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "CW",
  iana: ["America/Curacao"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "DM",
  iana: ["America/Dominica"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "DO",
  iana: ["America/Santo_Domingo"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "GD",
  iana: ["America/Grenada"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "GP",
  iana: ["America/Guadeloupe"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "GY",
  iana: ["America/Guyana"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "KN",
  iana: ["America/St_Kitts"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "LC",
  iana: ["America/St_Lucia"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "MF",
  iana: ["America/Marigot"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "MQ",
  iana: ["America/Martinique"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "MS",
  iana: ["America/Montserrat"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "PR",
  iana: ["America/Puerto_Rico"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "SX",
  iana: ["America/Lower_Princes"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "TT",
  iana: ["America/Port_of_Spain"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "VC",
  iana: ["America/St_Vincent"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "VG",
  iana: ["America/Tortola"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "VI",
  iana: ["America/St_Thomas"]
}, {
  windowsName: "SA Western Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+4"]
}, {
  windowsName: "Pacific SA Standard Time",
  territory: "001",
  iana: ["America/Santiago"]
}, {
  windowsName: "Pacific SA Standard Time",
  territory: "CL",
  iana: ["America/Santiago"]
}, {
  windowsName: "Newfoundland Standard Time",
  territory: "001",
  iana: ["America/St_Johns"]
}, {
  windowsName: "Newfoundland Standard Time",
  territory: "CA",
  iana: ["America/St_Johns"]
}, {
  windowsName: "Tocantins Standard Time",
  territory: "001",
  iana: ["America/Araguaina"]
}, {
  windowsName: "Tocantins Standard Time",
  territory: "BR",
  iana: ["America/Araguaina"]
}, {
  windowsName: "E. South America Standard Time",
  territory: "001",
  iana: ["America/Sao_Paulo"]
}, {
  windowsName: "E. South America Standard Time",
  territory: "BR",
  iana: ["America/Sao_Paulo"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "001",
  iana: ["America/Cayenne"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "AQ",
  iana: ["Antarctica/Rothera", "Antarctica/Palmer"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "BR",
  iana: ["America/Fortaleza", "America/Belem", "America/Maceio", "America/Recife", "America/Santarem"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "FK",
  iana: ["Atlantic/Stanley"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "GF",
  iana: ["America/Cayenne"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "SR",
  iana: ["America/Paramaribo"]
}, {
  windowsName: "SA Eastern Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+3"]
}, {
  windowsName: "Argentina Standard Time",
  territory: "001",
  iana: ["America/Buenos_Aires"]
}, {
  windowsName: "Argentina Standard Time",
  territory: "AR",
  iana: ["America/Buenos_Aires", "America/Argentina/La_Rioja", "America/Argentina/Rio_Gallegos", "America/Argentina/Salta", "America/Argentina/San_Juan", "America/Argentina/San_Luis", "America/Argentina/Tucuman", "America/Argentina/Ushuaia", "America/Catamarca", "America/Cordoba", "America/Jujuy", "America/Mendoza"]
}, {
  windowsName: "Greenland Standard Time",
  territory: "001",
  iana: ["America/Godthab"]
}, {
  windowsName: "Greenland Standard Time",
  territory: "GL",
  iana: ["America/Godthab"]
}, {
  windowsName: "Montevideo Standard Time",
  territory: "001",
  iana: ["America/Montevideo"]
}, {
  windowsName: "Montevideo Standard Time",
  territory: "UY",
  iana: ["America/Montevideo"]
}, {
  windowsName: "Magallanes Standard Time",
  territory: "001",
  iana: ["America/Punta_Arenas"]
}, {
  windowsName: "Magallanes Standard Time",
  territory: "CL",
  iana: ["America/Punta_Arenas"]
}, {
  windowsName: "Saint Pierre Standard Time",
  territory: "001",
  iana: ["America/Miquelon"]
}, {
  windowsName: "Saint Pierre Standard Time",
  territory: "PM",
  iana: ["America/Miquelon"]
}, {
  windowsName: "Bahia Standard Time",
  territory: "001",
  iana: ["America/Bahia"]
}, {
  windowsName: "Bahia Standard Time",
  territory: "BR",
  iana: ["America/Bahia"]
}, {
  windowsName: "UTC-02",
  territory: "001",
  iana: ["Etc/GMT+2"]
}, {
  windowsName: "UTC-02",
  territory: "BR",
  iana: ["America/Noronha"]
}, {
  windowsName: "UTC-02",
  territory: "GS",
  iana: ["Atlantic/South_Georgia"]
}, {
  windowsName: "UTC-02",
  territory: "ZZ",
  iana: ["Etc/GMT+2"]
}, {
  windowsName: "Azores Standard Time",
  territory: "001",
  iana: ["Atlantic/Azores"]
}, {
  windowsName: "Azores Standard Time",
  territory: "GL",
  iana: ["America/Scoresbysund"]
}, {
  windowsName: "Azores Standard Time",
  territory: "PT",
  iana: ["Atlantic/Azores"]
}, {
  windowsName: "Cape Verde Standard Time",
  territory: "001",
  iana: ["Atlantic/Cape_Verde"]
}, {
  windowsName: "Cape Verde Standard Time",
  territory: "CV",
  iana: ["Atlantic/Cape_Verde"]
}, {
  windowsName: "Cape Verde Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT+1"]
}, {
  windowsName: "UTC",
  territory: "001",
  iana: ["Etc/UTC"]
}, {
  windowsName: "UTC",
  territory: "ZZ",
  iana: ["Etc/UTC", "Etc/GMT"]
}, {
  windowsName: "GMT Standard Time",
  territory: "001",
  iana: ["Europe/London"]
}, {
  windowsName: "GMT Standard Time",
  territory: "ES",
  iana: ["Atlantic/Canary"]
}, {
  windowsName: "GMT Standard Time",
  territory: "FO",
  iana: ["Atlantic/Faeroe"]
}, {
  windowsName: "GMT Standard Time",
  territory: "GB",
  iana: ["Europe/London"]
}, {
  windowsName: "GMT Standard Time",
  territory: "GG",
  iana: ["Europe/Guernsey"]
}, {
  windowsName: "GMT Standard Time",
  territory: "IE",
  iana: ["Europe/Dublin"]
}, {
  windowsName: "GMT Standard Time",
  territory: "IM",
  iana: ["Europe/Isle_of_Man"]
}, {
  windowsName: "GMT Standard Time",
  territory: "JE",
  iana: ["Europe/Jersey"]
}, {
  windowsName: "GMT Standard Time",
  territory: "PT",
  iana: ["Europe/Lisbon", "Atlantic/Madeira"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "001",
  iana: ["Atlantic/Reykjavik"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "BF",
  iana: ["Africa/Ouagadougou"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "CI",
  iana: ["Africa/Abidjan"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "GH",
  iana: ["Africa/Accra"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "GL",
  iana: ["America/Danmarkshavn"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "GM",
  iana: ["Africa/Banjul"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "GN",
  iana: ["Africa/Conakry"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "GW",
  iana: ["Africa/Bissau"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "IS",
  iana: ["Atlantic/Reykjavik"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "LR",
  iana: ["Africa/Monrovia"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "ML",
  iana: ["Africa/Bamako"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "MR",
  iana: ["Africa/Nouakchott"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "SH",
  iana: ["Atlantic/St_Helena"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "SL",
  iana: ["Africa/Freetown"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "SN",
  iana: ["Africa/Dakar"]
}, {
  windowsName: "Greenwich Standard Time",
  territory: "TG",
  iana: ["Africa/Lome"]
}, {
  windowsName: "Sao Tome Standard Time",
  territory: "001",
  iana: ["Africa/Sao_Tome"]
}, {
  windowsName: "Sao Tome Standard Time",
  territory: "ST",
  iana: ["Africa/Sao_Tome"]
}, {
  windowsName: "Morocco Standard Time",
  territory: "001",
  iana: ["Africa/Casablanca"]
}, {
  windowsName: "Morocco Standard Time",
  territory: "EH",
  iana: ["Africa/El_Aaiun"]
}, {
  windowsName: "Morocco Standard Time",
  territory: "MA",
  iana: ["Africa/Casablanca"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "001",
  iana: ["Europe/Berlin"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "AD",
  iana: ["Europe/Andorra"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "AT",
  iana: ["Europe/Vienna"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "CH",
  iana: ["Europe/Zurich"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "DE",
  iana: ["Europe/Berlin", "Europe/Busingen"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "GI",
  iana: ["Europe/Gibraltar"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "IT",
  iana: ["Europe/Rome"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "LI",
  iana: ["Europe/Vaduz"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "LU",
  iana: ["Europe/Luxembourg"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "MC",
  iana: ["Europe/Monaco"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "MT",
  iana: ["Europe/Malta"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "NL",
  iana: ["Europe/Amsterdam"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "NO",
  iana: ["Europe/Oslo"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "SE",
  iana: ["Europe/Stockholm"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "SJ",
  iana: ["Arctic/Longyearbyen"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "SM",
  iana: ["Europe/San_Marino"]
}, {
  windowsName: "W. Europe Standard Time",
  territory: "VA",
  iana: ["Europe/Vatican"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "001",
  iana: ["Europe/Budapest"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "AL",
  iana: ["Europe/Tirane"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "CZ",
  iana: ["Europe/Prague"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "HU",
  iana: ["Europe/Budapest"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "ME",
  iana: ["Europe/Podgorica"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "RS",
  iana: ["Europe/Belgrade"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "SI",
  iana: ["Europe/Ljubljana"]
}, {
  windowsName: "Central Europe Standard Time",
  territory: "SK",
  iana: ["Europe/Bratislava"]
}, {
  windowsName: "Romance Standard Time",
  territory: "001",
  iana: ["Europe/Paris"]
}, {
  windowsName: "Romance Standard Time",
  territory: "BE",
  iana: ["Europe/Brussels"]
}, {
  windowsName: "Romance Standard Time",
  territory: "DK",
  iana: ["Europe/Copenhagen"]
}, {
  windowsName: "Romance Standard Time",
  territory: "ES",
  iana: ["Europe/Madrid", "Africa/Ceuta"]
}, {
  windowsName: "Romance Standard Time",
  territory: "FR",
  iana: ["Europe/Paris"]
}, {
  windowsName: "Central European Standard Time",
  territory: "001",
  iana: ["Europe/Warsaw"]
}, {
  windowsName: "Central European Standard Time",
  territory: "BA",
  iana: ["Europe/Sarajevo"]
}, {
  windowsName: "Central European Standard Time",
  territory: "HR",
  iana: ["Europe/Zagreb"]
}, {
  windowsName: "Central European Standard Time",
  territory: "MK",
  iana: ["Europe/Skopje"]
}, {
  windowsName: "Central European Standard Time",
  territory: "PL",
  iana: ["Europe/Warsaw"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "001",
  iana: ["Africa/Lagos"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "AO",
  iana: ["Africa/Luanda"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "BJ",
  iana: ["Africa/Porto-Novo"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "CD",
  iana: ["Africa/Kinshasa"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "CF",
  iana: ["Africa/Bangui"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "CG",
  iana: ["Africa/Brazzaville"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "CM",
  iana: ["Africa/Douala"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "DZ",
  iana: ["Africa/Algiers"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "GA",
  iana: ["Africa/Libreville"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "GQ",
  iana: ["Africa/Malabo"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "NE",
  iana: ["Africa/Niamey"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "NG",
  iana: ["Africa/Lagos"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "TD",
  iana: ["Africa/Ndjamena"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "TN",
  iana: ["Africa/Tunis"]
}, {
  windowsName: "W. Central Africa Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-1"]
}, {
  windowsName: "Jordan Standard Time",
  territory: "001",
  iana: ["Asia/Amman"]
}, {
  windowsName: "Jordan Standard Time",
  territory: "JO",
  iana: ["Asia/Amman"]
}, {
  windowsName: "GTB Standard Time",
  territory: "001",
  iana: ["Europe/Bucharest"]
}, {
  windowsName: "GTB Standard Time",
  territory: "CY",
  iana: ["Asia/Nicosia", "Asia/Famagusta"]
}, {
  windowsName: "GTB Standard Time",
  territory: "GR",
  iana: ["Europe/Athens"]
}, {
  windowsName: "GTB Standard Time",
  territory: "RO",
  iana: ["Europe/Bucharest"]
}, {
  windowsName: "Middle East Standard Time",
  territory: "001",
  iana: ["Asia/Beirut"]
}, {
  windowsName: "Middle East Standard Time",
  territory: "LB",
  iana: ["Asia/Beirut"]
}, {
  windowsName: "Egypt Standard Time",
  territory: "001",
  iana: ["Africa/Cairo"]
}, {
  windowsName: "Egypt Standard Time",
  territory: "EG",
  iana: ["Africa/Cairo"]
}, {
  windowsName: "E. Europe Standard Time",
  territory: "001",
  iana: ["Europe/Chisinau"]
}, {
  windowsName: "E. Europe Standard Time",
  territory: "MD",
  iana: ["Europe/Chisinau"]
}, {
  windowsName: "Syria Standard Time",
  territory: "001",
  iana: ["Asia/Damascus"]
}, {
  windowsName: "Syria Standard Time",
  territory: "SY",
  iana: ["Asia/Damascus"]
}, {
  windowsName: "West Bank Standard Time",
  territory: "001",
  iana: ["Asia/Hebron"]
}, {
  windowsName: "West Bank Standard Time",
  territory: "PS",
  iana: ["Asia/Hebron", "Asia/Gaza"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "001",
  iana: ["Africa/Johannesburg"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "BI",
  iana: ["Africa/Bujumbura"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "BW",
  iana: ["Africa/Gaborone"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "CD",
  iana: ["Africa/Lubumbashi"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "LS",
  iana: ["Africa/Maseru"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "MW",
  iana: ["Africa/Blantyre"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "MZ",
  iana: ["Africa/Maputo"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "RW",
  iana: ["Africa/Kigali"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "SZ",
  iana: ["Africa/Mbabane"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "ZA",
  iana: ["Africa/Johannesburg"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "ZM",
  iana: ["Africa/Lusaka"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "ZW",
  iana: ["Africa/Harare"]
}, {
  windowsName: "South Africa Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-2"]
}, {
  windowsName: "FLE Standard Time",
  territory: "001",
  iana: ["Europe/Kiev"]
}, {
  windowsName: "FLE Standard Time",
  territory: "AX",
  iana: ["Europe/Mariehamn"]
}, {
  windowsName: "FLE Standard Time",
  territory: "BG",
  iana: ["Europe/Sofia"]
}, {
  windowsName: "FLE Standard Time",
  territory: "EE",
  iana: ["Europe/Tallinn"]
}, {
  windowsName: "FLE Standard Time",
  territory: "FI",
  iana: ["Europe/Helsinki"]
}, {
  windowsName: "FLE Standard Time",
  territory: "LT",
  iana: ["Europe/Vilnius"]
}, {
  windowsName: "FLE Standard Time",
  territory: "LV",
  iana: ["Europe/Riga"]
}, {
  windowsName: "FLE Standard Time",
  territory: "UA",
  iana: ["Europe/Kiev", "Europe/Uzhgorod", "Europe/Zaporozhye"]
}, {
  windowsName: "Israel Standard Time",
  territory: "001",
  iana: ["Asia/Jerusalem"]
}, {
  windowsName: "Israel Standard Time",
  territory: "IL",
  iana: ["Asia/Jerusalem"]
}, {
  windowsName: "South Sudan Standard Time",
  territory: "001",
  iana: ["Africa/Juba"]
}, {
  windowsName: "South Sudan Standard Time",
  territory: "SS",
  iana: ["Africa/Juba"]
}, {
  windowsName: "Kaliningrad Standard Time",
  territory: "001",
  iana: ["Europe/Kaliningrad"]
}, {
  windowsName: "Kaliningrad Standard Time",
  territory: "RU",
  iana: ["Europe/Kaliningrad"]
}, {
  windowsName: "Sudan Standard Time",
  territory: "001",
  iana: ["Africa/Khartoum"]
}, {
  windowsName: "Sudan Standard Time",
  territory: "SD",
  iana: ["Africa/Khartoum"]
}, {
  windowsName: "Libya Standard Time",
  territory: "001",
  iana: ["Africa/Tripoli"]
}, {
  windowsName: "Libya Standard Time",
  territory: "LY",
  iana: ["Africa/Tripoli"]
}, {
  windowsName: "Namibia Standard Time",
  territory: "001",
  iana: ["Africa/Windhoek"]
}, {
  windowsName: "Namibia Standard Time",
  territory: "NA",
  iana: ["Africa/Windhoek"]
}, {
  windowsName: "Arabic Standard Time",
  territory: "001",
  iana: ["Asia/Baghdad"]
}, {
  windowsName: "Arabic Standard Time",
  territory: "IQ",
  iana: ["Asia/Baghdad"]
}, {
  windowsName: "Turkey Standard Time",
  territory: "001",
  iana: ["Europe/Istanbul"]
}, {
  windowsName: "Turkey Standard Time",
  territory: "TR",
  iana: ["Europe/Istanbul"]
}, {
  windowsName: "Arab Standard Time",
  territory: "001",
  iana: ["Asia/Riyadh"]
}, {
  windowsName: "Arab Standard Time",
  territory: "BH",
  iana: ["Asia/Bahrain"]
}, {
  windowsName: "Arab Standard Time",
  territory: "KW",
  iana: ["Asia/Kuwait"]
}, {
  windowsName: "Arab Standard Time",
  territory: "QA",
  iana: ["Asia/Qatar"]
}, {
  windowsName: "Arab Standard Time",
  territory: "SA",
  iana: ["Asia/Riyadh"]
}, {
  windowsName: "Arab Standard Time",
  territory: "YE",
  iana: ["Asia/Aden"]
}, {
  windowsName: "Belarus Standard Time",
  territory: "001",
  iana: ["Europe/Minsk"]
}, {
  windowsName: "Belarus Standard Time",
  territory: "BY",
  iana: ["Europe/Minsk"]
}, {
  windowsName: "Russian Standard Time",
  territory: "001",
  iana: ["Europe/Moscow"]
}, {
  windowsName: "Russian Standard Time",
  territory: "RU",
  iana: ["Europe/Moscow", "Europe/Kirov"]
}, {
  windowsName: "Russian Standard Time",
  territory: "UA",
  iana: ["Europe/Simferopol"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "001",
  iana: ["Africa/Nairobi"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "AQ",
  iana: ["Antarctica/Syowa"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "DJ",
  iana: ["Africa/Djibouti"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "ER",
  iana: ["Africa/Asmera"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "ET",
  iana: ["Africa/Addis_Ababa"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "KE",
  iana: ["Africa/Nairobi"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "KM",
  iana: ["Indian/Comoro"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "MG",
  iana: ["Indian/Antananarivo"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "SO",
  iana: ["Africa/Mogadishu"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "TZ",
  iana: ["Africa/Dar_es_Salaam"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "UG",
  iana: ["Africa/Kampala"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "YT",
  iana: ["Indian/Mayotte"]
}, {
  windowsName: "E. Africa Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-3"]
}, {
  windowsName: "Iran Standard Time",
  territory: "001",
  iana: ["Asia/Tehran"]
}, {
  windowsName: "Iran Standard Time",
  territory: "IR",
  iana: ["Asia/Tehran"]
}, {
  windowsName: "Arabian Standard Time",
  territory: "001",
  iana: ["Asia/Dubai"]
}, {
  windowsName: "Arabian Standard Time",
  territory: "AE",
  iana: ["Asia/Dubai"]
}, {
  windowsName: "Arabian Standard Time",
  territory: "OM",
  iana: ["Asia/Muscat"]
}, {
  windowsName: "Arabian Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-4"]
}, {
  windowsName: "Astrakhan Standard Time",
  territory: "001",
  iana: ["Europe/Astrakhan"]
}, {
  windowsName: "Astrakhan Standard Time",
  territory: "RU",
  iana: ["Europe/Astrakhan", "Europe/Ulyanovsk"]
}, {
  windowsName: "Azerbaijan Standard Time",
  territory: "001",
  iana: ["Asia/Baku"]
}, {
  windowsName: "Azerbaijan Standard Time",
  territory: "AZ",
  iana: ["Asia/Baku"]
}, {
  windowsName: "Russia Time Zone 3",
  territory: "001",
  iana: ["Europe/Samara"]
}, {
  windowsName: "Russia Time Zone 3",
  territory: "RU",
  iana: ["Europe/Samara"]
}, {
  windowsName: "Mauritius Standard Time",
  territory: "001",
  iana: ["Indian/Mauritius"]
}, {
  windowsName: "Mauritius Standard Time",
  territory: "MU",
  iana: ["Indian/Mauritius"]
}, {
  windowsName: "Mauritius Standard Time",
  territory: "RE",
  iana: ["Indian/Reunion"]
}, {
  windowsName: "Mauritius Standard Time",
  territory: "SC",
  iana: ["Indian/Mahe"]
}, {
  windowsName: "Saratov Standard Time",
  territory: "001",
  iana: ["Europe/Saratov"]
}, {
  windowsName: "Saratov Standard Time",
  territory: "RU",
  iana: ["Europe/Saratov"]
}, {
  windowsName: "Georgian Standard Time",
  territory: "001",
  iana: ["Asia/Tbilisi"]
}, {
  windowsName: "Georgian Standard Time",
  territory: "GE",
  iana: ["Asia/Tbilisi"]
}, {
  windowsName: "Volgograd Standard Time",
  territory: "001",
  iana: ["Europe/Volgograd"]
}, {
  windowsName: "Volgograd Standard Time",
  territory: "RU",
  iana: ["Europe/Volgograd"]
}, {
  windowsName: "Caucasus Standard Time",
  territory: "001",
  iana: ["Asia/Yerevan"]
}, {
  windowsName: "Caucasus Standard Time",
  territory: "AM",
  iana: ["Asia/Yerevan"]
}, {
  windowsName: "Afghanistan Standard Time",
  territory: "001",
  iana: ["Asia/Kabul"]
}, {
  windowsName: "Afghanistan Standard Time",
  territory: "AF",
  iana: ["Asia/Kabul"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "001",
  iana: ["Asia/Tashkent"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "AQ",
  iana: ["Antarctica/Mawson"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "KZ",
  iana: ["Asia/Oral", "Asia/Aqtau", "Asia/Aqtobe", "Asia/Atyrau"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "MV",
  iana: ["Indian/Maldives"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "TF",
  iana: ["Indian/Kerguelen"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "TJ",
  iana: ["Asia/Dushanbe"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "TM",
  iana: ["Asia/Ashgabat"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "UZ",
  iana: ["Asia/Tashkent", "Asia/Samarkand"]
}, {
  windowsName: "West Asia Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-5"]
}, {
  windowsName: "Ekaterinburg Standard Time",
  territory: "001",
  iana: ["Asia/Yekaterinburg"]
}, {
  windowsName: "Ekaterinburg Standard Time",
  territory: "RU",
  iana: ["Asia/Yekaterinburg"]
}, {
  windowsName: "Pakistan Standard Time",
  territory: "001",
  iana: ["Asia/Karachi"]
}, {
  windowsName: "Pakistan Standard Time",
  territory: "PK",
  iana: ["Asia/Karachi"]
}, {
  windowsName: "Qyzylorda Standard Time",
  territory: "001",
  iana: ["Asia/Qyzylorda"]
}, {
  windowsName: "Qyzylorda Standard Time",
  territory: "KZ",
  iana: ["Asia/Qyzylorda"]
}, {
  windowsName: "India Standard Time",
  territory: "001",
  iana: ["Asia/Calcutta"]
}, {
  windowsName: "India Standard Time",
  territory: "IN",
  iana: ["Asia/Calcutta"]
}, {
  windowsName: "Sri Lanka Standard Time",
  territory: "001",
  iana: ["Asia/Colombo"]
}, {
  windowsName: "Sri Lanka Standard Time",
  territory: "LK",
  iana: ["Asia/Colombo"]
}, {
  windowsName: "Nepal Standard Time",
  territory: "001",
  iana: ["Asia/Katmandu"]
}, {
  windowsName: "Nepal Standard Time",
  territory: "NP",
  iana: ["Asia/Katmandu"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "001",
  iana: ["Asia/Almaty"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "AQ",
  iana: ["Antarctica/Vostok"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "CN",
  iana: ["Asia/Urumqi"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "IO",
  iana: ["Indian/Chagos"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "KG",
  iana: ["Asia/Bishkek"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "KZ",
  iana: ["Asia/Almaty", "Asia/Qostanay"]
}, {
  windowsName: "Central Asia Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-6"]
}, {
  windowsName: "Bangladesh Standard Time",
  territory: "001",
  iana: ["Asia/Dhaka"]
}, {
  windowsName: "Bangladesh Standard Time",
  territory: "BD",
  iana: ["Asia/Dhaka"]
}, {
  windowsName: "Bangladesh Standard Time",
  territory: "BT",
  iana: ["Asia/Thimphu"]
}, {
  windowsName: "Omsk Standard Time",
  territory: "001",
  iana: ["Asia/Omsk"]
}, {
  windowsName: "Omsk Standard Time",
  territory: "RU",
  iana: ["Asia/Omsk"]
}, {
  windowsName: "Myanmar Standard Time",
  territory: "001",
  iana: ["Asia/Rangoon"]
}, {
  windowsName: "Myanmar Standard Time",
  territory: "CC",
  iana: ["Indian/Cocos"]
}, {
  windowsName: "Myanmar Standard Time",
  territory: "MM",
  iana: ["Asia/Rangoon"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "001",
  iana: ["Asia/Bangkok"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "AQ",
  iana: ["Antarctica/Davis"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "CX",
  iana: ["Indian/Christmas"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "ID",
  iana: ["Asia/Jakarta", "Asia/Pontianak"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "KH",
  iana: ["Asia/Phnom_Penh"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "LA",
  iana: ["Asia/Vientiane"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "TH",
  iana: ["Asia/Bangkok"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "VN",
  iana: ["Asia/Saigon"]
}, {
  windowsName: "SE Asia Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-7"]
}, {
  windowsName: "Altai Standard Time",
  territory: "001",
  iana: ["Asia/Barnaul"]
}, {
  windowsName: "Altai Standard Time",
  territory: "RU",
  iana: ["Asia/Barnaul"]
}, {
  windowsName: "W. Mongolia Standard Time",
  territory: "001",
  iana: ["Asia/Hovd"]
}, {
  windowsName: "W. Mongolia Standard Time",
  territory: "MN",
  iana: ["Asia/Hovd"]
}, {
  windowsName: "North Asia Standard Time",
  territory: "001",
  iana: ["Asia/Krasnoyarsk"]
}, {
  windowsName: "North Asia Standard Time",
  territory: "RU",
  iana: ["Asia/Krasnoyarsk", "Asia/Novokuznetsk"]
}, {
  windowsName: "N. Central Asia Standard Time",
  territory: "001",
  iana: ["Asia/Novosibirsk"]
}, {
  windowsName: "N. Central Asia Standard Time",
  territory: "RU",
  iana: ["Asia/Novosibirsk"]
}, {
  windowsName: "Tomsk Standard Time",
  territory: "001",
  iana: ["Asia/Tomsk"]
}, {
  windowsName: "Tomsk Standard Time",
  territory: "RU",
  iana: ["Asia/Tomsk"]
}, {
  windowsName: "China Standard Time",
  territory: "001",
  iana: ["Asia/Shanghai"]
}, {
  windowsName: "China Standard Time",
  territory: "CN",
  iana: ["Asia/Shanghai"]
}, {
  windowsName: "China Standard Time",
  territory: "HK",
  iana: ["Asia/Hong_Kong"]
}, {
  windowsName: "China Standard Time",
  territory: "MO",
  iana: ["Asia/Macau"]
}, {
  windowsName: "North Asia East Standard Time",
  territory: "001",
  iana: ["Asia/Irkutsk"]
}, {
  windowsName: "North Asia East Standard Time",
  territory: "RU",
  iana: ["Asia/Irkutsk"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "001",
  iana: ["Asia/Singapore"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "BN",
  iana: ["Asia/Brunei"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "ID",
  iana: ["Asia/Makassar"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "MY",
  iana: ["Asia/Kuala_Lumpur", "Asia/Kuching"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "PH",
  iana: ["Asia/Manila"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "SG",
  iana: ["Asia/Singapore"]
}, {
  windowsName: "Singapore Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-8"]
}, {
  windowsName: "W. Australia Standard Time",
  territory: "001",
  iana: ["Australia/Perth"]
}, {
  windowsName: "W. Australia Standard Time",
  territory: "AU",
  iana: ["Australia/Perth"]
}, {
  windowsName: "Taipei Standard Time",
  territory: "001",
  iana: ["Asia/Taipei"]
}, {
  windowsName: "Taipei Standard Time",
  territory: "TW",
  iana: ["Asia/Taipei"]
}, {
  windowsName: "Ulaanbaatar Standard Time",
  territory: "001",
  iana: ["Asia/Ulaanbaatar"]
}, {
  windowsName: "Ulaanbaatar Standard Time",
  territory: "MN",
  iana: ["Asia/Ulaanbaatar", "Asia/Choibalsan"]
}, {
  windowsName: "Aus Central W. Standard Time",
  territory: "001",
  iana: ["Australia/Eucla"]
}, {
  windowsName: "Aus Central W. Standard Time",
  territory: "AU",
  iana: ["Australia/Eucla"]
}, {
  windowsName: "Transbaikal Standard Time",
  territory: "001",
  iana: ["Asia/Chita"]
}, {
  windowsName: "Transbaikal Standard Time",
  territory: "RU",
  iana: ["Asia/Chita"]
}, {
  windowsName: "Tokyo Standard Time",
  territory: "001",
  iana: ["Asia/Tokyo"]
}, {
  windowsName: "Tokyo Standard Time",
  territory: "ID",
  iana: ["Asia/Jayapura"]
}, {
  windowsName: "Tokyo Standard Time",
  territory: "JP",
  iana: ["Asia/Tokyo"]
}, {
  windowsName: "Tokyo Standard Time",
  territory: "PW",
  iana: ["Pacific/Palau"]
}, {
  windowsName: "Tokyo Standard Time",
  territory: "TL",
  iana: ["Asia/Dili"]
}, {
  windowsName: "Tokyo Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-9"]
}, {
  windowsName: "North Korea Standard Time",
  territory: "001",
  iana: ["Asia/Pyongyang"]
}, {
  windowsName: "North Korea Standard Time",
  territory: "KP",
  iana: ["Asia/Pyongyang"]
}, {
  windowsName: "Korea Standard Time",
  territory: "001",
  iana: ["Asia/Seoul"]
}, {
  windowsName: "Korea Standard Time",
  territory: "KR",
  iana: ["Asia/Seoul"]
}, {
  windowsName: "Yakutsk Standard Time",
  territory: "001",
  iana: ["Asia/Yakutsk"]
}, {
  windowsName: "Yakutsk Standard Time",
  territory: "RU",
  iana: ["Asia/Yakutsk", "Asia/Khandyga"]
}, {
  windowsName: "Cen. Australia Standard Time",
  territory: "001",
  iana: ["Australia/Adelaide"]
}, {
  windowsName: "Cen. Australia Standard Time",
  territory: "AU",
  iana: ["Australia/Adelaide", "Australia/Broken_Hill"]
}, {
  windowsName: "AUS Central Standard Time",
  territory: "001",
  iana: ["Australia/Darwin"]
}, {
  windowsName: "AUS Central Standard Time",
  territory: "AU",
  iana: ["Australia/Darwin"]
}, {
  windowsName: "E. Australia Standard Time",
  territory: "001",
  iana: ["Australia/Brisbane"]
}, {
  windowsName: "E. Australia Standard Time",
  territory: "AU",
  iana: ["Australia/Brisbane", "Australia/Lindeman"]
}, {
  windowsName: "AUS Eastern Standard Time",
  territory: "001",
  iana: ["Australia/Sydney"]
}, {
  windowsName: "AUS Eastern Standard Time",
  territory: "AU",
  iana: ["Australia/Sydney", "Australia/Melbourne"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "001",
  iana: ["Pacific/Port_Moresby"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "AQ",
  iana: ["Antarctica/DumontDUrville"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "FM",
  iana: ["Pacific/Truk"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "GU",
  iana: ["Pacific/Guam"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "MP",
  iana: ["Pacific/Saipan"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "PG",
  iana: ["Pacific/Port_Moresby"]
}, {
  windowsName: "West Pacific Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-10"]
}, {
  windowsName: "Tasmania Standard Time",
  territory: "001",
  iana: ["Australia/Hobart"]
}, {
  windowsName: "Tasmania Standard Time",
  territory: "AU",
  iana: ["Australia/Hobart", "Australia/Currie", "Antarctica/Macquarie"]
}, {
  windowsName: "Vladivostok Standard Time",
  territory: "001",
  iana: ["Asia/Vladivostok"]
}, {
  windowsName: "Vladivostok Standard Time",
  territory: "RU",
  iana: ["Asia/Vladivostok", "Asia/Ust-Nera"]
}, {
  windowsName: "Lord Howe Standard Time",
  territory: "001",
  iana: ["Australia/Lord_Howe"]
}, {
  windowsName: "Lord Howe Standard Time",
  territory: "AU",
  iana: ["Australia/Lord_Howe"]
}, {
  windowsName: "Bougainville Standard Time",
  territory: "001",
  iana: ["Pacific/Bougainville"]
}, {
  windowsName: "Bougainville Standard Time",
  territory: "PG",
  iana: ["Pacific/Bougainville"]
}, {
  windowsName: "Russia Time Zone 10",
  territory: "001",
  iana: ["Asia/Srednekolymsk"]
}, {
  windowsName: "Russia Time Zone 10",
  territory: "RU",
  iana: ["Asia/Srednekolymsk"]
}, {
  windowsName: "Magadan Standard Time",
  territory: "001",
  iana: ["Asia/Magadan"]
}, {
  windowsName: "Magadan Standard Time",
  territory: "RU",
  iana: ["Asia/Magadan"]
}, {
  windowsName: "Norfolk Standard Time",
  territory: "001",
  iana: ["Pacific/Norfolk"]
}, {
  windowsName: "Norfolk Standard Time",
  territory: "NF",
  iana: ["Pacific/Norfolk"]
}, {
  windowsName: "Sakhalin Standard Time",
  territory: "001",
  iana: ["Asia/Sakhalin"]
}, {
  windowsName: "Sakhalin Standard Time",
  territory: "RU",
  iana: ["Asia/Sakhalin"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "001",
  iana: ["Pacific/Guadalcanal"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "AQ",
  iana: ["Antarctica/Casey"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "FM",
  iana: ["Pacific/Ponape", "Pacific/Kosrae"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "NC",
  iana: ["Pacific/Noumea"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "SB",
  iana: ["Pacific/Guadalcanal"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "VU",
  iana: ["Pacific/Efate"]
}, {
  windowsName: "Central Pacific Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-11"]
}, {
  windowsName: "Russia Time Zone 11",
  territory: "001",
  iana: ["Asia/Kamchatka"]
}, {
  windowsName: "Russia Time Zone 11",
  territory: "RU",
  iana: ["Asia/Kamchatka", "Asia/Anadyr"]
}, {
  windowsName: "New Zealand Standard Time",
  territory: "001",
  iana: ["Pacific/Auckland"]
}, {
  windowsName: "New Zealand Standard Time",
  territory: "AQ",
  iana: ["Antarctica/McMurdo"]
}, {
  windowsName: "New Zealand Standard Time",
  territory: "NZ",
  iana: ["Pacific/Auckland"]
}, {
  windowsName: "UTC+12",
  territory: "001",
  iana: ["Etc/GMT-12"]
}, {
  windowsName: "UTC+12",
  territory: "KI",
  iana: ["Pacific/Tarawa"]
}, {
  windowsName: "UTC+12",
  territory: "MH",
  iana: ["Pacific/Majuro", "Pacific/Kwajalein"]
}, {
  windowsName: "UTC+12",
  territory: "NR",
  iana: ["Pacific/Nauru"]
}, {
  windowsName: "UTC+12",
  territory: "TV",
  iana: ["Pacific/Funafuti"]
}, {
  windowsName: "UTC+12",
  territory: "UM",
  iana: ["Pacific/Wake"]
}, {
  windowsName: "UTC+12",
  territory: "WF",
  iana: ["Pacific/Wallis"]
}, {
  windowsName: "UTC+12",
  territory: "ZZ",
  iana: ["Etc/GMT-12"]
}, {
  windowsName: "Fiji Standard Time",
  territory: "001",
  iana: ["Pacific/Fiji"]
}, {
  windowsName: "Fiji Standard Time",
  territory: "FJ",
  iana: ["Pacific/Fiji"]
}, {
  windowsName: "Chatham Islands Standard Time",
  territory: "001",
  iana: ["Pacific/Chatham"]
}, {
  windowsName: "Chatham Islands Standard Time",
  territory: "NZ",
  iana: ["Pacific/Chatham"]
}, {
  windowsName: "UTC+13",
  territory: "001",
  iana: ["Etc/GMT-13"]
}, {
  windowsName: "UTC+13",
  territory: "KI",
  iana: ["Pacific/Enderbury"]
}, {
  windowsName: "UTC+13",
  territory: "TK",
  iana: ["Pacific/Fakaofo"]
}, {
  windowsName: "UTC+13",
  territory: "ZZ",
  iana: ["Etc/GMT-13"]
}, {
  windowsName: "Tonga Standard Time",
  territory: "001",
  iana: ["Pacific/Tongatapu"]
}, {
  windowsName: "Tonga Standard Time",
  territory: "TO",
  iana: ["Pacific/Tongatapu"]
}, {
  windowsName: "Samoa Standard Time",
  territory: "001",
  iana: ["Pacific/Apia"]
}, {
  windowsName: "Samoa Standard Time",
  territory: "WS",
  iana: ["Pacific/Apia"]
}, {
  windowsName: "Line Islands Standard Time",
  territory: "001",
  iana: ["Pacific/Kiritimati"]
}, {
  windowsName: "Line Islands Standard Time",
  territory: "KI",
  iana: ["Pacific/Kiritimati"]
}, {
  windowsName: "Line Islands Standard Time",
  territory: "ZZ",
  iana: ["Etc/GMT-14"]
}];
function findIanaAliases(ianaTimeZone) {
  var result = /* @__PURE__ */ new Set();
  IANA_ALIAS_MAP.filter(function(_ref) {
    var alias = _ref.alias;
    return alias.includes(ianaTimeZone);
  }).map(function(it) {
    return it.alias;
  }).flat().forEach(function(it) {
    result.add(it);
  });
  return Array.from(result);
}
function findIana(windowsTimeZone, territory) {
  var set = /* @__PURE__ */ new Set();
  WINDOWS_TO_IANA_MAP.filter(function(it) {
    if (typeof territory === "undefined") {
      return it.windowsName === windowsTimeZone;
    }
    return it.windowsName === windowsTimeZone && it.territory === territory;
  }).map(function(it) {
    return it.iana;
  }).flat().map(findIanaAliases).flat().forEach(function(alias) {
    set.add(alias);
  });
  return Array.from(set);
}
function findWindows(ianaTimeZone) {
  var aliases = findIanaAliases(ianaTimeZone);
  var result = /* @__PURE__ */ new Set();
  WINDOWS_TO_IANA_MAP.filter(function(it) {
    return it.iana.find(function(it2) {
      return aliases.includes(it2);
    });
  }).forEach(function(entry) {
    result.add(entry.windowsName);
  });
  return Array.from(result);
}
export {
  IANA_ALIAS_MAP,
  WINDOWS_TO_IANA_MAP,
  findIana,
  findIanaAliases,
  findWindows
};
//# sourceMappingURL=windows-iana.js.map
