import { federation } from "@module-federation/vite";
import { defineConfig, loadEnv } from "vite";
import { react as vitePluginReact } from "@euroland/vite-plugin-react";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    server: {
      origin: "http://localhost:4007",
      port: 4007,
    },
    base: env.VITE_BASE_URL,
    plugins: [
      vitePluginReact({ hostUrl: "http://localhost:3000" }),
      federation({
        name: "investmentCal2",
        exposes: {
          "./App": "./src/App.jsx",
        },
        shared: {
          react: { singleton: true },
          "react-dom": { singleton: true },
          antd: { singleton: true },
          "react/jsx-runtime": { singleton: true },
          "react/jsx-dev-runtime": { singleton: true },
          "react-dom/client": { singleton: true },
        },

        manifest: true,
      }),
    ],
    build: {
      target: "chrome89",
    },
  };
});
