.rule_prod_template:
  environment:
    name: production
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'

.deploy_prod_template:
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: manual
  needs:
    - prepare:production
    - build:production
    - test:production
    - quality:production
    - package:production

prepare:production:
  extends:
    - .node_template
    - .rule_prod_template
  tags:
    - ee-v-docker2-dind
  stage: prepare
  before_script:
    - apk add --no-cache git
    - chmod +x ./scripts/update-versions-script.sh
  script:
    - ./scripts/update-versions-script.sh
  artifacts:
    paths:
      - version.json
      - package.json
    expire_in: 1 week

build:production:
  extends:
    - .node_template
    - .rule_prod_template
  tags:
    - ee-v-docker2-dind
  stage: build
  needs:
    - prepare:production
  script:
    - npm run build "--" --mode=production
  artifacts:
    paths:
      - dist/
    expire_in: 1 week

test:production:
  extends:
    - .node_template
    - .rule_prod_template
  stage: test
  tags:
    - ee-v-docker2-dind
  needs:
    - prepare:production
    - build:production
  script:
    # - npm run test
    - echo "Missing npm test script..."

quality:production:
  extends:
    - .node_template
    - .rule_prod_template
  tags:
    - ee-v-docker2-dind
  stage: quality
  needs:
    - prepare:production
    - build:production
  script:
    - npm run lint
    # - npm run audit -- --audit-level=high

package:production:
  extends:
    - .node_template
    - .rule_prod_template
  tags:
    - ee-v-docker2-dind
  stage: package
  needs:
    - prepare:production
    - build:production
    - test:production
    - quality:production
  before_script:
    - apk add --no-cache git
    - chmod +x ./scripts/package-client-semantic.sh
  script:
    - echo "Packaging..."
    - './scripts/package-client-semantic.sh'
  artifacts:
    paths:
      - version.json
      - '*.tar.gz'
    expire_in: 1 week

deploy:staging:
  extends:
    - .deploy_prod_template
    - .deploy_frontend_script_template
  tags:
    - ee-buildtest-powershell
  stage: deploy
  environment:
    name: staging
    url: 'https://staging-gr.eurolandir.com/tools/opifex3/modules/ic2/version.txt'
  variables:
    ENVIRONMENT: 'Staging'
    DEPLOY_SERVER1: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'staging-site'
    APP_PATH: '/tools/opifex3/modules/ic2'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

"deploy:ee-v-webcat151,161":
  extends:
    - .deploy_prod_template
    - .deploy_frontend_script_template
  tags:
    - ee-buildtest-powershell
  stage: deploy
  environment:
    name: webcat
    url: 'https://gr-web-ws1.eurolandir.com/tools/opifex3/modules/ic2/version.txt'
  variables:
    ENVIRONMENT: 'Production'
    DEPLOY_SERVER1: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'production-site'
    APP_PATH: '/tools/opifex3/modules/ic2'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

# "deploy:ne-web-ws1,2":
#   extends:
#     - .deploy_prod_template
#     - .deploy_frontend_script_template
#   tags:
#     - ee-buildtest-powershell
#   stage: deploy
#   environment:
#     name: cloud-ne
#     url: 'https://ne-web-ws1.eurolandir.com/tools/CustomerInterface3/shared/auth/version.txt'
#   variables:
#     ENVIRONMENT: 'CloudNE'
#     DEPLOY_SERVER1: 'ne-web-haproxy.northeurope.cloudapp.azure.com'
#     DEPLOY_SERVER_PORT1: 8173
#     DEPLOY_SERVER2: 'ne-web-haproxy.northeurope.cloudapp.azure.com'
#     DEPLOY_SERVER_PORT2: 8172
#     SITE_NAME: 'Default Web Site'
#     APP_PATH: '/tools/CustomerInterface3/shared/auth'
#   before_script:
#     - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
#     - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

# "deploy:ea-web-ws1,2":
#   extends:
#     - .deploy_prod_template
#     - .deploy_frontend_script_template
#   tags:
#     - ee-buildtest-powershell
#   stage: deploy
#   environment:
#     name: cloud-ea
#     url: 'https://ea-web-ws1.eurolandir.com/tools/CustomerInterface3/shared/auth/version.txt'
#   variables:
#     ENVIRONMENT: 'CloudEA'
#     DEPLOY_SERVER1: 'ea-web-haproxy.eastasia.cloudapp.azure.com'
#     DEPLOY_SERVER_PORT1: 8173
#     DEPLOY_SERVER2: 'ea-web-haproxy.eastasia.cloudapp.azure.com'
#     DEPLOY_SERVER_PORT2: 8172
#     SITE_NAME: 'Default Web Site'
#     APP_PATH: '/tools/CustomerInterface3/shared/auth'
#   before_script:
#     - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
#     - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

# "deploy:uae-web-ws1,2":
#   extends:
#     - .deploy_prod_template
#     - .deploy_frontend_script_template
#   tags:
#     - ee-buildtest-powershell
#   stage: deploy
#   environment:
#     name: cloud-uae
#     url: 'https://uae-web-ws1.eurolandir.com/tools/CustomerInterface3/shared/auth/version.txt'
#   variables:
#     ENVIRONMENT: 'CloudUAE'
#     DEPLOY_SERVER1: 'uaewebhaproxy1.uaenorth.cloudapp.azure.com'
#     DEPLOY_SERVER_PORT1: 8173
#     DEPLOY_SERVER2: 'uaewebhaproxy1.uaenorth.cloudapp.azure.com'
#     DEPLOY_SERVER_PORT2: 8172
#     SITE_NAME: 'Default Web Site'
#     APP_PATH: '/tools/CustomerInterface3/shared/auth'
#   before_script:
#     - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
#     - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

# "deploy:ksa-web-ws1,2":
#   extends:
#     - .deploy_prod_template
#     - .deploy_frontend_script_template
#   tags:
#     - ee-buildtest-powershell
#   stage: deploy
#   environment:
#     name: cloud-ksa
#     url: 'https://ksa-web-ws1.eurolandir.com/tools/CustomerInterface3/shared/auth/version.txt'
#   variables:
#     ENVIRONMENT: 'CloudKSA'
#     DEPLOY_SERVER1: '**************'
#     DEPLOY_SERVER_PORT1: 8173
#     DEPLOY_SERVER2: '**************'
#     DEPLOY_SERVER_PORT2: 8172
#     SITE_NAME: 'Default Web Site'
#     APP_PATH: '/tools/CustomerInterface3/shared/auth'
#   before_script:
#     - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
#     - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

# "deploy:cn-web-ws1,2":
#   extends:
#     - .deploy_prod_template
#     - .deploy_frontend_script_template
#   tags:
#     - ee-buildtest-powershell
#   stage: deploy
#   environment:
#     name: cloud-cn
#     url: 'https://cn-web-ws1.eurolandir.com/tools/CustomerInterface3/shared/auth/version.txt'
#   variables:
#     ENVIRONMENT: 'CloudCN'
#     DEPLOY_SERVER1: 'cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
#     DEPLOY_SERVER_PORT1: 8173
#     DEPLOY_SERVER2: 'cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
#     DEPLOY_SERVER_PORT2: 8172
#     SITE_NAME: 'Default Web Site'
#     APP_PATH: '/tools/CustomerInterface3/shared/auth'
#   before_script:
#     - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
#     - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
