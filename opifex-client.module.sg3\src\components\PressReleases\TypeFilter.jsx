import { useState, useEffect, useContext } from 'react';
import { Button, Flex, Form, Table, Tooltip } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { useInputStyle } from '../../hooks/useInputStyle';
import CustomInputTableCell from '../common/CustomInputTableCell';
import { SettingsContext } from '../../context/SettingsContext';
import {
  createUniqueKeyGenerator,
  formatSourceMessageTypeToString,
  getMessageTypeOptions,
  parseSourceMessageTypeString,
} from '../../utils/common';
import { useMemo } from 'react';
import LazyLoad from "../common/LazyLoading";
import SkeletonTable from "./SkeletionTable";

const generateUniqueKey = createUniqueKeyGenerator('TF');

const TypeFilter = ({ onChange, form, sources, fieldPrefix = ['PressReleases'] }) => {
  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const [typeFilterDatas, setTypeFilterDatas] = useState([]);
  const sourceOptions = Object.values(sources)?.map((source) => ({
    label: source?.name,
    value: source?.id?.toString(),
  }));

  const editTypeFilterForm = Form.useWatch(
    [...fieldPrefix, 'EditTypeFilter'],
    { form, preserve: true }
  );

  /**
   * Effect to handle type filter updates for press releases.
   * Processes the editTypeFilterForm data to:
   * 1. Create an array of source and message type pairs
   * 2. Validate each pair against available sources and message types
   * 3. Clear invalid message types
   * 4. Format and update the form with the final type filter string
   * 
   * @effect
   * @dependencies {Object} editTypeFilterForm - Form data containing source and message type selections
   * @dependencies {Object} sources - Available sources with their message types
   * @dependencies {Function} form.setFieldValue - Antd form setter function
   * @dependencies {Function} onChange - Optional callback function to update the parent form
   */
  useEffect(() => {
    const typeFilterArray = [];
    const sourcesArray = Object.values(sources);
    Object.keys(editTypeFilterForm || {})
      ?.filter((itemKey) => !!editTypeFilterForm[itemKey])
      ?.forEach((itemKey) => {
        const sourceMessageType = {
          Source: editTypeFilterForm[itemKey]?.Source || '',
          MessageType: '',
        };
        if (
          sourcesArray.find(
            (src) =>
              src.id?.toString() === editTypeFilterForm[itemKey]?.Source &&
              src.messageTypes?.find(
                (mst) =>
                  mst.messageTypeId?.toString() ===
                  editTypeFilterForm[itemKey]?.MessageType
              )
          )
        ) {
          sourceMessageType['MessageType'] =
            editTypeFilterForm[itemKey]?.MessageType || '';
        } else {
          form.setFieldValue(
            ['PressReleases', 'EditTypeFilter', `${itemKey}`, 'MessageType'],
            ''
          );
        }
        typeFilterArray.push(sourceMessageType);
      });
    const newTypeFilter = formatSourceMessageTypeToString(typeFilterArray);
    form.setFieldValue([...fieldPrefix, 'TypeFilter'], newTypeFilter);
    if (onChange) {
      onChange();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editTypeFilterForm]);

  /**
   * Effect to initialize type filter data for press releases.
   * Handles two scenarios:
   * 1. Initial setup: When EditTypeFilterKeys is not present
   *    - Parses TypeFilter string into structured data
   *    - Filters sources against available sources
   *    - Generates unique keys for each filter entry
   *    - Updates form with initial filter data and keys
   * 2. Existing setup: When EditTypeFilterKeys exists
   *    - Maps existing filter keys to their corresponding values
   * 
   * @effect
   * @dependencies {Object} settings.initState.PressReleases - Initial press release settings
   * @dependencies {Object} sources - Available sources configuration
   * @dependencies {Function} form.setFieldValue - Antd form setter function
   * @dependencies {Function} setTypeFilterDatas - State setter for type filter data
   * @dependencies {Function} onChange - Optional callback function to update the parent form
   */
  useEffect(() => {
    // Get initial data based on fieldPrefix
    const isInstrumentConfig = fieldPrefix.length > 1;
    let initialData = '';

    if (isInstrumentConfig) {
      // For instrument config, get from Instruments.Instrument array
      const instrumentId = fieldPrefix[1];
      const instrumentData = settings?.initState?.Instruments?.Instrument?.find(
        inst => inst.Id?.toString() === instrumentId?.toString()
      );
      initialData = instrumentData?.PressRelease?.TypeFilter || '';
    } else {
      // For global config
      initialData = settings?.initState?.PressReleases?.TypeFilter || '';
    }

    if (!form.getFieldValue([...fieldPrefix, 'EditTypeFilterKeys'])) {
      const editTypeFilter = parseSourceMessageTypeString(initialData)
        ?.filter((item) => Object.keys(sources || {}).includes(item.Source))
        ?.reduce(
          (editTypeFilterObj, item) => ({ ...editTypeFilterObj, [generateUniqueKey()]: item }),
          {}
        );
      const typeFilterKeys = Object.keys(editTypeFilter || {});
      const initTypeFilterDatas = typeFilterKeys?.map((itemKey) => ({
        key: itemKey,
        value: editTypeFilter[itemKey],
      }));

      form.setFieldValue([...fieldPrefix, 'EditTypeFilterKeys'], typeFilterKeys);
      form.setFieldValue([...fieldPrefix, 'EditTypeFilter'], editTypeFilter);
      setTypeFilterDatas(initTypeFilterDatas || []);

      if (onChange) {
        onChange();
      }
    } else {
      const existingKeys = form.getFieldValue([...fieldPrefix, 'EditTypeFilterKeys']);
      const existingData = form.getFieldValue([...fieldPrefix, 'EditTypeFilter']);
      setTypeFilterDatas(
        existingKeys?.map((itemKey) => ({
          key: itemKey,
          value: existingData?.[itemKey],
        })) || []
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sources]);

  /**
   * Handles adding a new type filter to the form.
   * 1. Generates a unique key for the new filter entry
   * 2. Adds the new filter entry to the typeFilterDatas state
   * 3. Updates the form with the new key and filter entry
   * 4. Calls the onChange callback function to update the parent form
   * 
   * @returns {void}
   */
  const handleAddTypeFilter = () => {
    const newKey = generateUniqueKey();

    const newTypeFilterDatas = [...typeFilterDatas, { key: newKey }];
    setTypeFilterDatas(newTypeFilterDatas);
    const currentKeys =
      form.getFieldValue([...fieldPrefix, 'EditTypeFilterKeys']) || [];
    form.setFieldValue(
      [...fieldPrefix, 'EditTypeFilterKeys'],
      [...currentKeys, newKey]
    );

    if (onChange) {
      onChange();
    }
  };

  /**
   * Handles deleting a type filter from the form.
   * 1. Retrieves the key of the filter to be deleted
   * 2. Filters out the specified filter from the typeFilterDatas state
   * 3. Updates the form with the remaining filter keys
   * 4. Calls the onChange callback function to update the parent form
   * 
   * @param {number} index - The index of the filter to be deleted
   * @returns {void}
   */
  const handleDeleteTypeFilter = (index) => {
    const keyToDelete = typeFilterDatas[index]?.key;

    setTypeFilterDatas((typeFilterDatas) =>
      typeFilterDatas.filter((_, i) => i !== index)
    );
    const currentKeys =
      form.getFieldValue([...fieldPrefix, 'EditTypeFilterKeys']) || [];
    form.setFieldValue(
      [...fieldPrefix, 'EditTypeFilterKeys'],
      currentKeys.filter((key) => key !== keyToDelete)
    );
    form.setFieldValue(
      [...fieldPrefix, 'EditTypeFilter', keyToDelete],
      undefined
    );

    if (onChange) {
      onChange();
    }
  };

  /**
   * Defines the columns for the type filter table.
   * 1. Source column: Displays a dropdown for selecting a source
   * 2. Message Type column: Displays a dropdown for selecting a message type
   * 3. Action column: Displays a delete button for removing the filter
   * 
   * @type {import("antd").TableProps<DataType>['columns']}
   */
  const typeFilterColumns = useMemo(() => [
    {
      title: 'Source',
      align: 'left',
      minWidth: 300,
      key: 'Source',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={sourceOptions}
          form={form}
          name={[...fieldPrefix, 'EditTypeFilter', `${record.key}`, 'Source']}
          defaultValue={record.value?.['Source']}
          rules={[{ required: true, message: 'Source is required' }]}
          // isDirectEdit={true}
        />
      ),
    },
    {
      title: 'Message Type',
      align: 'left',
      minWidth: 300,
      key: 'MessageType',
      render: (_, record) => (
        <CustomInputTableCell
          type={'select'}
          options={getMessageTypeOptions(
            sources,
            editTypeFilterForm?.[record.key]?.['Source']
          )}
          form={form}
          name={[
            ...fieldPrefix,
            'EditTypeFilter',
            `${record.key}`,
            'MessageType',
          ]}
          defaultValue={record.value?.['MessageType']}
          rules={[{ required: true, message: 'Message Type is required' }]}
          // isDirectEdit={true}
        />
      ),
    },
    {
      title: 'Action',
      align: 'center',
      width: 100,
      fixed: !isMobile ? 'right' : false,
      key: 'Action',
      render: (_, record, index) => (
        <Tooltip title={`Delete this type filter`}>
          <Button
            type="primary"
            danger
            onClick={() => handleDeleteTypeFilter(index)}
            icon={<DeleteOutlined />}
          />
        </Tooltip>
      ),
    },
  ], []);

  return (
    <LazyLoad threshold={0.1} fallback={<SkeletonTable rowCount={1} />}>
      <Flex justify="flex-start" align="center" className="mb-5">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddTypeFilter}
        >
          Add type filter
        </Button>
      </Flex>
      
      <Table
        key={`type-filter-${fieldPrefix.join('-')}`}
        size={isMobile ? 'small' : 'default'}
        columns={typeFilterColumns}
        dataSource={typeFilterDatas}
        bordered
        pagination={false}
        style={{ width: '100%', marginTop: 16, marginBottom: 24 }}
        scroll={{ x: 'max-content', y: 400 }}
        tableLayout="auto"
        rowKey={`type-filter-${fieldPrefix.join('-')}`}
      />
    </LazyLoad>
  );
};

export default TypeFilter;
