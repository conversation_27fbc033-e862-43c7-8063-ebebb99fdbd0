import { Checkbox, Flex, Form, InputNumber, Select } from "antd";
import { DEFAULT_TAB } from "../../constant/commom";
import { InstrumentConfigs } from "./InstrumentConfigs";
import { useInputStyle } from "../../hooks/useInputStyle";

export const DataItem = ({ form }) => {
  const { inputStyle } = useInputStyle();

  return (
    <>
      <Form.Item label={"Features settings"}>
        <Flex justify="flex-start" align="center" gap="large" wrap>
          <Form.Item noStyle name={["Accessibility"]} valuePropName="checked">
            <Checkbox>Accessibility</Checkbox>
          </Form.Item>
          <Form.Item noStyle name={["EnableHeading"]} valuePropName="checked">
            <Checkbox>EnableHeading</Checkbox>
          </Form.Item>
        </Flex>
      </Form.Item>
      <Form.Item>
        <Flex justify="flex-start" align="center" gap="large" wrap>
          <Flex justify="flex-start" align="center" gap="large" wrap>
            <Form.Item noStyle name={["SharepriceNumberDecimalDigits"]}>
              <InputNumber style={inputStyle} placeholder="number" min={0} />
            </Form.Item>
            <span>Shareprice Number Decimal Digits</span>
          </Flex>
          <Flex justify="flex-start" align="center" gap="large" wrap>
            <Form.Item noStyle name={["SharePricePercentageDecimalDigits"]}>
              <InputNumber style={inputStyle} placeholder="number" min={0} />
            </Form.Item>
            <span>Share Price Percentage Decimal Digits</span>
          </Flex>
        </Flex>
      </Form.Item>
      <Form.Item>
        <Flex justify="flex-start" align="center" gap="large" wrap>
          <Form.Item
            noStyle
            name={["ShowDataProviderInfo"]}
            valuePropName="checked"
          >
            <Checkbox>Show Data Provider Info</Checkbox>
          </Form.Item>
          <Form.Item
            noStyle
            name={["ShowDataDelayInfo"]}
            valuePropName="checked"
          >
            <Checkbox>Show Data Delay Info</Checkbox>
          </Form.Item>
          <Form.Item
            noStyle
            name={["ShowSupplierInfoLink"]}
            valuePropName="checked"
          >
            <Checkbox>Show Supplier Info Link</Checkbox>
          </Form.Item>
          <Form.Item
            noStyle
            name={["ShowSupplierInfo"]}
            valuePropName="checked"
          >
            <Checkbox>Show Supplier Info</Checkbox>
          </Form.Item>
          <Form.Item
            noStyle
            name={["ShowDisclaimerInfoLink"]}
            valuePropName="checked"
          >
            <Checkbox>Show Disclaimer Info Link</Checkbox>
          </Form.Item>
          <Form.Item
            noStyle
            name={["ShowDisclaimerInfo"]}
            valuePropName="checked"
          >
            <Checkbox>Show Disclaimer Info</Checkbox>
          </Form.Item>
        </Flex>
      </Form.Item>
      <Form.Item name={["DefaultTab"]} label="Default tab">
        <Select style={inputStyle} options={DEFAULT_TAB} />
      </Form.Item>
      <InstrumentConfigs form={form} />
    </>
  );
};
