import { Form, Select } from "antd";
import { useContext, useEffect, useState } from "react";
import { SettingsContext } from "../../context/SettingsContext";

const FormItemSelectFontFamily = ({ name, label = "", className }) => {
  const { optionsFonts } = useContext(SettingsContext);
  return (
    <Form.Item name={name} label={label} className={className}>
      <SelectFontFamily options={optionsFonts} />
    </Form.Item>
  );
};
const SelectFontFamily = ({ options, onChange, value = "" }) => {
  const [fontValue, setFontValue] = useState([]);
  useEffect(() => {
    if (value) {
      const fontValue = [];
      value.split(", ").forEach((v) => {
        const font = options.find((o) => o.label === v);
        if (font) {
          fontValue.push(font.value);
        }
      });
      setFontValue(fontValue);
    }
  }, [value, options]);
  return (
    <Select
      mode="multiple"
      value={fontValue}
      maxTagCount="responsive"
      placeholder="Font family"
      notFoundContent="No font options available"
      options={options}
      onChange={(value) => {
        const fontFamily = value
          .map((v) => {
            const font = options.find((o) => o.value === v);
            return font?.label || "";
          })
          .join(", ");
        setFontValue(value);
        onChange(fontFamily);
      }}
      style={{ width: "100%", marginBottom: 0 }}
    />
  );
};
export { FormItemSelectFontFamily };
