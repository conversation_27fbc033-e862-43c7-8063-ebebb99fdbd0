import {
  __commonJS
} from "./chunk-ZKAWKZG5.js";

// node_modules/__mf__virtual/fragulizer__mf_v__runtimeInit__mf_v__.js
var require_fragulizer_mf_v_runtimeInit_mf_v = __commonJS({
  "node_modules/__mf__virtual/fragulizer__mf_v__runtimeInit__mf_v__.js"(exports, module) {
    var initResolve;
    var initReject;
    var initPromise = new Promise((re, rj) => {
      initResolve = re;
      initReject = rj;
    });
    module.exports = {
      initPromise,
      initResolve,
      initReject
    };
  }
});

export {
  require_fragulizer_mf_v_runtimeInit_mf_v
};
//# sourceMappingURL=chunk-FM5QRY6S.js.map
