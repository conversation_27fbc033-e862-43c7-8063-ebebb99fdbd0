import { Form } from "antd";
import { useContext } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { FormItemInput } from "../FormSettingItem/FormItemInput";
import GroupNameTable from "./GroupNameTable";
import AddGroupButton from "./AddGroupButton";
import InstrumentGroupListing from "./InstrumentGroupListing";

const InstrumentGroups = ({
  instrumentGroups,
  addGroup,
  deleteGroup,
  initializeDefaultGroup,
  getCurrentFormData,
  form,
  onChange,
  instrumentIdDefaults,
}) => {
  const { basicSettings } = useContext(SettingsContext);
  const { availableLanguages = [] } = basicSettings;

  return (
    <div className="instrument-groups">
      <AddGroupButton onAddGroup={addGroup} />

      <GroupNameTable
        form={form}
        onChange={onChange}
        instrumentGroups={instrumentGroups}
        availableLanguages={availableLanguages}
        onDeleteGroup={deleteGroup}
        initializeDefaultGroup={initializeDefaultGroup}
        getCurrentFormData={getCurrentFormData}
      />

      <InstrumentGroupListing
        form={form}
        instrumentGroups={instrumentGroups}
        availableLanguages={availableLanguages}
        instrumentIdDefaults={instrumentIdDefaults}
      />

      <Form.Item name={["InstrumentGroupKeys"]} noStyle>
        <FormItemInput standalone={false} type="hidden" />
      </Form.Item>
    </div>
  );
};

export default InstrumentGroups;
