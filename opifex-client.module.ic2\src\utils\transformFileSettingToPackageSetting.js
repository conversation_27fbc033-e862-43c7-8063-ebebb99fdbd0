import merge from "lodash.merge";
import { convertDotnetFormatToDayjsFormat } from "@euroland/opifix-utils";

export const transformFileSettingToPackageSetting = async ({
  xmlSetting,
  cssSetting,
  generalTools,
  edittingVersion,
  companyCode,
  languageIds,
  defaultSettings,
}) => {
  try {
    const xml = JSON.parse(xmlSetting || "{}");

      const transformKeysToUpperCase = (data) => {
      if (!data || typeof data !== 'object' || Array.isArray(data)) {
        return {};
      }
      return Object.fromEntries(
        Object.entries(data).map(([key, value]) => [key.toUpperCase(), value])
      );
    }

    let toolSetting,
      instrument,
      Format,
      UseLatinNumber,
      timezone,
      dataRefreshRate,
      availableLanguages;

    let raw = xml?.instrumentIds;
    const enabledDividendOption = xml?.enabledDividendOption?.split(",");
    let InstrumentIds = (Array.isArray(raw)
      ? raw
      : typeof raw === "string"
      ? raw.split(",")
      : typeof raw === "number"
      ? [raw.toString()]
      : []).map(item => parseInt(item));

    instrument = InstrumentIds?.reduce((acc, id) => {
      acc[id] = {
        shareName: xml?.customInstrumentName?.[`_${id}`],
        marketName: xml?.customMarketName?.[`_${id}`],
      };
      return acc;
    }, {});
    dataRefreshRate = dataRefreshRate ?? 60;
    UseLatinNumber = xml?.useLatinNumber;
    timezone = xml?.timeZone;
    Format = xml?.format
      ? Object.keys(xml.format).reduce((formats, lang) => {
          formats[lang] = {
            ShortDate: convertDotnetFormatToDayjsFormat(
              xml.format[lang]?.shortDate
            ),
            LongDate: convertDotnetFormatToDayjsFormat(
              xml.format[lang]?.longDate
            ),
            DecimalSeparator:
              xml.format[lang]?.decimalSeparator === " "
                ? "space"
                : xml.format[lang]?.decimalSeparator,
            DecimalDigits: xml.format[lang]?.decimalDigits,
            PercentDigits: xml.format[lang]?.percentDigits,
            ThousandsSeparator:
              xml.format[lang]?.thousandsSeparator === " "
                ? "space"
                : xml.format[lang]?.thousandsSeparator,
            NegativeNumberFormat: xml.format[lang]?.negativeNumberFormat,
            PositiveNumberFormat: xml.format[lang]?.positiveNumberFormat,
            PositiveChangeFormat: xml.format[lang]?.positiveChangeFormat,
          };
          return formats;
        }, {})
      : {};

    availableLanguages = [
      ...new Set(["en-GB", ...Object.keys(Format || {})]),
    ].filter(
      (lang) => Array.isArray(languageIds) && languageIds.includes(lang)
    );

    toolSetting = {
      CustomPhrases: transformKeysToUpperCase(xml?.customPhrases),
      CustomCurrencySign: transformKeysToUpperCase(xml?.customCurrencySign),
      StyleURI: xml?.styleURI,
      CurrencyEnabled: xml?.currencyEnabled ?? false,
      DefaultTab: xml?.defaultTab,
      currencyCodes: Array.isArray(xml?.currencies)
        ? xml?.currencies
        : xml?.currencies?.split(","),
      ChartColors: xml?.chartColors?.split(";"),
      ShowDataProviderInfo: xml?.showdataproviderinfo ?? false,
      ShowDisclaimerInfo: xml?.showdisclaimerinfo ?? false,
      ShowSupplierInfo: xml?.showsupplierinfo ?? false,
      ShowSupplierInfoLink: xml?.showsupplierinfolink ?? false,
      ShowDataDelayInfo: xml?.showDataDelayInfo ?? false,
      TableEvenRowColor: xml?.tableevenrowcolor ?? null,
      TableHeadBackgroundColor: xml?.tableheadbackgroundcolor ?? null,
      TableHeadColor: xml?.tableheadcolor ?? null,
      TableHeadFont: xml?.tableheadfont ?? undefined,
      TableInlineBorderColor: xml?.tableinlinebordercolor ?? null,
      TableInlineBorderEnable: xml?.tableinlineborderenable ?? false,
      TableOddRowColor: xml?.tableoddrowcolor ?? null,
      TableOutlineBorderColor: xml?.tableoutlinebordercolor ?? null,
      TableOutlineBorderEnable: xml?.tableoutlineborderenable ?? false,
      SharePriceValueDecreaseColor: xml?.sharePriceValueDecreaseColor ?? null,
      SharePriceValueIncreaseColor: xml?.sharePriceValueIncreaseColor ?? null,
      ValueColor: xml?.ValueColor ?? null,
      LinkColor: xml?.linkColor ?? null,
      LinkHoverColor: xml?.linkHoverColor ?? null,
      MainHeadingColor: xml?.mainHeadingColor ?? null,
      MainHeadingFont: xml?.mainHeadingFont ?? null,
      MainHeadingFontSize: xml?.mainHeadingFontSize ?? null,
      MainTextColor: xml?.mainTextColor ?? null,
      MainTextFont: xml?.mainTextFont ?? null,
      MainTextFontSize: xml?.mainTextFontSize ?? null,
      InstrumentConfigs: InstrumentIds?.reduce((acc, id) => {
        acc[id] = {
          EnabledDividendOption: enabledDividendOption.includes(String(id)),
          LimitStartingData: xml?.limitStartingData?.[`_${id}`],
          LimitInvestmentStartDate: xml?.limitInvestmentStartDate?.[`_${id}`],
        };
        return acc;
      }, {}),
      customCss: cssSetting,
    };
    toolSetting = merge({}, defaultSettings, toolSetting);

    return JSON.stringify({
      availableTools: {
        companyCode,
        version: edittingVersion?.version,
        selectedTools: [edittingVersion.selectedTools],
      },
      basicSettings: {
        dataRefreshRate,
        availableLanguages,
        UseLatinNumber,
        mainFont: {
          fontFamily: "",
        },
        timezone,
        Format,
        instrumentIds: InstrumentIds,
        instrument,
      },
      tools: {
        generalTools,
        [edittingVersion.selectedTools]: toolSetting,
      },
    });
  } catch (error) {
    console.log("Error:", error);
    return null;
  }
};
