{"name": "opifex-client.module.template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:qa": "env-cmd -f .env.qa npm run-script build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@euroland/opifix-utils": "^1.1.0", "@euroland/react-antd-draggable-select": "^0.0.1", "@monaco-editor/react": "^4.7.0", "antd": "^5.21.4", "lodash.debounce": "^4.0.8", "query-string": "^9.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.80.6"}, "devDependencies": {"@eslint/js": "^9.11.1", "@euroland/vite-plugin-react": "^0.1.0", "@module-federation/vite": "^1.1.3", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.3.3", "env-cmd": "^10.1.0", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "vite": "^5.4.8"}}