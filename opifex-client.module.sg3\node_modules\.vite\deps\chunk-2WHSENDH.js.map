{"version": 3, "sources": ["../../@module-federation/runtime-core/dist/polyfills.cjs.cjs", "../../@module-federation/sdk/dist/polyfills.cjs.cjs", "../../@module-federation/sdk/dist/index.cjs.cjs", "../../@module-federation/error-codes/dist/index.cjs.js", "../../@module-federation/runtime-core/dist/index.cjs.cjs", "../../@module-federation/runtime/dist/utils.cjs.cjs", "../../@module-federation/runtime/dist/index.cjs.cjs"], "sourcesContent": ["'use strict';\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nfunction _object_without_properties_loose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n\nexports._extends = _extends;\nexports._object_without_properties_loose = _object_without_properties_loose;\n", "'use strict';\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nexports._ = _extends;\n", "'use strict';\n\nvar polyfills = require('./polyfills.cjs.cjs');\n\nconst FederationModuleManifest = 'federation-manifest.json';\nconst MANIFEST_EXT = '.json';\nconst BROWSER_LOG_KEY = 'FEDERATION_DEBUG';\nconst NameTransformSymbol = {\n    AT: '@',\n    HYPHEN: '-',\n    SLASH: '/'\n};\nconst NameTransformMap = {\n    [NameTransformSymbol.AT]: 'scope_',\n    [NameTransformSymbol.HYPHEN]: '_',\n    [NameTransformSymbol.SLASH]: '__'\n};\nconst EncodedNameTransformMap = {\n    [NameTransformMap[NameTransformSymbol.AT]]: NameTransformSymbol.AT,\n    [NameTransformMap[NameTransformSymbol.HYPHEN]]: NameTransformSymbol.HYPHEN,\n    [NameTransformMap[NameTransformSymbol.SLASH]]: NameTransformSymbol.SLASH\n};\nconst SEPARATOR = ':';\nconst ManifestFileName = 'mf-manifest.json';\nconst StatsFileName = 'mf-stats.json';\nconst MFModuleType = {\n    NPM: 'npm',\n    APP: 'app'\n};\nconst MODULE_DEVTOOL_IDENTIFIER = '__MF_DEVTOOLS_MODULE_INFO__';\nconst ENCODE_NAME_PREFIX = 'ENCODE_NAME_PREFIX';\nconst TEMP_DIR = '.federation';\nconst MFPrefetchCommon = {\n    identifier: 'MFDataPrefetch',\n    globalKey: '__PREFETCH__',\n    library: 'mf-data-prefetch',\n    exportsKey: '__PREFETCH_EXPORTS__',\n    fileName: 'bootstrap.js'\n};\n\nvar ContainerPlugin = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar ContainerReferencePlugin = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar ModuleFederationPlugin = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar SharePlugin = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nfunction isBrowserEnv() {\n    return typeof window !== 'undefined' && typeof window.document !== 'undefined';\n}\nfunction isReactNativeEnv() {\n    var _navigator;\n    return typeof navigator !== 'undefined' && ((_navigator = navigator) == null ? void 0 : _navigator.product) === 'ReactNative';\n}\nfunction isBrowserDebug() {\n    try {\n        if (isBrowserEnv() && window.localStorage) {\n            return Boolean(localStorage.getItem(BROWSER_LOG_KEY));\n        }\n    } catch (error) {\n        return false;\n    }\n    return false;\n}\nfunction isDebugMode() {\n    if (typeof process !== 'undefined' && process.env && process.env['FEDERATION_DEBUG']) {\n        return Boolean(process.env['FEDERATION_DEBUG']);\n    }\n    if (typeof FEDERATION_DEBUG !== 'undefined' && Boolean(FEDERATION_DEBUG)) {\n        return true;\n    }\n    return isBrowserDebug();\n}\nconst getProcessEnv = function() {\n    return typeof process !== 'undefined' && process.env ? process.env : {};\n};\n\nconst LOG_CATEGORY = '[ Federation Runtime ]';\n// entry: name:version   version : 1.0.0 | ^1.2.3\n// entry: name:entry  entry:  https://localhost:9000/federation-manifest.json\nconst parseEntry = (str, devVerOrUrl, separator = SEPARATOR)=>{\n    const strSplit = str.split(separator);\n    const devVersionOrUrl = getProcessEnv()['NODE_ENV'] === 'development' && devVerOrUrl;\n    const defaultVersion = '*';\n    const isEntry = (s)=>s.startsWith('http') || s.includes(MANIFEST_EXT);\n    // Check if the string starts with a type\n    if (strSplit.length >= 2) {\n        let [name, ...versionOrEntryArr] = strSplit;\n        // @<EMAIL>\n        if (str.startsWith(separator)) {\n            name = strSplit.slice(0, 2).join(separator);\n            versionOrEntryArr = [\n                devVersionOrUrl || strSplit.slice(2).join(separator)\n            ];\n        }\n        let versionOrEntry = devVersionOrUrl || versionOrEntryArr.join(separator);\n        if (isEntry(versionOrEntry)) {\n            return {\n                name,\n                entry: versionOrEntry\n            };\n        } else {\n            // Apply version rule\n            // devVersionOrUrl => inputVersion => defaultVersion\n            return {\n                name,\n                version: versionOrEntry || defaultVersion\n            };\n        }\n    } else if (strSplit.length === 1) {\n        const [name] = strSplit;\n        if (devVersionOrUrl && isEntry(devVersionOrUrl)) {\n            return {\n                name,\n                entry: devVersionOrUrl\n            };\n        }\n        return {\n            name,\n            version: devVersionOrUrl || defaultVersion\n        };\n    } else {\n        throw `Invalid entry value: ${str}`;\n    }\n};\nconst composeKeyWithSeparator = function(...args) {\n    if (!args.length) {\n        return '';\n    }\n    return args.reduce((sum, cur)=>{\n        if (!cur) {\n            return sum;\n        }\n        if (!sum) {\n            return cur;\n        }\n        return `${sum}${SEPARATOR}${cur}`;\n    }, '');\n};\nconst encodeName = function(name, prefix = '', withExt = false) {\n    try {\n        const ext = withExt ? '.js' : '';\n        return `${prefix}${name.replace(new RegExp(`${NameTransformSymbol.AT}`, 'g'), NameTransformMap[NameTransformSymbol.AT]).replace(new RegExp(`${NameTransformSymbol.HYPHEN}`, 'g'), NameTransformMap[NameTransformSymbol.HYPHEN]).replace(new RegExp(`${NameTransformSymbol.SLASH}`, 'g'), NameTransformMap[NameTransformSymbol.SLASH])}${ext}`;\n    } catch (err) {\n        throw err;\n    }\n};\nconst decodeName = function(name, prefix, withExt) {\n    try {\n        let decodedName = name;\n        if (prefix) {\n            if (!decodedName.startsWith(prefix)) {\n                return decodedName;\n            }\n            decodedName = decodedName.replace(new RegExp(prefix, 'g'), '');\n        }\n        decodedName = decodedName.replace(new RegExp(`${NameTransformMap[NameTransformSymbol.AT]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.AT]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.SLASH]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.SLASH]]).replace(new RegExp(`${NameTransformMap[NameTransformSymbol.HYPHEN]}`, 'g'), EncodedNameTransformMap[NameTransformMap[NameTransformSymbol.HYPHEN]]);\n        if (withExt) {\n            decodedName = decodedName.replace('.js', '');\n        }\n        return decodedName;\n    } catch (err) {\n        throw err;\n    }\n};\nconst generateExposeFilename = (exposeName, withExt)=>{\n    if (!exposeName) {\n        return '';\n    }\n    let expose = exposeName;\n    if (expose === '.') {\n        expose = 'default_export';\n    }\n    if (expose.startsWith('./')) {\n        expose = expose.replace('./', '');\n    }\n    return encodeName(expose, '__federation_expose_', withExt);\n};\nconst generateShareFilename = (pkgName, withExt)=>{\n    if (!pkgName) {\n        return '';\n    }\n    return encodeName(pkgName, '__federation_shared_', withExt);\n};\nconst getResourceUrl = (module, sourceUrl)=>{\n    if ('getPublicPath' in module) {\n        let publicPath;\n        if (!module.getPublicPath.startsWith('function')) {\n            publicPath = new Function(module.getPublicPath)();\n        } else {\n            publicPath = new Function('return ' + module.getPublicPath)()();\n        }\n        return `${publicPath}${sourceUrl}`;\n    } else if ('publicPath' in module) {\n        if (!isBrowserEnv() && !isReactNativeEnv() && 'ssrPublicPath' in module) {\n            return `${module.ssrPublicPath}${sourceUrl}`;\n        }\n        return `${module.publicPath}${sourceUrl}`;\n    } else {\n        console.warn('Cannot get resource URL. If in debug mode, please ignore.', module, sourceUrl);\n        return '';\n    }\n};\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nconst assert = (condition, msg)=>{\n    if (!condition) {\n        error(msg);\n    }\n};\nconst error = (msg)=>{\n    throw new Error(`${LOG_CATEGORY}: ${msg}`);\n};\nconst warn = (msg)=>{\n    console.warn(`${LOG_CATEGORY}: ${msg}`);\n};\nfunction safeToString(info) {\n    try {\n        return JSON.stringify(info, null, 2);\n    } catch (e) {\n        return '';\n    }\n}\n// RegExp for version string\nconst VERSION_PATTERN_REGEXP = /^([\\d^=v<>~]|[*xX]$)/;\nfunction isRequiredVersion(str) {\n    return VERSION_PATTERN_REGEXP.test(str);\n}\n\nconst simpleJoinRemoteEntry = (rPath, rName)=>{\n    if (!rPath) {\n        return rName;\n    }\n    const transformPath = (str)=>{\n        if (str === '.') {\n            return '';\n        }\n        if (str.startsWith('./')) {\n            return str.replace('./', '');\n        }\n        if (str.startsWith('/')) {\n            const strWithoutSlash = str.slice(1);\n            if (strWithoutSlash.endsWith('/')) {\n                return strWithoutSlash.slice(0, -1);\n            }\n            return strWithoutSlash;\n        }\n        return str;\n    };\n    const transformedPath = transformPath(rPath);\n    if (!transformedPath) {\n        return rName;\n    }\n    if (transformedPath.endsWith('/')) {\n        return `${transformedPath}${rName}`;\n    }\n    return `${transformedPath}/${rName}`;\n};\nfunction inferAutoPublicPath(url) {\n    return url.replace(/#.*$/, '').replace(/\\?.*$/, '').replace(/\\/[^\\/]+$/, '/');\n}\n// Priority: overrides > remotes\n// eslint-disable-next-line max-lines-per-function\nfunction generateSnapshotFromManifest(manifest, options = {}) {\n    var _manifest_metaData, _manifest_metaData1;\n    const { remotes = {}, overrides = {}, version } = options;\n    let remoteSnapshot;\n    const getPublicPath = ()=>{\n        if ('publicPath' in manifest.metaData) {\n            if (manifest.metaData.publicPath === 'auto' && version) {\n                // use same implementation as publicPath auto runtime module implements\n                return inferAutoPublicPath(version);\n            }\n            return manifest.metaData.publicPath;\n        } else {\n            return manifest.metaData.getPublicPath;\n        }\n    };\n    const overridesKeys = Object.keys(overrides);\n    let remotesInfo = {};\n    // If remotes are not provided, only the remotes in the manifest will be read\n    if (!Object.keys(remotes).length) {\n        var _manifest_remotes;\n        remotesInfo = ((_manifest_remotes = manifest.remotes) == null ? void 0 : _manifest_remotes.reduce((res, next)=>{\n            let matchedVersion;\n            const name = next.federationContainerName;\n            // overrides have higher priority\n            if (overridesKeys.includes(name)) {\n                matchedVersion = overrides[name];\n            } else {\n                if ('version' in next) {\n                    matchedVersion = next.version;\n                } else {\n                    matchedVersion = next.entry;\n                }\n            }\n            res[name] = {\n                matchedVersion\n            };\n            return res;\n        }, {})) || {};\n    }\n    // If remotes (deploy scenario) are specified, they need to be traversed again\n    Object.keys(remotes).forEach((key)=>remotesInfo[key] = {\n            // overrides will override dependencies\n            matchedVersion: overridesKeys.includes(key) ? overrides[key] : remotes[key]\n        });\n    const { remoteEntry: { path: remoteEntryPath, name: remoteEntryName, type: remoteEntryType }, types: remoteTypes, buildInfo: { buildVersion }, globalName, ssrRemoteEntry } = manifest.metaData;\n    const { exposes } = manifest;\n    let basicRemoteSnapshot = {\n        version: version ? version : '',\n        buildVersion,\n        globalName,\n        remoteEntry: simpleJoinRemoteEntry(remoteEntryPath, remoteEntryName),\n        remoteEntryType,\n        remoteTypes: simpleJoinRemoteEntry(remoteTypes.path, remoteTypes.name),\n        remoteTypesZip: remoteTypes.zip || '',\n        remoteTypesAPI: remoteTypes.api || '',\n        remotesInfo,\n        shared: manifest == null ? void 0 : manifest.shared.map((item)=>({\n                assets: item.assets,\n                sharedName: item.name,\n                version: item.version\n            })),\n        modules: exposes == null ? void 0 : exposes.map((expose)=>({\n                moduleName: expose.name,\n                modulePath: expose.path,\n                assets: expose.assets\n            }))\n    };\n    if ((_manifest_metaData = manifest.metaData) == null ? void 0 : _manifest_metaData.prefetchInterface) {\n        const prefetchInterface = manifest.metaData.prefetchInterface;\n        basicRemoteSnapshot = polyfills._({}, basicRemoteSnapshot, {\n            prefetchInterface\n        });\n    }\n    if ((_manifest_metaData1 = manifest.metaData) == null ? void 0 : _manifest_metaData1.prefetchEntry) {\n        const { path, name, type } = manifest.metaData.prefetchEntry;\n        basicRemoteSnapshot = polyfills._({}, basicRemoteSnapshot, {\n            prefetchEntry: simpleJoinRemoteEntry(path, name),\n            prefetchEntryType: type\n        });\n    }\n    if ('publicPath' in manifest.metaData) {\n        remoteSnapshot = polyfills._({}, basicRemoteSnapshot, {\n            publicPath: getPublicPath(),\n            ssrPublicPath: manifest.metaData.ssrPublicPath\n        });\n    } else {\n        remoteSnapshot = polyfills._({}, basicRemoteSnapshot, {\n            getPublicPath: getPublicPath()\n        });\n    }\n    if (ssrRemoteEntry) {\n        const fullSSRRemoteEntry = simpleJoinRemoteEntry(ssrRemoteEntry.path, ssrRemoteEntry.name);\n        remoteSnapshot.ssrRemoteEntry = fullSSRRemoteEntry;\n        remoteSnapshot.ssrRemoteEntryType = ssrRemoteEntry.type || 'commonjs-module';\n    }\n    return remoteSnapshot;\n}\nfunction isManifestProvider(moduleInfo) {\n    if ('remoteEntry' in moduleInfo && moduleInfo.remoteEntry.includes(MANIFEST_EXT)) {\n        return true;\n    } else {\n        return false;\n    }\n}\n\nconst PREFIX = '[ Module Federation ]';\nlet Logger = class Logger {\n    setPrefix(prefix) {\n        this.prefix = prefix;\n    }\n    log(...args) {\n        console.log(this.prefix, ...args);\n    }\n    warn(...args) {\n        console.log(this.prefix, ...args);\n    }\n    error(...args) {\n        console.log(this.prefix, ...args);\n    }\n    success(...args) {\n        console.log(this.prefix, ...args);\n    }\n    info(...args) {\n        console.log(this.prefix, ...args);\n    }\n    ready(...args) {\n        console.log(this.prefix, ...args);\n    }\n    debug(...args) {\n        if (isDebugMode()) {\n            console.log(this.prefix, ...args);\n        }\n    }\n    constructor(prefix){\n        this.prefix = prefix;\n    }\n};\nfunction createLogger(prefix) {\n    return new Logger(prefix);\n}\nconst logger = createLogger(PREFIX);\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function safeWrapper(callback, disableWarn) {\n    try {\n        const res = await callback();\n        return res;\n    } catch (e) {\n        !disableWarn && warn(e);\n        return;\n    }\n}\nfunction isStaticResourcesEqual(url1, url2) {\n    const REG_EXP = /^(https?:)?\\/\\//i;\n    // Transform url1 and url2 into relative paths\n    const relativeUrl1 = url1.replace(REG_EXP, '').replace(/\\/$/, '');\n    const relativeUrl2 = url2.replace(REG_EXP, '').replace(/\\/$/, '');\n    // Check if the relative paths are identical\n    return relativeUrl1 === relativeUrl2;\n}\nfunction createScript(info) {\n    // Retrieve the existing script element by its src attribute\n    let script = null;\n    let needAttach = true;\n    let timeout = 20000;\n    let timeoutId;\n    const scripts = document.getElementsByTagName('script');\n    for(let i = 0; i < scripts.length; i++){\n        const s = scripts[i];\n        const scriptSrc = s.getAttribute('src');\n        if (scriptSrc && isStaticResourcesEqual(scriptSrc, info.url)) {\n            script = s;\n            needAttach = false;\n            break;\n        }\n    }\n    if (!script) {\n        const attrs = info.attrs;\n        script = document.createElement('script');\n        script.type = (attrs == null ? void 0 : attrs['type']) === 'module' ? 'module' : 'text/javascript';\n        let createScriptRes = undefined;\n        if (info.createScriptHook) {\n            createScriptRes = info.createScriptHook(info.url, info.attrs);\n            if (createScriptRes instanceof HTMLScriptElement) {\n                script = createScriptRes;\n            } else if (typeof createScriptRes === 'object') {\n                if ('script' in createScriptRes && createScriptRes.script) {\n                    script = createScriptRes.script;\n                }\n                if ('timeout' in createScriptRes && createScriptRes.timeout) {\n                    timeout = createScriptRes.timeout;\n                }\n            }\n        }\n        if (!script.src) {\n            script.src = info.url;\n        }\n        if (attrs && !createScriptRes) {\n            Object.keys(attrs).forEach((name)=>{\n                if (script) {\n                    if (name === 'async' || name === 'defer') {\n                        script[name] = attrs[name];\n                    // Attributes that do not exist are considered overridden\n                    } else if (!script.getAttribute(name)) {\n                        script.setAttribute(name, attrs[name]);\n                    }\n                }\n            });\n        }\n    }\n    const onScriptComplete = async (prev, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    event)=>{\n        clearTimeout(timeoutId);\n        const onScriptCompleteCallback = ()=>{\n            if ((event == null ? void 0 : event.type) === 'error') {\n                (info == null ? void 0 : info.onErrorCallback) && (info == null ? void 0 : info.onErrorCallback(event));\n            } else {\n                (info == null ? void 0 : info.cb) && (info == null ? void 0 : info.cb());\n            }\n        };\n        // Prevent memory leaks in IE.\n        if (script) {\n            script.onerror = null;\n            script.onload = null;\n            safeWrapper(()=>{\n                const { needDeleteScript = true } = info;\n                if (needDeleteScript) {\n                    (script == null ? void 0 : script.parentNode) && script.parentNode.removeChild(script);\n                }\n            });\n            if (prev && typeof prev === 'function') {\n                const result = prev(event);\n                if (result instanceof Promise) {\n                    const res = await result;\n                    onScriptCompleteCallback();\n                    return res;\n                }\n                onScriptCompleteCallback();\n                return result;\n            }\n        }\n        onScriptCompleteCallback();\n    };\n    script.onerror = onScriptComplete.bind(null, script.onerror);\n    script.onload = onScriptComplete.bind(null, script.onload);\n    timeoutId = setTimeout(()=>{\n        onScriptComplete(null, new Error(`Remote script \"${info.url}\" time-outed.`));\n    }, timeout);\n    return {\n        script,\n        needAttach\n    };\n}\nfunction createLink(info) {\n    // <link rel=\"preload\" href=\"script.js\" as=\"script\">\n    // Retrieve the existing script element by its src attribute\n    let link = null;\n    let needAttach = true;\n    const links = document.getElementsByTagName('link');\n    for(let i = 0; i < links.length; i++){\n        const l = links[i];\n        const linkHref = l.getAttribute('href');\n        const linkRel = l.getAttribute('rel');\n        if (linkHref && isStaticResourcesEqual(linkHref, info.url) && linkRel === info.attrs['rel']) {\n            link = l;\n            needAttach = false;\n            break;\n        }\n    }\n    if (!link) {\n        link = document.createElement('link');\n        link.setAttribute('href', info.url);\n        let createLinkRes = undefined;\n        const attrs = info.attrs;\n        if (info.createLinkHook) {\n            createLinkRes = info.createLinkHook(info.url, attrs);\n            if (createLinkRes instanceof HTMLLinkElement) {\n                link = createLinkRes;\n            }\n        }\n        if (attrs && !createLinkRes) {\n            Object.keys(attrs).forEach((name)=>{\n                if (link && !link.getAttribute(name)) {\n                    link.setAttribute(name, attrs[name]);\n                }\n            });\n        }\n    }\n    const onLinkComplete = (prev, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    event)=>{\n        const onLinkCompleteCallback = ()=>{\n            if ((event == null ? void 0 : event.type) === 'error') {\n                (info == null ? void 0 : info.onErrorCallback) && (info == null ? void 0 : info.onErrorCallback(event));\n            } else {\n                (info == null ? void 0 : info.cb) && (info == null ? void 0 : info.cb());\n            }\n        };\n        // Prevent memory leaks in IE.\n        if (link) {\n            link.onerror = null;\n            link.onload = null;\n            safeWrapper(()=>{\n                const { needDeleteLink = true } = info;\n                if (needDeleteLink) {\n                    (link == null ? void 0 : link.parentNode) && link.parentNode.removeChild(link);\n                }\n            });\n            if (prev) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const res = prev(event);\n                onLinkCompleteCallback();\n                return res;\n            }\n        }\n        onLinkCompleteCallback();\n    };\n    link.onerror = onLinkComplete.bind(null, link.onerror);\n    link.onload = onLinkComplete.bind(null, link.onload);\n    return {\n        link,\n        needAttach\n    };\n}\nfunction loadScript(url, info) {\n    const { attrs = {}, createScriptHook } = info;\n    return new Promise((resolve, reject)=>{\n        const { script, needAttach } = createScript({\n            url,\n            cb: resolve,\n            onErrorCallback: reject,\n            attrs: polyfills._({\n                fetchpriority: 'high'\n            }, attrs),\n            createScriptHook,\n            needDeleteScript: true\n        });\n        needAttach && document.head.appendChild(script);\n    });\n}\n\nfunction importNodeModule(name) {\n    if (!name) {\n        throw new Error('import specifier is required');\n    }\n    const importModule = new Function('name', `return import(name)`);\n    return importModule(name).then((res)=>res).catch((error)=>{\n        console.error(`Error importing module ${name}:`, error);\n        throw error;\n    });\n}\nconst loadNodeFetch = async ()=>{\n    const fetchModule = await importNodeModule('node-fetch');\n    return fetchModule.default || fetchModule;\n};\nconst lazyLoaderHookFetch = async (input, init, loaderHook)=>{\n    const hook = (url, init)=>{\n        return loaderHook.lifecycle.fetch.emit(url, init);\n    };\n    const res = await hook(input, init || {});\n    if (!res || !(res instanceof Response)) {\n        const fetchFunction = typeof fetch === 'undefined' ? await loadNodeFetch() : fetch;\n        return fetchFunction(input, init || {});\n    }\n    return res;\n};\nconst createScriptNode = typeof ENV_TARGET === 'undefined' || ENV_TARGET !== 'web' ? (url, cb, attrs, loaderHook)=>{\n    if (loaderHook == null ? void 0 : loaderHook.createScriptHook) {\n        const hookResult = loaderHook.createScriptHook(url);\n        if (hookResult && typeof hookResult === 'object' && 'url' in hookResult) {\n            url = hookResult.url;\n        }\n    }\n    let urlObj;\n    try {\n        urlObj = new URL(url);\n    } catch (e) {\n        console.error('Error constructing URL:', e);\n        cb(new Error(`Invalid URL: ${e}`));\n        return;\n    }\n    const getFetch = async ()=>{\n        if (loaderHook == null ? void 0 : loaderHook.fetch) {\n            return (input, init)=>lazyLoaderHookFetch(input, init, loaderHook);\n        }\n        return typeof fetch === 'undefined' ? loadNodeFetch() : fetch;\n    };\n    const handleScriptFetch = async (f, urlObj)=>{\n        try {\n            var //@ts-ignore\n            _vm_constants;\n            const res = await f(urlObj.href);\n            const data = await res.text();\n            const [path, vm] = await Promise.all([\n                importNodeModule('path'),\n                importNodeModule('vm')\n            ]);\n            const scriptContext = {\n                exports: {},\n                module: {\n                    exports: {}\n                }\n            };\n            const urlDirname = urlObj.pathname.split('/').slice(0, -1).join('/');\n            const filename = path.basename(urlObj.pathname);\n            var _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER;\n            const script = new vm.Script(`(function(exports, module, require, __dirname, __filename) {${data}\\n})`, {\n                filename,\n                importModuleDynamically: (_vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER = (_vm_constants = vm.constants) == null ? void 0 : _vm_constants.USE_MAIN_CONTEXT_DEFAULT_LOADER) != null ? _vm_constants_USE_MAIN_CONTEXT_DEFAULT_LOADER : importNodeModule\n            });\n            script.runInThisContext()(scriptContext.exports, scriptContext.module, eval('require'), urlDirname, filename);\n            const exportedInterface = scriptContext.module.exports || scriptContext.exports;\n            if (attrs && exportedInterface && attrs['globalName']) {\n                const container = exportedInterface[attrs['globalName']] || exportedInterface;\n                cb(undefined, container);\n                return;\n            }\n            cb(undefined, exportedInterface);\n        } catch (e) {\n            cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));\n        }\n    };\n    getFetch().then(async (f)=>{\n        if ((attrs == null ? void 0 : attrs['type']) === 'esm' || (attrs == null ? void 0 : attrs['type']) === 'module') {\n            return loadModule(urlObj.href, {\n                fetch: f,\n                vm: await importNodeModule('vm')\n            }).then(async (module)=>{\n                await module.evaluate();\n                cb(undefined, module.namespace);\n            }).catch((e)=>{\n                cb(e instanceof Error ? e : new Error(`Script execution error: ${e}`));\n            });\n        }\n        handleScriptFetch(f, urlObj);\n    }).catch((err)=>{\n        cb(err);\n    });\n} : (url, cb, attrs, loaderHook)=>{\n    cb(new Error('createScriptNode is disabled in non-Node.js environment'));\n};\nconst loadScriptNode = typeof ENV_TARGET === 'undefined' || ENV_TARGET !== 'web' ? (url, info)=>{\n    return new Promise((resolve, reject)=>{\n        createScriptNode(url, (error, scriptContext)=>{\n            if (error) {\n                reject(error);\n            } else {\n                var _info_attrs, _info_attrs1;\n                const remoteEntryKey = (info == null ? void 0 : (_info_attrs = info.attrs) == null ? void 0 : _info_attrs['globalName']) || `__FEDERATION_${info == null ? void 0 : (_info_attrs1 = info.attrs) == null ? void 0 : _info_attrs1['name']}:custom__`;\n                const entryExports = globalThis[remoteEntryKey] = scriptContext;\n                resolve(entryExports);\n            }\n        }, info.attrs, info.loaderHook);\n    });\n} : (url, info)=>{\n    throw new Error('loadScriptNode is disabled in non-Node.js environment');\n};\nasync function loadModule(url, options) {\n    const { fetch: fetch1, vm } = options;\n    const response = await fetch1(url);\n    const code = await response.text();\n    const module = new vm.SourceTextModule(code, {\n        // @ts-ignore\n        importModuleDynamically: async (specifier, script)=>{\n            const resolvedUrl = new URL(specifier, url).href;\n            return loadModule(resolvedUrl, options);\n        }\n    });\n    await module.link(async (specifier)=>{\n        const resolvedUrl = new URL(specifier, url).href;\n        const module = await loadModule(resolvedUrl, options);\n        return module;\n    });\n    return module;\n}\n\nfunction normalizeOptions(enableDefault, defaultOptions, key) {\n    return function(options) {\n        if (options === false) {\n            return false;\n        }\n        if (typeof options === 'undefined') {\n            if (enableDefault) {\n                return defaultOptions;\n            } else {\n                return false;\n            }\n        }\n        if (options === true) {\n            return defaultOptions;\n        }\n        if (options && typeof options === 'object') {\n            return polyfills._({}, defaultOptions, options);\n        }\n        throw new Error(`Unexpected type for \\`${key}\\`, expect boolean/undefined/object, got: ${typeof options}`);\n    };\n}\n\nconst createModuleFederationConfig = (options)=>{\n    return options;\n};\n\nexports.BROWSER_LOG_KEY = BROWSER_LOG_KEY;\nexports.ENCODE_NAME_PREFIX = ENCODE_NAME_PREFIX;\nexports.EncodedNameTransformMap = EncodedNameTransformMap;\nexports.FederationModuleManifest = FederationModuleManifest;\nexports.MANIFEST_EXT = MANIFEST_EXT;\nexports.MFModuleType = MFModuleType;\nexports.MFPrefetchCommon = MFPrefetchCommon;\nexports.MODULE_DEVTOOL_IDENTIFIER = MODULE_DEVTOOL_IDENTIFIER;\nexports.ManifestFileName = ManifestFileName;\nexports.NameTransformMap = NameTransformMap;\nexports.NameTransformSymbol = NameTransformSymbol;\nexports.SEPARATOR = SEPARATOR;\nexports.StatsFileName = StatsFileName;\nexports.TEMP_DIR = TEMP_DIR;\nexports.assert = assert;\nexports.composeKeyWithSeparator = composeKeyWithSeparator;\nexports.containerPlugin = ContainerPlugin;\nexports.containerReferencePlugin = ContainerReferencePlugin;\nexports.createLink = createLink;\nexports.createLogger = createLogger;\nexports.createModuleFederationConfig = createModuleFederationConfig;\nexports.createScript = createScript;\nexports.createScriptNode = createScriptNode;\nexports.decodeName = decodeName;\nexports.encodeName = encodeName;\nexports.error = error;\nexports.generateExposeFilename = generateExposeFilename;\nexports.generateShareFilename = generateShareFilename;\nexports.generateSnapshotFromManifest = generateSnapshotFromManifest;\nexports.getProcessEnv = getProcessEnv;\nexports.getResourceUrl = getResourceUrl;\nexports.inferAutoPublicPath = inferAutoPublicPath;\nexports.isBrowserEnv = isBrowserEnv;\nexports.isDebugMode = isDebugMode;\nexports.isManifestProvider = isManifestProvider;\nexports.isReactNativeEnv = isReactNativeEnv;\nexports.isRequiredVersion = isRequiredVersion;\nexports.isStaticResourcesEqual = isStaticResourcesEqual;\nexports.loadScript = loadScript;\nexports.loadScriptNode = loadScriptNode;\nexports.logger = logger;\nexports.moduleFederationPlugin = ModuleFederationPlugin;\nexports.normalizeOptions = normalizeOptions;\nexports.parseEntry = parseEntry;\nexports.safeToString = safeToString;\nexports.safeWrapper = safeWrapper;\nexports.sharePlugin = SharePlugin;\nexports.simpleJoinRemoteEntry = simpleJoinRemoteEntry;\nexports.warn = warn;\n", "'use strict';\n\nconst RUNTIME_001 = 'RUNTIME-001';\nconst RUNTIME_002 = 'RUNTIME-002';\nconst RUNTIME_003 = 'RUNTIME-003';\nconst RUNTIME_004 = 'RUNTIME-004';\nconst RUNTIME_005 = 'RUNTIME-005';\nconst RUNTIME_006 = 'RUNTIME-006';\nconst RUNTIME_007 = 'RUNTIME-007';\nconst RUNTIME_008 = 'RUNTIME-008';\nconst RUNTIME_009 = 'RUNTIME-009';\nconst TYPE_001 = 'TYPE-001';\nconst BUILD_001 = 'BUILD-001';\nconst BUILD_002 = 'BUILD-002';\n\nconst getDocsUrl = (errorCode)=>{\n    const type = errorCode.split('-')[0].toLowerCase();\n    return `View the docs to see how to solve: https://module-federation.io/guide/troubleshooting/${type}/${errorCode}`;\n};\nconst getShortErrorMsg = (errorCode, errorDescMap, args, originalErrorMsg)=>{\n    const msg = [\n        `${[\n            errorDescMap[errorCode]\n        ]} #${errorCode}`\n    ];\n    args && msg.push(`args: ${JSON.stringify(args)}`);\n    msg.push(getDocsUrl(errorCode));\n    originalErrorMsg && msg.push(`Original Error Message:\\n ${originalErrorMsg}`);\n    return msg.join('\\n');\n};\n\nfunction _extends() {\n    _extends = Object.assign || function assign(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n\nconst runtimeDescMap = {\n    [RUNTIME_001]: 'Failed to get remoteEntry exports.',\n    [RUNTIME_002]: 'The remote entry interface does not contain \"init\"',\n    [RUNTIME_003]: 'Failed to get manifest.',\n    [RUNTIME_004]: 'Failed to locate remote.',\n    [RUNTIME_005]: 'Invalid loadShareSync function call from bundler runtime',\n    [RUNTIME_006]: 'Invalid loadShareSync function call from runtime',\n    [RUNTIME_007]: 'Failed to get remote snapshot.',\n    [RUNTIME_008]: 'Failed to load script resources.',\n    [RUNTIME_009]: 'Please call createInstance first.'\n};\nconst typeDescMap = {\n    [TYPE_001]: 'Failed to generate type declaration. Execute the below cmd to reproduce and fix the error.'\n};\nconst buildDescMap = {\n    [BUILD_001]: 'Failed to find expose module.',\n    [BUILD_002]: 'PublicPath is required in prod mode.'\n};\nconst errorDescMap = _extends({}, runtimeDescMap, typeDescMap, buildDescMap);\n\nexports.BUILD_001 = BUILD_001;\nexports.BUILD_002 = BUILD_002;\nexports.RUNTIME_001 = RUNTIME_001;\nexports.RUNTIME_002 = RUNTIME_002;\nexports.RUNTIME_003 = RUNTIME_003;\nexports.RUNTIME_004 = RUNTIME_004;\nexports.RUNTIME_005 = RUNTIME_005;\nexports.RUNTIME_006 = RUNTIME_006;\nexports.RUNTIME_007 = RUNTIME_007;\nexports.RUNTIME_008 = RUNTIME_008;\nexports.RUNTIME_009 = RUNTIME_009;\nexports.TYPE_001 = TYPE_001;\nexports.buildDescMap = buildDescMap;\nexports.errorDescMap = errorDescMap;\nexports.getShortErrorMsg = getShortErrorMsg;\nexports.runtimeDescMap = runtimeDescMap;\nexports.typeDescMap = typeDescMap;\n", "'use strict';\n\nvar polyfills = require('./polyfills.cjs.cjs');\nvar sdk = require('@module-federation/sdk');\nvar errorCodes = require('@module-federation/error-codes');\n\nconst LOG_CATEGORY = '[ Federation Runtime ]';\n// FIXME: pre-bundle ?\nconst logger = sdk.createLogger(LOG_CATEGORY);\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction assert(condition, msg) {\n    if (!condition) {\n        error(msg);\n    }\n}\nfunction error(msg) {\n    if (msg instanceof Error) {\n        // Check if the message already starts with the log category to avoid duplication\n        if (!msg.message.startsWith(LOG_CATEGORY)) {\n            msg.message = `${LOG_CATEGORY}: ${msg.message}`;\n        }\n        throw msg;\n    }\n    throw new Error(`${LOG_CATEGORY}: ${msg}`);\n}\nfunction warn(msg) {\n    if (msg instanceof Error) {\n        // Check if the message already starts with the log category to avoid duplication\n        if (!msg.message.startsWith(LOG_CATEGORY)) {\n            msg.message = `${LOG_CATEGORY}: ${msg.message}`;\n        }\n        logger.warn(msg);\n    } else {\n        logger.warn(msg);\n    }\n}\n\nfunction addUniqueItem(arr, item) {\n    if (arr.findIndex((name)=>name === item) === -1) {\n        arr.push(item);\n    }\n    return arr;\n}\nfunction getFMId(remoteInfo) {\n    if ('version' in remoteInfo && remoteInfo.version) {\n        return `${remoteInfo.name}:${remoteInfo.version}`;\n    } else if ('entry' in remoteInfo && remoteInfo.entry) {\n        return `${remoteInfo.name}:${remoteInfo.entry}`;\n    } else {\n        return `${remoteInfo.name}`;\n    }\n}\nfunction isRemoteInfoWithEntry(remote) {\n    return typeof remote.entry !== 'undefined';\n}\nfunction isPureRemoteEntry(remote) {\n    return !remote.entry.includes('.json');\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function safeWrapper(callback, disableWarn) {\n    try {\n        const res = await callback();\n        return res;\n    } catch (e) {\n        !disableWarn && warn(e);\n        return;\n    }\n}\nfunction isObject(val) {\n    return val && typeof val === 'object';\n}\nconst objectToString = Object.prototype.toString;\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isPlainObject(val) {\n    return objectToString.call(val) === '[object Object]';\n}\nfunction isStaticResourcesEqual(url1, url2) {\n    const REG_EXP = /^(https?:)?\\/\\//i;\n    // Transform url1 and url2 into relative paths\n    const relativeUrl1 = url1.replace(REG_EXP, '').replace(/\\/$/, '');\n    const relativeUrl2 = url2.replace(REG_EXP, '').replace(/\\/$/, '');\n    // Check if the relative paths are identical\n    return relativeUrl1 === relativeUrl2;\n}\nfunction arrayOptions(options) {\n    return Array.isArray(options) ? options : [\n        options\n    ];\n}\nfunction getRemoteEntryInfoFromSnapshot(snapshot) {\n    const defaultRemoteEntryInfo = {\n        url: '',\n        type: 'global',\n        globalName: ''\n    };\n    if (sdk.isBrowserEnv() || sdk.isReactNativeEnv()) {\n        return 'remoteEntry' in snapshot ? {\n            url: snapshot.remoteEntry,\n            type: snapshot.remoteEntryType,\n            globalName: snapshot.globalName\n        } : defaultRemoteEntryInfo;\n    }\n    if ('ssrRemoteEntry' in snapshot) {\n        return {\n            url: snapshot.ssrRemoteEntry || defaultRemoteEntryInfo.url,\n            type: snapshot.ssrRemoteEntryType || defaultRemoteEntryInfo.type,\n            globalName: snapshot.globalName\n        };\n    }\n    return defaultRemoteEntryInfo;\n}\nconst processModuleAlias = (name, subPath)=>{\n    // @host/ ./button -> @host/button\n    let moduleName;\n    if (name.endsWith('/')) {\n        moduleName = name.slice(0, -1);\n    } else {\n        moduleName = name;\n    }\n    if (subPath.startsWith('.')) {\n        subPath = subPath.slice(1);\n    }\n    moduleName = moduleName + subPath;\n    return moduleName;\n};\n\nconst CurrentGlobal = typeof globalThis === 'object' ? globalThis : window;\nconst nativeGlobal = (()=>{\n    try {\n        // get real window (incase of sandbox)\n        return document.defaultView;\n    } catch (e) {\n        // node env\n        return CurrentGlobal;\n    }\n})();\nconst Global = nativeGlobal;\nfunction definePropertyGlobalVal(target, key, val) {\n    Object.defineProperty(target, key, {\n        value: val,\n        configurable: false,\n        writable: true\n    });\n}\nfunction includeOwnProperty(target, key) {\n    return Object.hasOwnProperty.call(target, key);\n}\n// This section is to prevent encapsulation by certain microfrontend frameworks. Due to reuse policies, sandbox escapes.\n// The sandbox in the microfrontend does not replicate the value of 'configurable'.\n// If there is no loading content on the global object, this section defines the loading object.\nif (!includeOwnProperty(CurrentGlobal, '__GLOBAL_LOADING_REMOTE_ENTRY__')) {\n    definePropertyGlobalVal(CurrentGlobal, '__GLOBAL_LOADING_REMOTE_ENTRY__', {});\n}\nconst globalLoading = CurrentGlobal.__GLOBAL_LOADING_REMOTE_ENTRY__;\nfunction setGlobalDefaultVal(target) {\n    var _target___FEDERATION__, _target___FEDERATION__1, _target___FEDERATION__2, _target___FEDERATION__3, _target___FEDERATION__4, _target___FEDERATION__5;\n    if (includeOwnProperty(target, '__VMOK__') && !includeOwnProperty(target, '__FEDERATION__')) {\n        definePropertyGlobalVal(target, '__FEDERATION__', target.__VMOK__);\n    }\n    if (!includeOwnProperty(target, '__FEDERATION__')) {\n        definePropertyGlobalVal(target, '__FEDERATION__', {\n            __GLOBAL_PLUGIN__: [],\n            __INSTANCES__: [],\n            moduleInfo: {},\n            __SHARE__: {},\n            __MANIFEST_LOADING__: {},\n            __PRELOADED_MAP__: new Map()\n        });\n        definePropertyGlobalVal(target, '__VMOK__', target.__FEDERATION__);\n    }\n    var ___GLOBAL_PLUGIN__;\n    (___GLOBAL_PLUGIN__ = (_target___FEDERATION__ = target.__FEDERATION__).__GLOBAL_PLUGIN__) != null ? ___GLOBAL_PLUGIN__ : _target___FEDERATION__.__GLOBAL_PLUGIN__ = [];\n    var ___INSTANCES__;\n    (___INSTANCES__ = (_target___FEDERATION__1 = target.__FEDERATION__).__INSTANCES__) != null ? ___INSTANCES__ : _target___FEDERATION__1.__INSTANCES__ = [];\n    var _moduleInfo;\n    (_moduleInfo = (_target___FEDERATION__2 = target.__FEDERATION__).moduleInfo) != null ? _moduleInfo : _target___FEDERATION__2.moduleInfo = {};\n    var ___SHARE__;\n    (___SHARE__ = (_target___FEDERATION__3 = target.__FEDERATION__).__SHARE__) != null ? ___SHARE__ : _target___FEDERATION__3.__SHARE__ = {};\n    var ___MANIFEST_LOADING__;\n    (___MANIFEST_LOADING__ = (_target___FEDERATION__4 = target.__FEDERATION__).__MANIFEST_LOADING__) != null ? ___MANIFEST_LOADING__ : _target___FEDERATION__4.__MANIFEST_LOADING__ = {};\n    var ___PRELOADED_MAP__;\n    (___PRELOADED_MAP__ = (_target___FEDERATION__5 = target.__FEDERATION__).__PRELOADED_MAP__) != null ? ___PRELOADED_MAP__ : _target___FEDERATION__5.__PRELOADED_MAP__ = new Map();\n}\nsetGlobalDefaultVal(CurrentGlobal);\nsetGlobalDefaultVal(nativeGlobal);\nfunction resetFederationGlobalInfo() {\n    CurrentGlobal.__FEDERATION__.__GLOBAL_PLUGIN__ = [];\n    CurrentGlobal.__FEDERATION__.__INSTANCES__ = [];\n    CurrentGlobal.__FEDERATION__.moduleInfo = {};\n    CurrentGlobal.__FEDERATION__.__SHARE__ = {};\n    CurrentGlobal.__FEDERATION__.__MANIFEST_LOADING__ = {};\n    Object.keys(globalLoading).forEach((key)=>{\n        delete globalLoading[key];\n    });\n}\nfunction setGlobalFederationInstance(FederationInstance) {\n    CurrentGlobal.__FEDERATION__.__INSTANCES__.push(FederationInstance);\n}\nfunction getGlobalFederationConstructor() {\n    return CurrentGlobal.__FEDERATION__.__DEBUG_CONSTRUCTOR__;\n}\nfunction setGlobalFederationConstructor(FederationConstructor, isDebug = sdk.isDebugMode()) {\n    if (isDebug) {\n        CurrentGlobal.__FEDERATION__.__DEBUG_CONSTRUCTOR__ = FederationConstructor;\n        CurrentGlobal.__FEDERATION__.__DEBUG_CONSTRUCTOR_VERSION__ = \"0.17.1\";\n    }\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction getInfoWithoutType(target, key) {\n    if (typeof key === 'string') {\n        const keyRes = target[key];\n        if (keyRes) {\n            return {\n                value: target[key],\n                key: key\n            };\n        } else {\n            const targetKeys = Object.keys(target);\n            for (const targetKey of targetKeys){\n                const [targetTypeOrName, _] = targetKey.split(':');\n                const nKey = `${targetTypeOrName}:${key}`;\n                const typeWithKeyRes = target[nKey];\n                if (typeWithKeyRes) {\n                    return {\n                        value: typeWithKeyRes,\n                        key: nKey\n                    };\n                }\n            }\n            return {\n                value: undefined,\n                key: key\n            };\n        }\n    } else {\n        throw new Error('key must be string');\n    }\n}\nconst getGlobalSnapshot = ()=>nativeGlobal.__FEDERATION__.moduleInfo;\nconst getTargetSnapshotInfoByModuleInfo = (moduleInfo, snapshot)=>{\n    // Check if the remote is included in the hostSnapshot\n    const moduleKey = getFMId(moduleInfo);\n    const getModuleInfo = getInfoWithoutType(snapshot, moduleKey).value;\n    // The remoteSnapshot might not include a version\n    if (getModuleInfo && !getModuleInfo.version && 'version' in moduleInfo && moduleInfo['version']) {\n        getModuleInfo.version = moduleInfo['version'];\n    }\n    if (getModuleInfo) {\n        return getModuleInfo;\n    }\n    // If the remote is not included in the hostSnapshot, deploy a micro app snapshot\n    if ('version' in moduleInfo && moduleInfo['version']) {\n        const { version } = moduleInfo, resModuleInfo = polyfills._object_without_properties_loose(moduleInfo, [\n            \"version\"\n        ]);\n        const moduleKeyWithoutVersion = getFMId(resModuleInfo);\n        const getModuleInfoWithoutVersion = getInfoWithoutType(nativeGlobal.__FEDERATION__.moduleInfo, moduleKeyWithoutVersion).value;\n        if ((getModuleInfoWithoutVersion == null ? void 0 : getModuleInfoWithoutVersion.version) === version) {\n            return getModuleInfoWithoutVersion;\n        }\n    }\n    return;\n};\nconst getGlobalSnapshotInfoByModuleInfo = (moduleInfo)=>getTargetSnapshotInfoByModuleInfo(moduleInfo, nativeGlobal.__FEDERATION__.moduleInfo);\nconst setGlobalSnapshotInfoByModuleInfo = (remoteInfo, moduleDetailInfo)=>{\n    const moduleKey = getFMId(remoteInfo);\n    nativeGlobal.__FEDERATION__.moduleInfo[moduleKey] = moduleDetailInfo;\n    return nativeGlobal.__FEDERATION__.moduleInfo;\n};\nconst addGlobalSnapshot = (moduleInfos)=>{\n    nativeGlobal.__FEDERATION__.moduleInfo = polyfills._extends({}, nativeGlobal.__FEDERATION__.moduleInfo, moduleInfos);\n    return ()=>{\n        const keys = Object.keys(moduleInfos);\n        for (const key of keys){\n            delete nativeGlobal.__FEDERATION__.moduleInfo[key];\n        }\n    };\n};\nconst getRemoteEntryExports = (name, globalName)=>{\n    const remoteEntryKey = globalName || `__FEDERATION_${name}:custom__`;\n    const entryExports = CurrentGlobal[remoteEntryKey];\n    return {\n        remoteEntryKey,\n        entryExports\n    };\n};\n// This function is used to register global plugins.\n// It iterates over the provided plugins and checks if they are already registered.\n// If a plugin is not registered, it is added to the global plugins.\n// If a plugin is already registered, a warning message is logged.\nconst registerGlobalPlugins = (plugins)=>{\n    const { __GLOBAL_PLUGIN__ } = nativeGlobal.__FEDERATION__;\n    plugins.forEach((plugin)=>{\n        if (__GLOBAL_PLUGIN__.findIndex((p)=>p.name === plugin.name) === -1) {\n            __GLOBAL_PLUGIN__.push(plugin);\n        } else {\n            warn(`The plugin ${plugin.name} has been registered.`);\n        }\n    });\n};\nconst getGlobalHostPlugins = ()=>nativeGlobal.__FEDERATION__.__GLOBAL_PLUGIN__;\nconst getPreloaded = (id)=>CurrentGlobal.__FEDERATION__.__PRELOADED_MAP__.get(id);\nconst setPreloaded = (id)=>CurrentGlobal.__FEDERATION__.__PRELOADED_MAP__.set(id, true);\n\nconst DEFAULT_SCOPE = 'default';\nconst DEFAULT_REMOTE_TYPE = 'global';\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// those constants are based on https://www.rubydoc.info/gems/semantic_range/3.0.0/SemanticRange#BUILDIDENTIFIER-constant\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nconst buildIdentifier = '[0-9A-Za-z-]+';\nconst build = `(?:\\\\+(${buildIdentifier}(?:\\\\.${buildIdentifier})*))`;\nconst numericIdentifier = '0|[1-9]\\\\d*';\nconst numericIdentifierLoose = '[0-9]+';\nconst nonNumericIdentifier = '\\\\d*[a-zA-Z-][a-zA-Z0-9-]*';\nconst preReleaseIdentifierLoose = `(?:${numericIdentifierLoose}|${nonNumericIdentifier})`;\nconst preReleaseLoose = `(?:-?(${preReleaseIdentifierLoose}(?:\\\\.${preReleaseIdentifierLoose})*))`;\nconst preReleaseIdentifier = `(?:${numericIdentifier}|${nonNumericIdentifier})`;\nconst preRelease = `(?:-(${preReleaseIdentifier}(?:\\\\.${preReleaseIdentifier})*))`;\nconst xRangeIdentifier = `${numericIdentifier}|x|X|\\\\*`;\nconst xRangePlain = `[v=\\\\s]*(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:\\\\.(${xRangeIdentifier})(?:${preRelease})?${build}?)?)?`;\nconst hyphenRange = `^\\\\s*(${xRangePlain})\\\\s+-\\\\s+(${xRangePlain})\\\\s*$`;\nconst mainVersionLoose = `(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})\\\\.(${numericIdentifierLoose})`;\nconst loosePlain = `[v=\\\\s]*${mainVersionLoose}${preReleaseLoose}?${build}?`;\nconst gtlt = '((?:<|>)?=?)';\nconst comparatorTrim = `(\\\\s*)${gtlt}\\\\s*(${loosePlain}|${xRangePlain})`;\nconst loneTilde = '(?:~>?)';\nconst tildeTrim = `(\\\\s*)${loneTilde}\\\\s+`;\nconst loneCaret = '(?:\\\\^)';\nconst caretTrim = `(\\\\s*)${loneCaret}\\\\s+`;\nconst star = '(<|>)?=?\\\\s*\\\\*';\nconst caret = `^${loneCaret}${xRangePlain}$`;\nconst mainVersion = `(${numericIdentifier})\\\\.(${numericIdentifier})\\\\.(${numericIdentifier})`;\nconst fullPlain = `v?${mainVersion}${preRelease}?${build}?`;\nconst tilde = `^${loneTilde}${xRangePlain}$`;\nconst xRange = `^${gtlt}\\\\s*${xRangePlain}$`;\nconst comparator = `^${gtlt}\\\\s*(${fullPlain})$|^$`;\n// copy from semver package\nconst gte0 = '^\\\\s*>=\\\\s*0.0.0\\\\s*$';\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseRegex(source) {\n    return new RegExp(source);\n}\nfunction isXVersion(version) {\n    return !version || version.toLowerCase() === 'x' || version === '*';\n}\nfunction pipe(...fns) {\n    return (x)=>fns.reduce((v, f)=>f(v), x);\n}\nfunction extractComparator(comparatorString) {\n    return comparatorString.match(parseRegex(comparator));\n}\nfunction combineVersion(major, minor, patch, preRelease) {\n    const mainVersion = `${major}.${minor}.${patch}`;\n    if (preRelease) {\n        return `${mainVersion}-${preRelease}`;\n    }\n    return mainVersion;\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseHyphen(range) {\n    return range.replace(parseRegex(hyphenRange), (_range, from, fromMajor, fromMinor, fromPatch, _fromPreRelease, _fromBuild, to, toMajor, toMinor, toPatch, toPreRelease)=>{\n        if (isXVersion(fromMajor)) {\n            from = '';\n        } else if (isXVersion(fromMinor)) {\n            from = `>=${fromMajor}.0.0`;\n        } else if (isXVersion(fromPatch)) {\n            from = `>=${fromMajor}.${fromMinor}.0`;\n        } else {\n            from = `>=${from}`;\n        }\n        if (isXVersion(toMajor)) {\n            to = '';\n        } else if (isXVersion(toMinor)) {\n            to = `<${Number(toMajor) + 1}.0.0-0`;\n        } else if (isXVersion(toPatch)) {\n            to = `<${toMajor}.${Number(toMinor) + 1}.0-0`;\n        } else if (toPreRelease) {\n            to = `<=${toMajor}.${toMinor}.${toPatch}-${toPreRelease}`;\n        } else {\n            to = `<=${to}`;\n        }\n        return `${from} ${to}`.trim();\n    });\n}\nfunction parseComparatorTrim(range) {\n    return range.replace(parseRegex(comparatorTrim), '$1$2$3');\n}\nfunction parseTildeTrim(range) {\n    return range.replace(parseRegex(tildeTrim), '$1~');\n}\nfunction parseCaretTrim(range) {\n    return range.replace(parseRegex(caretTrim), '$1^');\n}\nfunction parseCarets(range) {\n    return range.trim().split(/\\s+/).map((rangeVersion)=>rangeVersion.replace(parseRegex(caret), (_, major, minor, patch, preRelease)=>{\n            if (isXVersion(major)) {\n                return '';\n            } else if (isXVersion(minor)) {\n                return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;\n            } else if (isXVersion(patch)) {\n                if (major === '0') {\n                    return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;\n                } else {\n                    return `>=${major}.${minor}.0 <${Number(major) + 1}.0.0-0`;\n                }\n            } else if (preRelease) {\n                if (major === '0') {\n                    if (minor === '0') {\n                        return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${minor}.${Number(patch) + 1}-0`;\n                    } else {\n                        return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n                    }\n                } else {\n                    return `>=${major}.${minor}.${patch}-${preRelease} <${Number(major) + 1}.0.0-0`;\n                }\n            } else {\n                if (major === '0') {\n                    if (minor === '0') {\n                        return `>=${major}.${minor}.${patch} <${major}.${minor}.${Number(patch) + 1}-0`;\n                    } else {\n                        return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;\n                    }\n                }\n                return `>=${major}.${minor}.${patch} <${Number(major) + 1}.0.0-0`;\n            }\n        })).join(' ');\n}\nfunction parseTildes(range) {\n    return range.trim().split(/\\s+/).map((rangeVersion)=>rangeVersion.replace(parseRegex(tilde), (_, major, minor, patch, preRelease)=>{\n            if (isXVersion(major)) {\n                return '';\n            } else if (isXVersion(minor)) {\n                return `>=${major}.0.0 <${Number(major) + 1}.0.0-0`;\n            } else if (isXVersion(patch)) {\n                return `>=${major}.${minor}.0 <${major}.${Number(minor) + 1}.0-0`;\n            } else if (preRelease) {\n                return `>=${major}.${minor}.${patch}-${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n            }\n            return `>=${major}.${minor}.${patch} <${major}.${Number(minor) + 1}.0-0`;\n        })).join(' ');\n}\nfunction parseXRanges(range) {\n    return range.split(/\\s+/).map((rangeVersion)=>rangeVersion.trim().replace(parseRegex(xRange), (ret, gtlt, major, minor, patch, preRelease)=>{\n            const isXMajor = isXVersion(major);\n            const isXMinor = isXMajor || isXVersion(minor);\n            const isXPatch = isXMinor || isXVersion(patch);\n            if (gtlt === '=' && isXPatch) {\n                gtlt = '';\n            }\n            preRelease = '';\n            if (isXMajor) {\n                if (gtlt === '>' || gtlt === '<') {\n                    // nothing is allowed\n                    return '<0.0.0-0';\n                } else {\n                    // nothing is forbidden\n                    return '*';\n                }\n            } else if (gtlt && isXPatch) {\n                // replace X with 0\n                if (isXMinor) {\n                    minor = 0;\n                }\n                patch = 0;\n                if (gtlt === '>') {\n                    // >1 => >=2.0.0\n                    // >1.2 => >=1.3.0\n                    gtlt = '>=';\n                    if (isXMinor) {\n                        major = Number(major) + 1;\n                        minor = 0;\n                        patch = 0;\n                    } else {\n                        minor = Number(minor) + 1;\n                        patch = 0;\n                    }\n                } else if (gtlt === '<=') {\n                    // <=0.7.x is actually <0.8.0, since any 0.7.x should pass\n                    // Similarly, <=7.x is actually <8.0.0, etc.\n                    gtlt = '<';\n                    if (isXMinor) {\n                        major = Number(major) + 1;\n                    } else {\n                        minor = Number(minor) + 1;\n                    }\n                }\n                if (gtlt === '<') {\n                    preRelease = '-0';\n                }\n                return `${gtlt + major}.${minor}.${patch}${preRelease}`;\n            } else if (isXMinor) {\n                return `>=${major}.0.0${preRelease} <${Number(major) + 1}.0.0-0`;\n            } else if (isXPatch) {\n                return `>=${major}.${minor}.0${preRelease} <${major}.${Number(minor) + 1}.0-0`;\n            }\n            return ret;\n        })).join(' ');\n}\nfunction parseStar(range) {\n    return range.trim().replace(parseRegex(star), '');\n}\nfunction parseGTE0(comparatorString) {\n    return comparatorString.trim().replace(parseRegex(gte0), '');\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction compareAtom(rangeAtom, versionAtom) {\n    rangeAtom = Number(rangeAtom) || rangeAtom;\n    versionAtom = Number(versionAtom) || versionAtom;\n    if (rangeAtom > versionAtom) {\n        return 1;\n    }\n    if (rangeAtom === versionAtom) {\n        return 0;\n    }\n    return -1;\n}\nfunction comparePreRelease(rangeAtom, versionAtom) {\n    const { preRelease: rangePreRelease } = rangeAtom;\n    const { preRelease: versionPreRelease } = versionAtom;\n    if (rangePreRelease === undefined && Boolean(versionPreRelease)) {\n        return 1;\n    }\n    if (Boolean(rangePreRelease) && versionPreRelease === undefined) {\n        return -1;\n    }\n    if (rangePreRelease === undefined && versionPreRelease === undefined) {\n        return 0;\n    }\n    for(let i = 0, n = rangePreRelease.length; i <= n; i++){\n        const rangeElement = rangePreRelease[i];\n        const versionElement = versionPreRelease[i];\n        if (rangeElement === versionElement) {\n            continue;\n        }\n        if (rangeElement === undefined && versionElement === undefined) {\n            return 0;\n        }\n        if (!rangeElement) {\n            return 1;\n        }\n        if (!versionElement) {\n            return -1;\n        }\n        return compareAtom(rangeElement, versionElement);\n    }\n    return 0;\n}\nfunction compareVersion(rangeAtom, versionAtom) {\n    return compareAtom(rangeAtom.major, versionAtom.major) || compareAtom(rangeAtom.minor, versionAtom.minor) || compareAtom(rangeAtom.patch, versionAtom.patch) || comparePreRelease(rangeAtom, versionAtom);\n}\nfunction eq(rangeAtom, versionAtom) {\n    return rangeAtom.version === versionAtom.version;\n}\nfunction compare(rangeAtom, versionAtom) {\n    switch(rangeAtom.operator){\n        case '':\n        case '=':\n            return eq(rangeAtom, versionAtom);\n        case '>':\n            return compareVersion(rangeAtom, versionAtom) < 0;\n        case '>=':\n            return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) < 0;\n        case '<':\n            return compareVersion(rangeAtom, versionAtom) > 0;\n        case '<=':\n            return eq(rangeAtom, versionAtom) || compareVersion(rangeAtom, versionAtom) > 0;\n        case undefined:\n            {\n                // mean * or x -> all versions\n                return true;\n            }\n        default:\n            return false;\n    }\n}\n\n// fork from https://github.com/originjs/vite-plugin-federation/blob/v1.1.12/packages/lib/src/utils/semver/index.ts\n// Copyright (c)\n// vite-plugin-federation is licensed under Mulan PSL v2.\n// You can use this software according to the terms and conditions of the Mulan PSL v2.\n// You may obtain a copy of Mulan PSL v2 at:\n//      http://license.coscl.org.cn/MulanPSL2\n// THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n// See the Mulan PSL v2 for more details.\nfunction parseComparatorString(range) {\n    return pipe(// handle caret\n    // ^ --> * (any, kinda silly)\n    // ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n    // ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n    // ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n    // ^1.2.3 --> >=1.2.3 <2.0.0-0\n    // ^1.2.0 --> >=1.2.0 <2.0.0-0\n    parseCarets, // handle tilde\n    // ~, ~> --> * (any, kinda silly)\n    // ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n    // ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n    // ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n    // ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n    // ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n    parseTildes, parseXRanges, parseStar)(range);\n}\nfunction parseRange(range) {\n    return pipe(// handle hyphenRange\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    parseHyphen, // handle trim comparator\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    parseComparatorTrim, // handle trim tilde\n    // `~ 1.2.3` => `~1.2.3`\n    parseTildeTrim, // handle trim caret\n    // `^ 1.2.3` => `^1.2.3`\n    parseCaretTrim)(range.trim()).split(/\\s+/).join(' ');\n}\nfunction satisfy(version, range) {\n    if (!version) {\n        return false;\n    }\n    // Extract version details once\n    const extractedVersion = extractComparator(version);\n    if (!extractedVersion) {\n        // If the version string is invalid, it can't satisfy any range\n        return false;\n    }\n    const [, versionOperator, , versionMajor, versionMinor, versionPatch, versionPreRelease] = extractedVersion;\n    const versionAtom = {\n        operator: versionOperator,\n        version: combineVersion(versionMajor, versionMinor, versionPatch, versionPreRelease),\n        major: versionMajor,\n        minor: versionMinor,\n        patch: versionPatch,\n        preRelease: versionPreRelease == null ? void 0 : versionPreRelease.split('.')\n    };\n    // Split the range by || to handle OR conditions\n    const orRanges = range.split('||');\n    for (const orRange of orRanges){\n        const trimmedOrRange = orRange.trim();\n        if (!trimmedOrRange) {\n            // An empty range string signifies wildcard *, satisfy any valid version\n            // (We already checked if the version itself is valid)\n            return true;\n        }\n        // Handle simple wildcards explicitly before complex parsing\n        if (trimmedOrRange === '*' || trimmedOrRange === 'x') {\n            return true;\n        }\n        try {\n            // Apply existing parsing logic to the current OR sub-range\n            const parsedSubRange = parseRange(trimmedOrRange); // Handles hyphens, trims etc.\n            // Check if the result of initial parsing is empty, which can happen\n            // for some wildcard cases handled by parseRange/parseComparatorString.\n            // E.g. `parseStar` used in `parseComparatorString` returns ''.\n            if (!parsedSubRange.trim()) {\n                // If parsing results in empty string, treat as wildcard match\n                return true;\n            }\n            const parsedComparatorString = parsedSubRange.split(' ').map((rangeVersion)=>parseComparatorString(rangeVersion)) // Expands ^, ~\n            .join(' ');\n            // Check again if the comparator string became empty after specific parsing like ^ or ~\n            if (!parsedComparatorString.trim()) {\n                return true;\n            }\n            // Split the sub-range by space for implicit AND conditions\n            const comparators = parsedComparatorString.split(/\\s+/).map((comparator)=>parseGTE0(comparator))// Filter out empty strings that might result from multiple spaces\n            .filter(Boolean);\n            // If a sub-range becomes empty after parsing (e.g., invalid characters),\n            // it cannot be satisfied. This check might be redundant now but kept for safety.\n            if (comparators.length === 0) {\n                continue;\n            }\n            let subRangeSatisfied = true;\n            for (const comparator of comparators){\n                const extractedComparator = extractComparator(comparator);\n                // If any part of the AND sub-range is invalid, the sub-range is not satisfied\n                if (!extractedComparator) {\n                    subRangeSatisfied = false;\n                    break;\n                }\n                const [, rangeOperator, , rangeMajor, rangeMinor, rangePatch, rangePreRelease] = extractedComparator;\n                const rangeAtom = {\n                    operator: rangeOperator,\n                    version: combineVersion(rangeMajor, rangeMinor, rangePatch, rangePreRelease),\n                    major: rangeMajor,\n                    minor: rangeMinor,\n                    patch: rangePatch,\n                    preRelease: rangePreRelease == null ? void 0 : rangePreRelease.split('.')\n                };\n                // Check if the version satisfies this specific comparator in the AND chain\n                if (!compare(rangeAtom, versionAtom)) {\n                    subRangeSatisfied = false; // This part of the AND condition failed\n                    break; // No need to check further comparators in this sub-range\n                }\n            }\n            // If all AND conditions within this OR sub-range were met, the overall range is satisfied\n            if (subRangeSatisfied) {\n                return true;\n            }\n        } catch (e) {\n            // Log error and treat this sub-range as unsatisfied\n            console.error(`[semver] Error processing range part \"${trimmedOrRange}\":`, e);\n            continue;\n        }\n    }\n    // If none of the OR sub-ranges were satisfied\n    return false;\n}\n\nfunction formatShare(shareArgs, from, name, shareStrategy) {\n    let get;\n    if ('get' in shareArgs) {\n        // eslint-disable-next-line prefer-destructuring\n        get = shareArgs.get;\n    } else if ('lib' in shareArgs) {\n        get = ()=>Promise.resolve(shareArgs.lib);\n    } else {\n        get = ()=>Promise.resolve(()=>{\n                throw new Error(`Can not get shared '${name}'!`);\n            });\n    }\n    var _shareArgs_version, _shareArgs_scope, _shareArgs_strategy;\n    return polyfills._extends({\n        deps: [],\n        useIn: [],\n        from,\n        loading: null\n    }, shareArgs, {\n        shareConfig: polyfills._extends({\n            requiredVersion: `^${shareArgs.version}`,\n            singleton: false,\n            eager: false,\n            strictVersion: false\n        }, shareArgs.shareConfig),\n        get,\n        loaded: (shareArgs == null ? void 0 : shareArgs.loaded) || 'lib' in shareArgs ? true : undefined,\n        version: (_shareArgs_version = shareArgs.version) != null ? _shareArgs_version : '0',\n        scope: Array.isArray(shareArgs.scope) ? shareArgs.scope : [\n            (_shareArgs_scope = shareArgs.scope) != null ? _shareArgs_scope : 'default'\n        ],\n        strategy: ((_shareArgs_strategy = shareArgs.strategy) != null ? _shareArgs_strategy : shareStrategy) || 'version-first'\n    });\n}\nfunction formatShareConfigs(globalOptions, userOptions) {\n    const shareArgs = userOptions.shared || {};\n    const from = userOptions.name;\n    const shareInfos = Object.keys(shareArgs).reduce((res, pkgName)=>{\n        const arrayShareArgs = arrayOptions(shareArgs[pkgName]);\n        res[pkgName] = res[pkgName] || [];\n        arrayShareArgs.forEach((shareConfig)=>{\n            res[pkgName].push(formatShare(shareConfig, from, pkgName, userOptions.shareStrategy));\n        });\n        return res;\n    }, {});\n    const shared = polyfills._extends({}, globalOptions.shared);\n    Object.keys(shareInfos).forEach((shareKey)=>{\n        if (!shared[shareKey]) {\n            shared[shareKey] = shareInfos[shareKey];\n        } else {\n            shareInfos[shareKey].forEach((newUserSharedOptions)=>{\n                const isSameVersion = shared[shareKey].find((sharedVal)=>sharedVal.version === newUserSharedOptions.version);\n                if (!isSameVersion) {\n                    shared[shareKey].push(newUserSharedOptions);\n                }\n            });\n        }\n    });\n    return {\n        shared,\n        shareInfos\n    };\n}\nfunction versionLt(a, b) {\n    const transformInvalidVersion = (version)=>{\n        const isNumberVersion = !Number.isNaN(Number(version));\n        if (isNumberVersion) {\n            const splitArr = version.split('.');\n            let validVersion = version;\n            for(let i = 0; i < 3 - splitArr.length; i++){\n                validVersion += '.0';\n            }\n            return validVersion;\n        }\n        return version;\n    };\n    if (satisfy(transformInvalidVersion(a), `<=${transformInvalidVersion(b)}`)) {\n        return true;\n    } else {\n        return false;\n    }\n}\nconst findVersion = (shareVersionMap, cb)=>{\n    const callback = cb || function(prev, cur) {\n        return versionLt(prev, cur);\n    };\n    return Object.keys(shareVersionMap).reduce((prev, cur)=>{\n        if (!prev) {\n            return cur;\n        }\n        if (callback(prev, cur)) {\n            return cur;\n        }\n        // default version is '0' https://github.com/webpack/webpack/blob/main/lib/sharing/ProvideSharedModule.js#L136\n        if (prev === '0') {\n            return cur;\n        }\n        return prev;\n    }, 0);\n};\nconst isLoaded = (shared)=>{\n    return Boolean(shared.loaded) || typeof shared.lib === 'function';\n};\nconst isLoading = (shared)=>{\n    return Boolean(shared.loading);\n};\nfunction findSingletonVersionOrderByVersion(shareScopeMap, scope, pkgName) {\n    const versions = shareScopeMap[scope][pkgName];\n    const callback = function(prev, cur) {\n        return !isLoaded(versions[prev]) && versionLt(prev, cur);\n    };\n    return findVersion(shareScopeMap[scope][pkgName], callback);\n}\nfunction findSingletonVersionOrderByLoaded(shareScopeMap, scope, pkgName) {\n    const versions = shareScopeMap[scope][pkgName];\n    const callback = function(prev, cur) {\n        const isLoadingOrLoaded = (shared)=>{\n            return isLoaded(shared) || isLoading(shared);\n        };\n        if (isLoadingOrLoaded(versions[cur])) {\n            if (isLoadingOrLoaded(versions[prev])) {\n                return Boolean(versionLt(prev, cur));\n            } else {\n                return true;\n            }\n        }\n        if (isLoadingOrLoaded(versions[prev])) {\n            return false;\n        }\n        return versionLt(prev, cur);\n    };\n    return findVersion(shareScopeMap[scope][pkgName], callback);\n}\nfunction getFindShareFunction(strategy) {\n    if (strategy === 'loaded-first') {\n        return findSingletonVersionOrderByLoaded;\n    }\n    return findSingletonVersionOrderByVersion;\n}\nfunction getRegisteredShare(localShareScopeMap, pkgName, shareInfo, resolveShare) {\n    if (!localShareScopeMap) {\n        return;\n    }\n    const { shareConfig, scope = DEFAULT_SCOPE, strategy } = shareInfo;\n    const scopes = Array.isArray(scope) ? scope : [\n        scope\n    ];\n    for (const sc of scopes){\n        if (shareConfig && localShareScopeMap[sc] && localShareScopeMap[sc][pkgName]) {\n            const { requiredVersion } = shareConfig;\n            const findShareFunction = getFindShareFunction(strategy);\n            const maxOrSingletonVersion = findShareFunction(localShareScopeMap, sc, pkgName);\n            //@ts-ignore\n            const defaultResolver = ()=>{\n                if (shareConfig.singleton) {\n                    if (typeof requiredVersion === 'string' && !satisfy(maxOrSingletonVersion, requiredVersion)) {\n                        const msg = `Version ${maxOrSingletonVersion} from ${maxOrSingletonVersion && localShareScopeMap[sc][pkgName][maxOrSingletonVersion].from} of shared singleton module ${pkgName} does not satisfy the requirement of ${shareInfo.from} which needs ${requiredVersion})`;\n                        if (shareConfig.strictVersion) {\n                            error(msg);\n                        } else {\n                            warn(msg);\n                        }\n                    }\n                    return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                } else {\n                    if (requiredVersion === false || requiredVersion === '*') {\n                        return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                    }\n                    if (satisfy(maxOrSingletonVersion, requiredVersion)) {\n                        return localShareScopeMap[sc][pkgName][maxOrSingletonVersion];\n                    }\n                    for (const [versionKey, versionValue] of Object.entries(localShareScopeMap[sc][pkgName])){\n                        if (satisfy(versionKey, requiredVersion)) {\n                            return versionValue;\n                        }\n                    }\n                }\n            };\n            const params = {\n                shareScopeMap: localShareScopeMap,\n                scope: sc,\n                pkgName,\n                version: maxOrSingletonVersion,\n                GlobalFederation: Global.__FEDERATION__,\n                resolver: defaultResolver\n            };\n            const resolveShared = resolveShare.emit(params) || params;\n            return resolveShared.resolver();\n        }\n    }\n}\nfunction getGlobalShareScope() {\n    return Global.__FEDERATION__.__SHARE__;\n}\nfunction getTargetSharedOptions(options) {\n    const { pkgName, extraOptions, shareInfos } = options;\n    const defaultResolver = (sharedOptions)=>{\n        if (!sharedOptions) {\n            return undefined;\n        }\n        const shareVersionMap = {};\n        sharedOptions.forEach((shared)=>{\n            shareVersionMap[shared.version] = shared;\n        });\n        const callback = function(prev, cur) {\n            return !isLoaded(shareVersionMap[prev]) && versionLt(prev, cur);\n        };\n        const maxVersion = findVersion(shareVersionMap, callback);\n        return shareVersionMap[maxVersion];\n    };\n    var _extraOptions_resolver;\n    const resolver = (_extraOptions_resolver = extraOptions == null ? void 0 : extraOptions.resolver) != null ? _extraOptions_resolver : defaultResolver;\n    return Object.assign({}, resolver(shareInfos[pkgName]), extraOptions == null ? void 0 : extraOptions.customShareInfo);\n}\n\nfunction getBuilderId() {\n    //@ts-ignore\n    return typeof FEDERATION_BUILD_IDENTIFIER !== 'undefined' ? FEDERATION_BUILD_IDENTIFIER : '';\n}\n\n// Function to match a remote with its name and expose\n// id: pkgName(@federation/app1) + expose(button) = @federation/app1/button\n// id: alias(app1) + expose(button) = app1/button\n// id: alias(app1/utils) + expose(loadash/sort) = app1/utils/loadash/sort\nfunction matchRemoteWithNameAndExpose(remotes, id) {\n    for (const remote of remotes){\n        // match pkgName\n        const isNameMatched = id.startsWith(remote.name);\n        let expose = id.replace(remote.name, '');\n        if (isNameMatched) {\n            if (expose.startsWith('/')) {\n                const pkgNameOrAlias = remote.name;\n                expose = `.${expose}`;\n                return {\n                    pkgNameOrAlias,\n                    expose,\n                    remote\n                };\n            } else if (expose === '') {\n                return {\n                    pkgNameOrAlias: remote.name,\n                    expose: '.',\n                    remote\n                };\n            }\n        }\n        // match alias\n        const isAliasMatched = remote.alias && id.startsWith(remote.alias);\n        let exposeWithAlias = remote.alias && id.replace(remote.alias, '');\n        if (remote.alias && isAliasMatched) {\n            if (exposeWithAlias && exposeWithAlias.startsWith('/')) {\n                const pkgNameOrAlias = remote.alias;\n                exposeWithAlias = `.${exposeWithAlias}`;\n                return {\n                    pkgNameOrAlias,\n                    expose: exposeWithAlias,\n                    remote\n                };\n            } else if (exposeWithAlias === '') {\n                return {\n                    pkgNameOrAlias: remote.alias,\n                    expose: '.',\n                    remote\n                };\n            }\n        }\n    }\n    return;\n}\n// Function to match a remote with its name or alias\nfunction matchRemote(remotes, nameOrAlias) {\n    for (const remote of remotes){\n        const isNameMatched = nameOrAlias === remote.name;\n        if (isNameMatched) {\n            return remote;\n        }\n        const isAliasMatched = remote.alias && nameOrAlias === remote.alias;\n        if (isAliasMatched) {\n            return remote;\n        }\n    }\n    return;\n}\n\nfunction registerPlugins(plugins, instance) {\n    const globalPlugins = getGlobalHostPlugins();\n    const hookInstances = [\n        instance.hooks,\n        instance.remoteHandler.hooks,\n        instance.sharedHandler.hooks,\n        instance.snapshotHandler.hooks,\n        instance.loaderHook,\n        instance.bridgeHook\n    ];\n    // Incorporate global plugins\n    if (globalPlugins.length > 0) {\n        globalPlugins.forEach((plugin)=>{\n            if (plugins == null ? void 0 : plugins.find((item)=>item.name !== plugin.name)) {\n                plugins.push(plugin);\n            }\n        });\n    }\n    if (plugins && plugins.length > 0) {\n        plugins.forEach((plugin)=>{\n            hookInstances.forEach((hookInstance)=>{\n                hookInstance.applyPlugin(plugin, instance);\n            });\n        });\n    }\n    return plugins;\n}\n\nconst importCallback = '.then(callbacks[0]).catch(callbacks[1])';\nasync function loadEsmEntry({ entry, remoteEntryExports }) {\n    return new Promise((resolve, reject)=>{\n        try {\n            if (!remoteEntryExports) {\n                if (typeof FEDERATION_ALLOW_NEW_FUNCTION !== 'undefined') {\n                    new Function('callbacks', `import(\"${entry}\")${importCallback}`)([\n                        resolve,\n                        reject\n                    ]);\n                } else {\n                    import(/* webpackIgnore: true */ /* @vite-ignore */ entry).then(resolve).catch(reject);\n                }\n            } else {\n                resolve(remoteEntryExports);\n            }\n        } catch (e) {\n            reject(e);\n        }\n    });\n}\nasync function loadSystemJsEntry({ entry, remoteEntryExports }) {\n    return new Promise((resolve, reject)=>{\n        try {\n            if (!remoteEntryExports) {\n                //@ts-ignore\n                if (typeof __system_context__ === 'undefined') {\n                    //@ts-ignore\n                    System.import(entry).then(resolve).catch(reject);\n                } else {\n                    new Function('callbacks', `System.import(\"${entry}\")${importCallback}`)([\n                        resolve,\n                        reject\n                    ]);\n                }\n            } else {\n                resolve(remoteEntryExports);\n            }\n        } catch (e) {\n            reject(e);\n        }\n    });\n}\nfunction handleRemoteEntryLoaded(name, globalName, entry) {\n    const { remoteEntryKey, entryExports } = getRemoteEntryExports(name, globalName);\n    assert(entryExports, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_001, errorCodes.runtimeDescMap, {\n        remoteName: name,\n        remoteEntryUrl: entry,\n        remoteEntryKey\n    }));\n    return entryExports;\n}\nasync function loadEntryScript({ name, globalName, entry, loaderHook }) {\n    const { entryExports: remoteEntryExports } = getRemoteEntryExports(name, globalName);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    return sdk.loadScript(entry, {\n        attrs: {},\n        createScriptHook: (url, attrs)=>{\n            const res = loaderHook.lifecycle.createScript.emit({\n                url,\n                attrs\n            });\n            if (!res) return;\n            if (res instanceof HTMLScriptElement) {\n                return res;\n            }\n            if ('script' in res || 'timeout' in res) {\n                return res;\n            }\n            return;\n        }\n    }).then(()=>{\n        return handleRemoteEntryLoaded(name, globalName, entry);\n    }).catch((e)=>{\n        assert(undefined, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_008, errorCodes.runtimeDescMap, {\n            remoteName: name,\n            resourceUrl: entry\n        }));\n        throw e;\n    });\n}\nasync function loadEntryDom({ remoteInfo, remoteEntryExports, loaderHook }) {\n    const { entry, entryGlobalName: globalName, name, type } = remoteInfo;\n    switch(type){\n        case 'esm':\n        case 'module':\n            return loadEsmEntry({\n                entry,\n                remoteEntryExports\n            });\n        case 'system':\n            return loadSystemJsEntry({\n                entry,\n                remoteEntryExports\n            });\n        default:\n            return loadEntryScript({\n                entry,\n                globalName,\n                name,\n                loaderHook\n            });\n    }\n}\nasync function loadEntryNode({ remoteInfo, loaderHook }) {\n    const { entry, entryGlobalName: globalName, name, type } = remoteInfo;\n    const { entryExports: remoteEntryExports } = getRemoteEntryExports(name, globalName);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    return sdk.loadScriptNode(entry, {\n        attrs: {\n            name,\n            globalName,\n            type\n        },\n        loaderHook: {\n            createScriptHook: (url, attrs = {})=>{\n                const res = loaderHook.lifecycle.createScript.emit({\n                    url,\n                    attrs\n                });\n                if (!res) return;\n                if ('url' in res) {\n                    return res;\n                }\n                return;\n            }\n        }\n    }).then(()=>{\n        return handleRemoteEntryLoaded(name, globalName, entry);\n    }).catch((e)=>{\n        throw e;\n    });\n}\nfunction getRemoteEntryUniqueKey(remoteInfo) {\n    const { entry, name } = remoteInfo;\n    return sdk.composeKeyWithSeparator(name, entry);\n}\nasync function getRemoteEntry({ origin, remoteEntryExports, remoteInfo }) {\n    const uniqueKey = getRemoteEntryUniqueKey(remoteInfo);\n    if (remoteEntryExports) {\n        return remoteEntryExports;\n    }\n    if (!globalLoading[uniqueKey]) {\n        const loadEntryHook = origin.remoteHandler.hooks.lifecycle.loadEntry;\n        const loaderHook = origin.loaderHook;\n        globalLoading[uniqueKey] = loadEntryHook.emit({\n            loaderHook,\n            remoteInfo,\n            remoteEntryExports\n        }).then((res)=>{\n            if (res) {\n                return res;\n            }\n            // Use ENV_TARGET if defined, otherwise fallback to isBrowserEnv, must keep this\n            const isWebEnvironment = typeof ENV_TARGET !== 'undefined' ? ENV_TARGET === 'web' : sdk.isBrowserEnv();\n            return isWebEnvironment ? loadEntryDom({\n                remoteInfo,\n                remoteEntryExports,\n                loaderHook\n            }) : loadEntryNode({\n                remoteInfo,\n                loaderHook\n            });\n        });\n    }\n    return globalLoading[uniqueKey];\n}\nfunction getRemoteInfo(remote) {\n    return polyfills._extends({}, remote, {\n        entry: 'entry' in remote ? remote.entry : '',\n        type: remote.type || DEFAULT_REMOTE_TYPE,\n        entryGlobalName: remote.entryGlobalName || remote.name,\n        shareScope: remote.shareScope || DEFAULT_SCOPE\n    });\n}\n\nfunction defaultPreloadArgs(preloadConfig) {\n    return polyfills._extends({\n        resourceCategory: 'sync',\n        share: true,\n        depsRemote: true,\n        prefetchInterface: false\n    }, preloadConfig);\n}\nfunction formatPreloadArgs(remotes, preloadArgs) {\n    return preloadArgs.map((args)=>{\n        const remoteInfo = matchRemote(remotes, args.nameOrAlias);\n        assert(remoteInfo, `Unable to preload ${args.nameOrAlias} as it is not included in ${!remoteInfo && sdk.safeToString({\n            remoteInfo,\n            remotes\n        })}`);\n        return {\n            remote: remoteInfo,\n            preloadConfig: defaultPreloadArgs(args)\n        };\n    });\n}\nfunction normalizePreloadExposes(exposes) {\n    if (!exposes) {\n        return [];\n    }\n    return exposes.map((expose)=>{\n        if (expose === '.') {\n            return expose;\n        }\n        if (expose.startsWith('./')) {\n            return expose.replace('./', '');\n        }\n        return expose;\n    });\n}\nfunction preloadAssets(remoteInfo, host, assets, // It is used to distinguish preload from load remote parallel loading\nuseLinkPreload = true) {\n    const { cssAssets, jsAssetsWithoutEntry, entryAssets } = assets;\n    if (host.options.inBrowser) {\n        entryAssets.forEach((asset)=>{\n            const { moduleInfo } = asset;\n            const module = host.moduleCache.get(remoteInfo.name);\n            if (module) {\n                getRemoteEntry({\n                    origin: host,\n                    remoteInfo: moduleInfo,\n                    remoteEntryExports: module.remoteEntryExports\n                });\n            } else {\n                getRemoteEntry({\n                    origin: host,\n                    remoteInfo: moduleInfo,\n                    remoteEntryExports: undefined\n                });\n            }\n        });\n        if (useLinkPreload) {\n            const defaultAttrs = {\n                rel: 'preload',\n                as: 'style'\n            };\n            cssAssets.forEach((cssUrl)=>{\n                const { link: cssEl, needAttach } = sdk.createLink({\n                    url: cssUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    }\n                });\n                needAttach && document.head.appendChild(cssEl);\n            });\n        } else {\n            const defaultAttrs = {\n                rel: 'stylesheet',\n                type: 'text/css'\n            };\n            cssAssets.forEach((cssUrl)=>{\n                const { link: cssEl, needAttach } = sdk.createLink({\n                    url: cssUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    },\n                    needDeleteLink: false\n                });\n                needAttach && document.head.appendChild(cssEl);\n            });\n        }\n        if (useLinkPreload) {\n            const defaultAttrs = {\n                rel: 'preload',\n                as: 'script'\n            };\n            jsAssetsWithoutEntry.forEach((jsUrl)=>{\n                const { link: linkEl, needAttach } = sdk.createLink({\n                    url: jsUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createLinkHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createLink.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLLinkElement) {\n                            return res;\n                        }\n                        return;\n                    }\n                });\n                needAttach && document.head.appendChild(linkEl);\n            });\n        } else {\n            const defaultAttrs = {\n                fetchpriority: 'high',\n                type: (remoteInfo == null ? void 0 : remoteInfo.type) === 'module' ? 'module' : 'text/javascript'\n            };\n            jsAssetsWithoutEntry.forEach((jsUrl)=>{\n                const { script: scriptEl, needAttach } = sdk.createScript({\n                    url: jsUrl,\n                    cb: ()=>{\n                    // noop\n                    },\n                    attrs: defaultAttrs,\n                    createScriptHook: (url, attrs)=>{\n                        const res = host.loaderHook.lifecycle.createScript.emit({\n                            url,\n                            attrs\n                        });\n                        if (res instanceof HTMLScriptElement) {\n                            return res;\n                        }\n                        return;\n                    },\n                    needDeleteScript: true\n                });\n                needAttach && document.head.appendChild(scriptEl);\n            });\n        }\n    }\n}\n\nconst ShareUtils = {\n    getRegisteredShare,\n    getGlobalShareScope\n};\nconst GlobalUtils = {\n    Global,\n    nativeGlobal,\n    resetFederationGlobalInfo,\n    setGlobalFederationInstance,\n    getGlobalFederationConstructor,\n    setGlobalFederationConstructor,\n    getInfoWithoutType,\n    getGlobalSnapshot,\n    getTargetSnapshotInfoByModuleInfo,\n    getGlobalSnapshotInfoByModuleInfo,\n    setGlobalSnapshotInfoByModuleInfo,\n    addGlobalSnapshot,\n    getRemoteEntryExports,\n    registerGlobalPlugins,\n    getGlobalHostPlugins,\n    getPreloaded,\n    setPreloaded\n};\nvar helpers = {\n    global: GlobalUtils,\n    share: ShareUtils,\n    utils: {\n        matchRemoteWithNameAndExpose,\n        preloadAssets,\n        getRemoteInfo\n    }\n};\n\nlet Module = class Module {\n    async getEntry() {\n        if (this.remoteEntryExports) {\n            return this.remoteEntryExports;\n        }\n        let remoteEntryExports;\n        try {\n            remoteEntryExports = await getRemoteEntry({\n                origin: this.host,\n                remoteInfo: this.remoteInfo,\n                remoteEntryExports: this.remoteEntryExports\n            });\n        } catch (err) {\n            const uniqueKey = getRemoteEntryUniqueKey(this.remoteInfo);\n            remoteEntryExports = await this.host.loaderHook.lifecycle.loadEntryError.emit({\n                getRemoteEntry,\n                origin: this.host,\n                remoteInfo: this.remoteInfo,\n                remoteEntryExports: this.remoteEntryExports,\n                globalLoading,\n                uniqueKey\n            });\n        }\n        assert(remoteEntryExports, `remoteEntryExports is undefined \\n ${sdk.safeToString(this.remoteInfo)}`);\n        this.remoteEntryExports = remoteEntryExports;\n        return this.remoteEntryExports;\n    }\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    async get(id, expose, options, remoteSnapshot) {\n        const { loadFactory = true } = options || {\n            loadFactory: true\n        };\n        // Get remoteEntry.js\n        const remoteEntryExports = await this.getEntry();\n        if (!this.inited) {\n            const localShareScopeMap = this.host.shareScopeMap;\n            const shareScopeKeys = Array.isArray(this.remoteInfo.shareScope) ? this.remoteInfo.shareScope : [\n                this.remoteInfo.shareScope\n            ];\n            if (!shareScopeKeys.length) {\n                shareScopeKeys.push('default');\n            }\n            shareScopeKeys.forEach((shareScopeKey)=>{\n                if (!localShareScopeMap[shareScopeKey]) {\n                    localShareScopeMap[shareScopeKey] = {};\n                }\n            });\n            // TODO: compate legacy init params, should use shareScopeMap if exist\n            const shareScope = localShareScopeMap[shareScopeKeys[0]];\n            const initScope = [];\n            const remoteEntryInitOptions = {\n                version: this.remoteInfo.version || '',\n                shareScopeKeys: Array.isArray(this.remoteInfo.shareScope) ? shareScopeKeys : this.remoteInfo.shareScope || 'default'\n            };\n            // Help to find host instance\n            Object.defineProperty(remoteEntryInitOptions, 'shareScopeMap', {\n                value: localShareScopeMap,\n                // remoteEntryInitOptions will be traversed and assigned during container init, ,so this attribute is not allowed to be traversed\n                enumerable: false\n            });\n            const initContainerOptions = await this.host.hooks.lifecycle.beforeInitContainer.emit({\n                shareScope,\n                // @ts-ignore shareScopeMap will be set by Object.defineProperty\n                remoteEntryInitOptions,\n                initScope,\n                remoteInfo: this.remoteInfo,\n                origin: this.host\n            });\n            if (typeof (remoteEntryExports == null ? void 0 : remoteEntryExports.init) === 'undefined') {\n                error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_002, errorCodes.runtimeDescMap, {\n                    hostName: this.host.name,\n                    remoteName: this.remoteInfo.name,\n                    remoteEntryUrl: this.remoteInfo.entry,\n                    remoteEntryKey: this.remoteInfo.entryGlobalName\n                }));\n            }\n            await remoteEntryExports.init(initContainerOptions.shareScope, initContainerOptions.initScope, initContainerOptions.remoteEntryInitOptions);\n            await this.host.hooks.lifecycle.initContainer.emit(polyfills._extends({}, initContainerOptions, {\n                id,\n                remoteSnapshot,\n                remoteEntryExports\n            }));\n        }\n        this.lib = remoteEntryExports;\n        this.inited = true;\n        let moduleFactory;\n        moduleFactory = await this.host.loaderHook.lifecycle.getModuleFactory.emit({\n            remoteEntryExports,\n            expose,\n            moduleInfo: this.remoteInfo\n        });\n        // get exposeGetter\n        if (!moduleFactory) {\n            moduleFactory = await remoteEntryExports.get(expose);\n        }\n        assert(moduleFactory, `${getFMId(this.remoteInfo)} remote don't export ${expose}.`);\n        // keep symbol for module name always one format\n        const symbolName = processModuleAlias(this.remoteInfo.name, expose);\n        const wrapModuleFactory = this.wraperFactory(moduleFactory, symbolName);\n        if (!loadFactory) {\n            return wrapModuleFactory;\n        }\n        const exposeContent = await wrapModuleFactory();\n        return exposeContent;\n    }\n    wraperFactory(moduleFactory, id) {\n        function defineModuleId(res, id) {\n            if (res && typeof res === 'object' && Object.isExtensible(res) && !Object.getOwnPropertyDescriptor(res, Symbol.for('mf_module_id'))) {\n                Object.defineProperty(res, Symbol.for('mf_module_id'), {\n                    value: id,\n                    enumerable: false\n                });\n            }\n        }\n        if (moduleFactory instanceof Promise) {\n            return async ()=>{\n                const res = await moduleFactory();\n                // This parameter is used for bridge debugging\n                defineModuleId(res, id);\n                return res;\n            };\n        } else {\n            return ()=>{\n                const res = moduleFactory();\n                // This parameter is used for bridge debugging\n                defineModuleId(res, id);\n                return res;\n            };\n        }\n    }\n    constructor({ remoteInfo, host }){\n        this.inited = false;\n        this.lib = undefined;\n        this.remoteInfo = remoteInfo;\n        this.host = host;\n    }\n};\n\nclass SyncHook {\n    on(fn) {\n        if (typeof fn === 'function') {\n            this.listeners.add(fn);\n        }\n    }\n    once(fn) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const self = this;\n        this.on(function wrapper(...args) {\n            self.remove(wrapper);\n            // eslint-disable-next-line prefer-spread\n            return fn.apply(null, args);\n        });\n    }\n    emit(...data) {\n        let result;\n        if (this.listeners.size > 0) {\n            // eslint-disable-next-line prefer-spread\n            this.listeners.forEach((fn)=>{\n                result = fn(...data);\n            });\n        }\n        return result;\n    }\n    remove(fn) {\n        this.listeners.delete(fn);\n    }\n    removeAll() {\n        this.listeners.clear();\n    }\n    constructor(type){\n        this.type = '';\n        this.listeners = new Set();\n        if (type) {\n            this.type = type;\n        }\n    }\n}\n\nclass AsyncHook extends SyncHook {\n    emit(...data) {\n        let result;\n        const ls = Array.from(this.listeners);\n        if (ls.length > 0) {\n            let i = 0;\n            const call = (prev)=>{\n                if (prev === false) {\n                    return false; // Abort process\n                } else if (i < ls.length) {\n                    return Promise.resolve(ls[i++].apply(null, data)).then(call);\n                } else {\n                    return prev;\n                }\n            };\n            result = call();\n        }\n        return Promise.resolve(result);\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction checkReturnData(originalData, returnedData) {\n    if (!isObject(returnedData)) {\n        return false;\n    }\n    if (originalData !== returnedData) {\n        // eslint-disable-next-line no-restricted-syntax\n        for(const key in originalData){\n            if (!(key in returnedData)) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nclass SyncWaterfallHook extends SyncHook {\n    emit(data) {\n        if (!isObject(data)) {\n            error(`The data for the \"${this.type}\" hook should be an object.`);\n        }\n        for (const fn of this.listeners){\n            try {\n                const tempData = fn(data);\n                if (checkReturnData(data, tempData)) {\n                    data = tempData;\n                } else {\n                    this.onerror(`A plugin returned an unacceptable value for the \"${this.type}\" type.`);\n                    break;\n                }\n            } catch (e) {\n                warn(e);\n                this.onerror(e);\n            }\n        }\n        return data;\n    }\n    constructor(type){\n        super();\n        this.onerror = error;\n        this.type = type;\n    }\n}\n\nclass AsyncWaterfallHook extends SyncHook {\n    emit(data) {\n        if (!isObject(data)) {\n            error(`The response data for the \"${this.type}\" hook must be an object.`);\n        }\n        const ls = Array.from(this.listeners);\n        if (ls.length > 0) {\n            let i = 0;\n            const processError = (e)=>{\n                warn(e);\n                this.onerror(e);\n                return data;\n            };\n            const call = (prevData)=>{\n                if (checkReturnData(data, prevData)) {\n                    data = prevData;\n                    if (i < ls.length) {\n                        try {\n                            return Promise.resolve(ls[i++](data)).then(call, processError);\n                        } catch (e) {\n                            return processError(e);\n                        }\n                    }\n                } else {\n                    this.onerror(`A plugin returned an incorrect value for the \"${this.type}\" type.`);\n                }\n                return data;\n            };\n            return Promise.resolve(call(data));\n        }\n        return Promise.resolve(data);\n    }\n    constructor(type){\n        super();\n        this.onerror = error;\n        this.type = type;\n    }\n}\n\nclass PluginSystem {\n    applyPlugin(plugin, instance) {\n        assert(isPlainObject(plugin), 'Plugin configuration is invalid.');\n        // The plugin's name is mandatory and must be unique\n        const pluginName = plugin.name;\n        assert(pluginName, 'A name must be provided by the plugin.');\n        if (!this.registerPlugins[pluginName]) {\n            this.registerPlugins[pluginName] = plugin;\n            plugin.apply == null ? void 0 : plugin.apply.call(plugin, instance);\n            Object.keys(this.lifecycle).forEach((key)=>{\n                const pluginLife = plugin[key];\n                if (pluginLife) {\n                    this.lifecycle[key].on(pluginLife);\n                }\n            });\n        }\n    }\n    removePlugin(pluginName) {\n        assert(pluginName, 'A name is required.');\n        const plugin = this.registerPlugins[pluginName];\n        assert(plugin, `The plugin \"${pluginName}\" is not registered.`);\n        Object.keys(plugin).forEach((key)=>{\n            if (key !== 'name') {\n                this.lifecycle[key].remove(plugin[key]);\n            }\n        });\n    }\n    constructor(lifecycle){\n        this.registerPlugins = {};\n        this.lifecycle = lifecycle;\n        this.lifecycleKeys = Object.keys(lifecycle);\n    }\n}\n\nfunction assignRemoteInfo(remoteInfo, remoteSnapshot) {\n    const remoteEntryInfo = getRemoteEntryInfoFromSnapshot(remoteSnapshot);\n    if (!remoteEntryInfo.url) {\n        error(`The attribute remoteEntry of ${remoteInfo.name} must not be undefined.`);\n    }\n    let entryUrl = sdk.getResourceUrl(remoteSnapshot, remoteEntryInfo.url);\n    if (!sdk.isBrowserEnv() && !entryUrl.startsWith('http')) {\n        entryUrl = `https:${entryUrl}`;\n    }\n    remoteInfo.type = remoteEntryInfo.type;\n    remoteInfo.entryGlobalName = remoteEntryInfo.globalName;\n    remoteInfo.entry = entryUrl;\n    remoteInfo.version = remoteSnapshot.version;\n    remoteInfo.buildVersion = remoteSnapshot.buildVersion;\n}\nfunction snapshotPlugin() {\n    return {\n        name: 'snapshot-plugin',\n        async afterResolve (args) {\n            const { remote, pkgNameOrAlias, expose, origin, remoteInfo, id } = args;\n            if (!isRemoteInfoWithEntry(remote) || !isPureRemoteEntry(remote)) {\n                const { remoteSnapshot, globalSnapshot } = await origin.snapshotHandler.loadRemoteSnapshotInfo({\n                    moduleInfo: remote,\n                    id\n                });\n                assignRemoteInfo(remoteInfo, remoteSnapshot);\n                // preloading assets\n                const preloadOptions = {\n                    remote,\n                    preloadConfig: {\n                        nameOrAlias: pkgNameOrAlias,\n                        exposes: [\n                            expose\n                        ],\n                        resourceCategory: 'sync',\n                        share: false,\n                        depsRemote: false\n                    }\n                };\n                const assets = await origin.remoteHandler.hooks.lifecycle.generatePreloadAssets.emit({\n                    origin,\n                    preloadOptions,\n                    remoteInfo,\n                    remote,\n                    remoteSnapshot,\n                    globalSnapshot\n                });\n                if (assets) {\n                    preloadAssets(remoteInfo, origin, assets, false);\n                }\n                return polyfills._extends({}, args, {\n                    remoteSnapshot\n                });\n            }\n            return args;\n        }\n    };\n}\n\n// name\n// name:version\nfunction splitId(id) {\n    const splitInfo = id.split(':');\n    if (splitInfo.length === 1) {\n        return {\n            name: splitInfo[0],\n            version: undefined\n        };\n    } else if (splitInfo.length === 2) {\n        return {\n            name: splitInfo[0],\n            version: splitInfo[1]\n        };\n    } else {\n        return {\n            name: splitInfo[1],\n            version: splitInfo[2]\n        };\n    }\n}\n// Traverse all nodes in moduleInfo and traverse the entire snapshot\nfunction traverseModuleInfo(globalSnapshot, remoteInfo, traverse, isRoot, memo = {}, remoteSnapshot) {\n    const id = getFMId(remoteInfo);\n    const { value: snapshotValue } = getInfoWithoutType(globalSnapshot, id);\n    const effectiveRemoteSnapshot = remoteSnapshot || snapshotValue;\n    if (effectiveRemoteSnapshot && !sdk.isManifestProvider(effectiveRemoteSnapshot)) {\n        traverse(effectiveRemoteSnapshot, remoteInfo, isRoot);\n        if (effectiveRemoteSnapshot.remotesInfo) {\n            const remoteKeys = Object.keys(effectiveRemoteSnapshot.remotesInfo);\n            for (const key of remoteKeys){\n                if (memo[key]) {\n                    continue;\n                }\n                memo[key] = true;\n                const subRemoteInfo = splitId(key);\n                const remoteValue = effectiveRemoteSnapshot.remotesInfo[key];\n                traverseModuleInfo(globalSnapshot, {\n                    name: subRemoteInfo.name,\n                    version: remoteValue.matchedVersion\n                }, traverse, false, memo, undefined);\n            }\n        }\n    }\n}\nconst isExisted = (type, url)=>{\n    return document.querySelector(`${type}[${type === 'link' ? 'href' : 'src'}=\"${url}\"]`);\n};\n// eslint-disable-next-line max-lines-per-function\nfunction generatePreloadAssets(origin, preloadOptions, remote, globalSnapshot, remoteSnapshot) {\n    const cssAssets = [];\n    const jsAssets = [];\n    const entryAssets = [];\n    const loadedSharedJsAssets = new Set();\n    const loadedSharedCssAssets = new Set();\n    const { options } = origin;\n    const { preloadConfig: rootPreloadConfig } = preloadOptions;\n    const { depsRemote } = rootPreloadConfig;\n    const memo = {};\n    traverseModuleInfo(globalSnapshot, remote, (moduleInfoSnapshot, remoteInfo, isRoot)=>{\n        let preloadConfig;\n        if (isRoot) {\n            preloadConfig = rootPreloadConfig;\n        } else {\n            if (Array.isArray(depsRemote)) {\n                // eslint-disable-next-line array-callback-return\n                const findPreloadConfig = depsRemote.find((remoteConfig)=>{\n                    if (remoteConfig.nameOrAlias === remoteInfo.name || remoteConfig.nameOrAlias === remoteInfo.alias) {\n                        return true;\n                    }\n                    return false;\n                });\n                if (!findPreloadConfig) {\n                    return;\n                }\n                preloadConfig = defaultPreloadArgs(findPreloadConfig);\n            } else if (depsRemote === true) {\n                preloadConfig = rootPreloadConfig;\n            } else {\n                return;\n            }\n        }\n        const remoteEntryUrl = sdk.getResourceUrl(moduleInfoSnapshot, getRemoteEntryInfoFromSnapshot(moduleInfoSnapshot).url);\n        if (remoteEntryUrl) {\n            entryAssets.push({\n                name: remoteInfo.name,\n                moduleInfo: {\n                    name: remoteInfo.name,\n                    entry: remoteEntryUrl,\n                    type: 'remoteEntryType' in moduleInfoSnapshot ? moduleInfoSnapshot.remoteEntryType : 'global',\n                    entryGlobalName: 'globalName' in moduleInfoSnapshot ? moduleInfoSnapshot.globalName : remoteInfo.name,\n                    shareScope: '',\n                    version: 'version' in moduleInfoSnapshot ? moduleInfoSnapshot.version : undefined\n                },\n                url: remoteEntryUrl\n            });\n        }\n        let moduleAssetsInfo = 'modules' in moduleInfoSnapshot ? moduleInfoSnapshot.modules : [];\n        const normalizedPreloadExposes = normalizePreloadExposes(preloadConfig.exposes);\n        if (normalizedPreloadExposes.length && 'modules' in moduleInfoSnapshot) {\n            var _moduleInfoSnapshot_modules;\n            moduleAssetsInfo = moduleInfoSnapshot == null ? void 0 : (_moduleInfoSnapshot_modules = moduleInfoSnapshot.modules) == null ? void 0 : _moduleInfoSnapshot_modules.reduce((assets, moduleAssetInfo)=>{\n                if ((normalizedPreloadExposes == null ? void 0 : normalizedPreloadExposes.indexOf(moduleAssetInfo.moduleName)) !== -1) {\n                    assets.push(moduleAssetInfo);\n                }\n                return assets;\n            }, []);\n        }\n        function handleAssets(assets) {\n            const assetsRes = assets.map((asset)=>sdk.getResourceUrl(moduleInfoSnapshot, asset));\n            if (preloadConfig.filter) {\n                return assetsRes.filter(preloadConfig.filter);\n            }\n            return assetsRes;\n        }\n        if (moduleAssetsInfo) {\n            const assetsLength = moduleAssetsInfo.length;\n            for(let index = 0; index < assetsLength; index++){\n                const assetsInfo = moduleAssetsInfo[index];\n                const exposeFullPath = `${remoteInfo.name}/${assetsInfo.moduleName}`;\n                origin.remoteHandler.hooks.lifecycle.handlePreloadModule.emit({\n                    id: assetsInfo.moduleName === '.' ? remoteInfo.name : exposeFullPath,\n                    name: remoteInfo.name,\n                    remoteSnapshot: moduleInfoSnapshot,\n                    preloadConfig,\n                    remote: remoteInfo,\n                    origin\n                });\n                const preloaded = getPreloaded(exposeFullPath);\n                if (preloaded) {\n                    continue;\n                }\n                if (preloadConfig.resourceCategory === 'all') {\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.async));\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.async));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));\n                // eslint-disable-next-line no-constant-condition\n                } else if (preloadConfig.resourceCategory = 'sync') {\n                    cssAssets.push(...handleAssets(assetsInfo.assets.css.sync));\n                    jsAssets.push(...handleAssets(assetsInfo.assets.js.sync));\n                }\n                setPreloaded(exposeFullPath);\n            }\n        }\n    }, true, memo, remoteSnapshot);\n    if (remoteSnapshot.shared && remoteSnapshot.shared.length > 0) {\n        const collectSharedAssets = (shareInfo, snapshotShared)=>{\n            const registeredShared = getRegisteredShare(origin.shareScopeMap, snapshotShared.sharedName, shareInfo, origin.sharedHandler.hooks.lifecycle.resolveShare);\n            // If the global share does not exist, or the lib function does not exist, it means that the shared has not been loaded yet and can be preloaded.\n            if (registeredShared && typeof registeredShared.lib === 'function') {\n                snapshotShared.assets.js.sync.forEach((asset)=>{\n                    loadedSharedJsAssets.add(asset);\n                });\n                snapshotShared.assets.css.sync.forEach((asset)=>{\n                    loadedSharedCssAssets.add(asset);\n                });\n            }\n        };\n        remoteSnapshot.shared.forEach((shared)=>{\n            var _options_shared;\n            const shareInfos = (_options_shared = options.shared) == null ? void 0 : _options_shared[shared.sharedName];\n            if (!shareInfos) {\n                return;\n            }\n            // if no version, preload all shared\n            const sharedOptions = shared.version ? shareInfos.find((s)=>s.version === shared.version) : shareInfos;\n            if (!sharedOptions) {\n                return;\n            }\n            const arrayShareInfo = arrayOptions(sharedOptions);\n            arrayShareInfo.forEach((s)=>{\n                collectSharedAssets(s, shared);\n            });\n        });\n    }\n    const needPreloadJsAssets = jsAssets.filter((asset)=>!loadedSharedJsAssets.has(asset) && !isExisted('script', asset));\n    const needPreloadCssAssets = cssAssets.filter((asset)=>!loadedSharedCssAssets.has(asset) && !isExisted('link', asset));\n    return {\n        cssAssets: needPreloadCssAssets,\n        jsAssetsWithoutEntry: needPreloadJsAssets,\n        entryAssets: entryAssets.filter((entry)=>!isExisted('script', entry.url))\n    };\n}\nconst generatePreloadAssetsPlugin = function() {\n    return {\n        name: 'generate-preload-assets-plugin',\n        async generatePreloadAssets (args) {\n            const { origin, preloadOptions, remoteInfo, remote, globalSnapshot, remoteSnapshot } = args;\n            if (!sdk.isBrowserEnv()) {\n                return {\n                    cssAssets: [],\n                    jsAssetsWithoutEntry: [],\n                    entryAssets: []\n                };\n            }\n            if (isRemoteInfoWithEntry(remote) && isPureRemoteEntry(remote)) {\n                return {\n                    cssAssets: [],\n                    jsAssetsWithoutEntry: [],\n                    entryAssets: [\n                        {\n                            name: remote.name,\n                            url: remote.entry,\n                            moduleInfo: {\n                                name: remoteInfo.name,\n                                entry: remote.entry,\n                                type: remoteInfo.type || 'global',\n                                entryGlobalName: '',\n                                shareScope: ''\n                            }\n                        }\n                    ]\n                };\n            }\n            assignRemoteInfo(remoteInfo, remoteSnapshot);\n            const assets = generatePreloadAssets(origin, preloadOptions, remoteInfo, globalSnapshot, remoteSnapshot);\n            return assets;\n        }\n    };\n};\n\nfunction getGlobalRemoteInfo(moduleInfo, origin) {\n    const hostGlobalSnapshot = getGlobalSnapshotInfoByModuleInfo({\n        name: origin.name,\n        version: origin.options.version\n    });\n    // get remote detail info from global\n    const globalRemoteInfo = hostGlobalSnapshot && 'remotesInfo' in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && getInfoWithoutType(hostGlobalSnapshot.remotesInfo, moduleInfo.name).value;\n    if (globalRemoteInfo && globalRemoteInfo.matchedVersion) {\n        return {\n            hostGlobalSnapshot,\n            globalSnapshot: getGlobalSnapshot(),\n            remoteSnapshot: getGlobalSnapshotInfoByModuleInfo({\n                name: moduleInfo.name,\n                version: globalRemoteInfo.matchedVersion\n            })\n        };\n    }\n    return {\n        hostGlobalSnapshot: undefined,\n        globalSnapshot: getGlobalSnapshot(),\n        remoteSnapshot: getGlobalSnapshotInfoByModuleInfo({\n            name: moduleInfo.name,\n            version: 'version' in moduleInfo ? moduleInfo.version : undefined\n        })\n    };\n}\nclass SnapshotHandler {\n    // eslint-disable-next-line max-lines-per-function\n    async loadRemoteSnapshotInfo({ moduleInfo, id, expose }) {\n        const { options } = this.HostInstance;\n        await this.hooks.lifecycle.beforeLoadRemoteSnapshot.emit({\n            options,\n            moduleInfo\n        });\n        let hostSnapshot = getGlobalSnapshotInfoByModuleInfo({\n            name: this.HostInstance.options.name,\n            version: this.HostInstance.options.version\n        });\n        if (!hostSnapshot) {\n            hostSnapshot = {\n                version: this.HostInstance.options.version || '',\n                remoteEntry: '',\n                remotesInfo: {}\n            };\n            addGlobalSnapshot({\n                [this.HostInstance.options.name]: hostSnapshot\n            });\n        }\n        // In dynamic loadRemote scenarios, incomplete remotesInfo delivery may occur. In such cases, the remotesInfo in the host needs to be completed in the snapshot at runtime.\n        // This ensures the snapshot's integrity and helps the chrome plugin correctly identify all producer modules, ensuring that proxyable producer modules will not be missing.\n        if (hostSnapshot && 'remotesInfo' in hostSnapshot && !getInfoWithoutType(hostSnapshot.remotesInfo, moduleInfo.name).value) {\n            if ('version' in moduleInfo || 'entry' in moduleInfo) {\n                hostSnapshot.remotesInfo = polyfills._extends({}, hostSnapshot == null ? void 0 : hostSnapshot.remotesInfo, {\n                    [moduleInfo.name]: {\n                        matchedVersion: 'version' in moduleInfo ? moduleInfo.version : moduleInfo.entry\n                    }\n                });\n            }\n        }\n        const { hostGlobalSnapshot, remoteSnapshot, globalSnapshot } = this.getGlobalRemoteInfo(moduleInfo);\n        const { remoteSnapshot: globalRemoteSnapshot, globalSnapshot: globalSnapshotRes } = await this.hooks.lifecycle.loadSnapshot.emit({\n            options,\n            moduleInfo,\n            hostGlobalSnapshot,\n            remoteSnapshot,\n            globalSnapshot\n        });\n        let mSnapshot;\n        let gSnapshot;\n        // global snapshot includes manifest or module info includes manifest\n        if (globalRemoteSnapshot) {\n            if (sdk.isManifestProvider(globalRemoteSnapshot)) {\n                const remoteEntry = sdk.isBrowserEnv() ? globalRemoteSnapshot.remoteEntry : globalRemoteSnapshot.ssrRemoteEntry || globalRemoteSnapshot.remoteEntry || '';\n                const moduleSnapshot = await this.getManifestJson(remoteEntry, moduleInfo, {});\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                const globalSnapshotRes = setGlobalSnapshotInfoByModuleInfo(polyfills._extends({}, moduleInfo, {\n                    // The global remote may be overridden\n                    // Therefore, set the snapshot key to the global address of the actual request\n                    entry: remoteEntry\n                }), moduleSnapshot);\n                mSnapshot = moduleSnapshot;\n                gSnapshot = globalSnapshotRes;\n            } else {\n                const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                    options: this.HostInstance.options,\n                    moduleInfo,\n                    remoteSnapshot: globalRemoteSnapshot,\n                    from: 'global'\n                });\n                mSnapshot = remoteSnapshotRes;\n                gSnapshot = globalSnapshotRes;\n            }\n        } else {\n            if (isRemoteInfoWithEntry(moduleInfo)) {\n                // get from manifest.json and merge remote info from remote server\n                const moduleSnapshot = await this.getManifestJson(moduleInfo.entry, moduleInfo, {});\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                const globalSnapshotRes = setGlobalSnapshotInfoByModuleInfo(moduleInfo, moduleSnapshot);\n                const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                    options: this.HostInstance.options,\n                    moduleInfo,\n                    remoteSnapshot: moduleSnapshot,\n                    from: 'global'\n                });\n                mSnapshot = remoteSnapshotRes;\n                gSnapshot = globalSnapshotRes;\n            } else {\n                error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_007, errorCodes.runtimeDescMap, {\n                    hostName: moduleInfo.name,\n                    hostVersion: moduleInfo.version,\n                    globalSnapshot: JSON.stringify(globalSnapshotRes)\n                }));\n            }\n        }\n        await this.hooks.lifecycle.afterLoadSnapshot.emit({\n            id,\n            host: this.HostInstance,\n            options,\n            moduleInfo,\n            remoteSnapshot: mSnapshot\n        });\n        return {\n            remoteSnapshot: mSnapshot,\n            globalSnapshot: gSnapshot\n        };\n    }\n    getGlobalRemoteInfo(moduleInfo) {\n        return getGlobalRemoteInfo(moduleInfo, this.HostInstance);\n    }\n    async getManifestJson(manifestUrl, moduleInfo, extraOptions) {\n        const getManifest = async ()=>{\n            let manifestJson = this.manifestCache.get(manifestUrl);\n            if (manifestJson) {\n                return manifestJson;\n            }\n            try {\n                let res = await this.loaderHook.lifecycle.fetch.emit(manifestUrl, {});\n                if (!res || !(res instanceof Response)) {\n                    res = await fetch(manifestUrl, {});\n                }\n                manifestJson = await res.json();\n            } catch (err) {\n                manifestJson = await this.HostInstance.remoteHandler.hooks.lifecycle.errorLoadRemote.emit({\n                    id: manifestUrl,\n                    error: err,\n                    from: 'runtime',\n                    lifecycle: 'afterResolve',\n                    origin: this.HostInstance\n                });\n                if (!manifestJson) {\n                    delete this.manifestLoading[manifestUrl];\n                    error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_003, errorCodes.runtimeDescMap, {\n                        manifestUrl,\n                        moduleName: moduleInfo.name,\n                        hostName: this.HostInstance.options.name\n                    }, `${err}`));\n                }\n            }\n            assert(manifestJson.metaData && manifestJson.exposes && manifestJson.shared, `${manifestUrl} is not a federation manifest`);\n            this.manifestCache.set(manifestUrl, manifestJson);\n            return manifestJson;\n        };\n        const asyncLoadProcess = async ()=>{\n            const manifestJson = await getManifest();\n            const remoteSnapshot = sdk.generateSnapshotFromManifest(manifestJson, {\n                version: manifestUrl\n            });\n            const { remoteSnapshot: remoteSnapshotRes } = await this.hooks.lifecycle.loadRemoteSnapshot.emit({\n                options: this.HostInstance.options,\n                moduleInfo,\n                manifestJson,\n                remoteSnapshot,\n                manifestUrl,\n                from: 'manifest'\n            });\n            return remoteSnapshotRes;\n        };\n        if (!this.manifestLoading[manifestUrl]) {\n            this.manifestLoading[manifestUrl] = asyncLoadProcess().then((res)=>res);\n        }\n        return this.manifestLoading[manifestUrl];\n    }\n    constructor(HostInstance){\n        this.loadingHostSnapshot = null;\n        this.manifestCache = new Map();\n        this.hooks = new PluginSystem({\n            beforeLoadRemoteSnapshot: new AsyncHook('beforeLoadRemoteSnapshot'),\n            loadSnapshot: new AsyncWaterfallHook('loadGlobalSnapshot'),\n            loadRemoteSnapshot: new AsyncWaterfallHook('loadRemoteSnapshot'),\n            afterLoadSnapshot: new AsyncWaterfallHook('afterLoadSnapshot')\n        });\n        this.manifestLoading = Global.__FEDERATION__.__MANIFEST_LOADING__;\n        this.HostInstance = HostInstance;\n        this.loaderHook = HostInstance.loaderHook;\n    }\n}\n\nclass SharedHandler {\n    // register shared in shareScopeMap\n    registerShared(globalOptions, userOptions) {\n        const { shareInfos, shared } = formatShareConfigs(globalOptions, userOptions);\n        const sharedKeys = Object.keys(shareInfos);\n        sharedKeys.forEach((sharedKey)=>{\n            const sharedVals = shareInfos[sharedKey];\n            sharedVals.forEach((sharedVal)=>{\n                const registeredShared = getRegisteredShare(this.shareScopeMap, sharedKey, sharedVal, this.hooks.lifecycle.resolveShare);\n                if (!registeredShared && sharedVal && sharedVal.lib) {\n                    this.setShared({\n                        pkgName: sharedKey,\n                        lib: sharedVal.lib,\n                        get: sharedVal.get,\n                        loaded: true,\n                        shared: sharedVal,\n                        from: userOptions.name\n                    });\n                }\n            });\n        });\n        return {\n            shareInfos,\n            shared\n        };\n    }\n    async loadShare(pkgName, extraOptions) {\n        const { host } = this;\n        // This function performs the following steps:\n        // 1. Checks if the currently loaded share already exists, if not, it throws an error\n        // 2. Searches globally for a matching share, if found, it uses it directly\n        // 3. If not found, it retrieves it from the current share and stores the obtained share globally.\n        const shareInfo = getTargetSharedOptions({\n            pkgName,\n            extraOptions,\n            shareInfos: host.options.shared\n        });\n        if (shareInfo == null ? void 0 : shareInfo.scope) {\n            await Promise.all(shareInfo.scope.map(async (shareScope)=>{\n                await Promise.all(this.initializeSharing(shareScope, {\n                    strategy: shareInfo.strategy\n                }));\n                return;\n            }));\n        }\n        const loadShareRes = await this.hooks.lifecycle.beforeLoadShare.emit({\n            pkgName,\n            shareInfo,\n            shared: host.options.shared,\n            origin: host\n        });\n        const { shareInfo: shareInfoRes } = loadShareRes;\n        // Assert that shareInfoRes exists, if not, throw an error\n        assert(shareInfoRes, `Cannot find ${pkgName} Share in the ${host.options.name}. Please ensure that the ${pkgName} Share parameters have been injected`);\n        // Retrieve from cache\n        const registeredShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n        const addUseIn = (shared)=>{\n            if (!shared.useIn) {\n                shared.useIn = [];\n            }\n            addUniqueItem(shared.useIn, host.options.name);\n        };\n        if (registeredShared && registeredShared.lib) {\n            addUseIn(registeredShared);\n            return registeredShared.lib;\n        } else if (registeredShared && registeredShared.loading && !registeredShared.loaded) {\n            const factory = await registeredShared.loading;\n            registeredShared.loaded = true;\n            if (!registeredShared.lib) {\n                registeredShared.lib = factory;\n            }\n            addUseIn(registeredShared);\n            return factory;\n        } else if (registeredShared) {\n            const asyncLoadProcess = async ()=>{\n                const factory = await registeredShared.get();\n                shareInfoRes.lib = factory;\n                shareInfoRes.loaded = true;\n                addUseIn(shareInfoRes);\n                const gShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n                if (gShared) {\n                    gShared.lib = factory;\n                    gShared.loaded = true;\n                    addUseIn(gShared);\n                }\n                return factory;\n            };\n            const loading = asyncLoadProcess();\n            this.setShared({\n                pkgName,\n                loaded: false,\n                shared: registeredShared,\n                from: host.options.name,\n                lib: null,\n                loading\n            });\n            return loading;\n        } else {\n            if (extraOptions == null ? void 0 : extraOptions.customShareInfo) {\n                return false;\n            }\n            const asyncLoadProcess = async ()=>{\n                const factory = await shareInfoRes.get();\n                shareInfoRes.lib = factory;\n                shareInfoRes.loaded = true;\n                addUseIn(shareInfoRes);\n                const gShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfoRes, this.hooks.lifecycle.resolveShare);\n                if (gShared) {\n                    gShared.lib = factory;\n                    gShared.loaded = true;\n                }\n                return factory;\n            };\n            const loading = asyncLoadProcess();\n            this.setShared({\n                pkgName,\n                loaded: false,\n                shared: shareInfoRes,\n                from: host.options.name,\n                lib: null,\n                loading\n            });\n            return loading;\n        }\n    }\n    /**\n     * This function initializes the sharing sequence (executed only once per share scope).\n     * It accepts one argument, the name of the share scope.\n     * If the share scope does not exist, it creates one.\n     */ // eslint-disable-next-line @typescript-eslint/member-ordering\n    initializeSharing(shareScopeName = DEFAULT_SCOPE, extraOptions) {\n        const { host } = this;\n        const from = extraOptions == null ? void 0 : extraOptions.from;\n        const strategy = extraOptions == null ? void 0 : extraOptions.strategy;\n        let initScope = extraOptions == null ? void 0 : extraOptions.initScope;\n        const promises = [];\n        if (from !== 'build') {\n            const { initTokens } = this;\n            if (!initScope) initScope = [];\n            let initToken = initTokens[shareScopeName];\n            if (!initToken) initToken = initTokens[shareScopeName] = {\n                from: this.host.name\n            };\n            if (initScope.indexOf(initToken) >= 0) return promises;\n            initScope.push(initToken);\n        }\n        const shareScope = this.shareScopeMap;\n        const hostName = host.options.name;\n        // Creates a new share scope if necessary\n        if (!shareScope[shareScopeName]) {\n            shareScope[shareScopeName] = {};\n        }\n        // Executes all initialization snippets from all accessible modules\n        const scope = shareScope[shareScopeName];\n        const register = (name, shared)=>{\n            var _activeVersion_shareConfig;\n            const { version, eager } = shared;\n            scope[name] = scope[name] || {};\n            const versions = scope[name];\n            const activeVersion = versions[version];\n            const activeVersionEager = Boolean(activeVersion && (activeVersion.eager || ((_activeVersion_shareConfig = activeVersion.shareConfig) == null ? void 0 : _activeVersion_shareConfig.eager)));\n            if (!activeVersion || activeVersion.strategy !== 'loaded-first' && !activeVersion.loaded && (Boolean(!eager) !== !activeVersionEager ? eager : hostName > activeVersion.from)) {\n                versions[version] = shared;\n            }\n        };\n        const initFn = (mod)=>mod && mod.init && mod.init(shareScope[shareScopeName], initScope);\n        const initRemoteModule = async (key)=>{\n            const { module } = await host.remoteHandler.getRemoteModuleAndOptions({\n                id: key\n            });\n            if (module.getEntry) {\n                let remoteEntryExports;\n                try {\n                    remoteEntryExports = await module.getEntry();\n                } catch (error) {\n                    remoteEntryExports = await host.remoteHandler.hooks.lifecycle.errorLoadRemote.emit({\n                        id: key,\n                        error,\n                        from: 'runtime',\n                        lifecycle: 'beforeLoadShare',\n                        origin: host\n                    });\n                }\n                if (!module.inited) {\n                    await initFn(remoteEntryExports);\n                    module.inited = true;\n                }\n            }\n        };\n        Object.keys(host.options.shared).forEach((shareName)=>{\n            const sharedArr = host.options.shared[shareName];\n            sharedArr.forEach((shared)=>{\n                if (shared.scope.includes(shareScopeName)) {\n                    register(shareName, shared);\n                }\n            });\n        });\n        // TODO: strategy==='version-first' need to be removed in the future\n        if (host.options.shareStrategy === 'version-first' || strategy === 'version-first') {\n            host.options.remotes.forEach((remote)=>{\n                if (remote.shareScope === shareScopeName) {\n                    promises.push(initRemoteModule(remote.name));\n                }\n            });\n        }\n        return promises;\n    }\n    // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.\n    // 1. If the loaded shared already exists globally, then it will be reused\n    // 2. If lib exists in local shared, it will be used directly\n    // 3. If the local get returns something other than Promise, then it will be used directly\n    loadShareSync(pkgName, extraOptions) {\n        const { host } = this;\n        const shareInfo = getTargetSharedOptions({\n            pkgName,\n            extraOptions,\n            shareInfos: host.options.shared\n        });\n        if (shareInfo == null ? void 0 : shareInfo.scope) {\n            shareInfo.scope.forEach((shareScope)=>{\n                this.initializeSharing(shareScope, {\n                    strategy: shareInfo.strategy\n                });\n            });\n        }\n        const registeredShared = getRegisteredShare(this.shareScopeMap, pkgName, shareInfo, this.hooks.lifecycle.resolveShare);\n        const addUseIn = (shared)=>{\n            if (!shared.useIn) {\n                shared.useIn = [];\n            }\n            addUniqueItem(shared.useIn, host.options.name);\n        };\n        if (registeredShared) {\n            if (typeof registeredShared.lib === 'function') {\n                addUseIn(registeredShared);\n                if (!registeredShared.loaded) {\n                    registeredShared.loaded = true;\n                    if (registeredShared.from === host.options.name) {\n                        shareInfo.loaded = true;\n                    }\n                }\n                return registeredShared.lib;\n            }\n            if (typeof registeredShared.get === 'function') {\n                const module = registeredShared.get();\n                if (!(module instanceof Promise)) {\n                    addUseIn(registeredShared);\n                    this.setShared({\n                        pkgName,\n                        loaded: true,\n                        from: host.options.name,\n                        lib: module,\n                        shared: registeredShared\n                    });\n                    return module;\n                }\n            }\n        }\n        if (shareInfo.lib) {\n            if (!shareInfo.loaded) {\n                shareInfo.loaded = true;\n            }\n            return shareInfo.lib;\n        }\n        if (shareInfo.get) {\n            const module = shareInfo.get();\n            if (module instanceof Promise) {\n                const errorCode = (extraOptions == null ? void 0 : extraOptions.from) === 'build' ? errorCodes.RUNTIME_005 : errorCodes.RUNTIME_006;\n                throw new Error(errorCodes.getShortErrorMsg(errorCode, errorCodes.runtimeDescMap, {\n                    hostName: host.options.name,\n                    sharedPkgName: pkgName\n                }));\n            }\n            shareInfo.lib = module;\n            this.setShared({\n                pkgName,\n                loaded: true,\n                from: host.options.name,\n                lib: shareInfo.lib,\n                shared: shareInfo\n            });\n            return shareInfo.lib;\n        }\n        throw new Error(errorCodes.getShortErrorMsg(errorCodes.RUNTIME_006, errorCodes.runtimeDescMap, {\n            hostName: host.options.name,\n            sharedPkgName: pkgName\n        }));\n    }\n    initShareScopeMap(scopeName, shareScope, extraOptions = {}) {\n        const { host } = this;\n        this.shareScopeMap[scopeName] = shareScope;\n        this.hooks.lifecycle.initContainerShareScopeMap.emit({\n            shareScope,\n            options: host.options,\n            origin: host,\n            scopeName,\n            hostShareScopeMap: extraOptions.hostShareScopeMap\n        });\n    }\n    setShared({ pkgName, shared, from, lib, loading, loaded, get }) {\n        const { version, scope = 'default' } = shared, shareInfo = polyfills._object_without_properties_loose(shared, [\n            \"version\",\n            \"scope\"\n        ]);\n        const scopes = Array.isArray(scope) ? scope : [\n            scope\n        ];\n        scopes.forEach((sc)=>{\n            if (!this.shareScopeMap[sc]) {\n                this.shareScopeMap[sc] = {};\n            }\n            if (!this.shareScopeMap[sc][pkgName]) {\n                this.shareScopeMap[sc][pkgName] = {};\n            }\n            if (!this.shareScopeMap[sc][pkgName][version]) {\n                this.shareScopeMap[sc][pkgName][version] = polyfills._extends({\n                    version,\n                    scope: [\n                        'default'\n                    ]\n                }, shareInfo, {\n                    lib,\n                    loaded,\n                    loading\n                });\n                if (get) {\n                    this.shareScopeMap[sc][pkgName][version].get = get;\n                }\n                return;\n            }\n            const registeredShared = this.shareScopeMap[sc][pkgName][version];\n            if (loading && !registeredShared.loading) {\n                registeredShared.loading = loading;\n            }\n        });\n    }\n    _setGlobalShareScopeMap(hostOptions) {\n        const globalShareScopeMap = getGlobalShareScope();\n        const identifier = hostOptions.id || hostOptions.name;\n        if (identifier && !globalShareScopeMap[identifier]) {\n            globalShareScopeMap[identifier] = this.shareScopeMap;\n        }\n    }\n    constructor(host){\n        this.hooks = new PluginSystem({\n            afterResolve: new AsyncWaterfallHook('afterResolve'),\n            beforeLoadShare: new AsyncWaterfallHook('beforeLoadShare'),\n            // not used yet\n            loadShare: new AsyncHook(),\n            resolveShare: new SyncWaterfallHook('resolveShare'),\n            // maybe will change, temporarily for internal use only\n            initContainerShareScopeMap: new SyncWaterfallHook('initContainerShareScopeMap')\n        });\n        this.host = host;\n        this.shareScopeMap = {};\n        this.initTokens = {};\n        this._setGlobalShareScopeMap(host.options);\n    }\n}\n\nclass RemoteHandler {\n    formatAndRegisterRemote(globalOptions, userOptions) {\n        const userRemotes = userOptions.remotes || [];\n        return userRemotes.reduce((res, remote)=>{\n            this.registerRemote(remote, res, {\n                force: false\n            });\n            return res;\n        }, globalOptions.remotes);\n    }\n    setIdToRemoteMap(id, remoteMatchInfo) {\n        const { remote, expose } = remoteMatchInfo;\n        const { name, alias } = remote;\n        this.idToRemoteMap[id] = {\n            name: remote.name,\n            expose\n        };\n        if (alias && id.startsWith(name)) {\n            const idWithAlias = id.replace(name, alias);\n            this.idToRemoteMap[idWithAlias] = {\n                name: remote.name,\n                expose\n            };\n            return;\n        }\n        if (alias && id.startsWith(alias)) {\n            const idWithName = id.replace(alias, name);\n            this.idToRemoteMap[idWithName] = {\n                name: remote.name,\n                expose\n            };\n        }\n    }\n    // eslint-disable-next-line max-lines-per-function\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async loadRemote(id, options) {\n        const { host } = this;\n        try {\n            const { loadFactory = true } = options || {\n                loadFactory: true\n            };\n            // 1. Validate the parameters of the retrieved module. There are two module request methods: pkgName + expose and alias + expose.\n            // 2. Request the snapshot information of the current host and globally store the obtained snapshot information. The retrieved module information is partially offline and partially online. The online module information will retrieve the modules used online.\n            // 3. Retrieve the detailed information of the current module from global (remoteEntry address, expose resource address)\n            // 4. After retrieving remoteEntry, call the init of the module, and then retrieve the exported content of the module through get\n            // id: pkgName(@federation/app1) + expose(button) = @federation/app1/button\n            // id: alias(app1) + expose(button) = app1/button\n            // id: alias(app1/utils) + expose(loadash/sort) = app1/utils/loadash/sort\n            const { module, moduleOptions, remoteMatchInfo } = await this.getRemoteModuleAndOptions({\n                id\n            });\n            const { pkgNameOrAlias, remote, expose, id: idRes, remoteSnapshot } = remoteMatchInfo;\n            const moduleOrFactory = await module.get(idRes, expose, options, remoteSnapshot);\n            const moduleWrapper = await this.hooks.lifecycle.onLoad.emit({\n                id: idRes,\n                pkgNameOrAlias,\n                expose,\n                exposeModule: loadFactory ? moduleOrFactory : undefined,\n                exposeModuleFactory: loadFactory ? undefined : moduleOrFactory,\n                remote,\n                options: moduleOptions,\n                moduleInstance: module,\n                origin: host\n            });\n            this.setIdToRemoteMap(id, remoteMatchInfo);\n            if (typeof moduleWrapper === 'function') {\n                return moduleWrapper;\n            }\n            return moduleOrFactory;\n        } catch (error) {\n            const { from = 'runtime' } = options || {\n                from: 'runtime'\n            };\n            const failOver = await this.hooks.lifecycle.errorLoadRemote.emit({\n                id,\n                error,\n                from,\n                lifecycle: 'onLoad',\n                origin: host\n            });\n            if (!failOver) {\n                throw error;\n            }\n            return failOver;\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async preloadRemote(preloadOptions) {\n        const { host } = this;\n        await this.hooks.lifecycle.beforePreloadRemote.emit({\n            preloadOps: preloadOptions,\n            options: host.options,\n            origin: host\n        });\n        const preloadOps = formatPreloadArgs(host.options.remotes, preloadOptions);\n        await Promise.all(preloadOps.map(async (ops)=>{\n            const { remote } = ops;\n            const remoteInfo = getRemoteInfo(remote);\n            const { globalSnapshot, remoteSnapshot } = await host.snapshotHandler.loadRemoteSnapshotInfo({\n                moduleInfo: remote\n            });\n            const assets = await this.hooks.lifecycle.generatePreloadAssets.emit({\n                origin: host,\n                preloadOptions: ops,\n                remote,\n                remoteInfo,\n                globalSnapshot,\n                remoteSnapshot\n            });\n            if (!assets) {\n                return;\n            }\n            preloadAssets(remoteInfo, host, assets);\n        }));\n    }\n    registerRemotes(remotes, options) {\n        const { host } = this;\n        remotes.forEach((remote)=>{\n            this.registerRemote(remote, host.options.remotes, {\n                force: options == null ? void 0 : options.force\n            });\n        });\n    }\n    async getRemoteModuleAndOptions(options) {\n        const { host } = this;\n        const { id } = options;\n        let loadRemoteArgs;\n        try {\n            loadRemoteArgs = await this.hooks.lifecycle.beforeRequest.emit({\n                id,\n                options: host.options,\n                origin: host\n            });\n        } catch (error) {\n            loadRemoteArgs = await this.hooks.lifecycle.errorLoadRemote.emit({\n                id,\n                options: host.options,\n                origin: host,\n                from: 'runtime',\n                error,\n                lifecycle: 'beforeRequest'\n            });\n            if (!loadRemoteArgs) {\n                throw error;\n            }\n        }\n        const { id: idRes } = loadRemoteArgs;\n        const remoteSplitInfo = matchRemoteWithNameAndExpose(host.options.remotes, idRes);\n        assert(remoteSplitInfo, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_004, errorCodes.runtimeDescMap, {\n            hostName: host.options.name,\n            requestId: idRes\n        }));\n        const { remote: rawRemote } = remoteSplitInfo;\n        const remoteInfo = getRemoteInfo(rawRemote);\n        const matchInfo = await host.sharedHandler.hooks.lifecycle.afterResolve.emit(polyfills._extends({\n            id: idRes\n        }, remoteSplitInfo, {\n            options: host.options,\n            origin: host,\n            remoteInfo\n        }));\n        const { remote, expose } = matchInfo;\n        assert(remote && expose, `The 'beforeRequest' hook was executed, but it failed to return the correct 'remote' and 'expose' values while loading ${idRes}.`);\n        let module = host.moduleCache.get(remote.name);\n        const moduleOptions = {\n            host: host,\n            remoteInfo\n        };\n        if (!module) {\n            module = new Module(moduleOptions);\n            host.moduleCache.set(remote.name, module);\n        }\n        return {\n            module,\n            moduleOptions,\n            remoteMatchInfo: matchInfo\n        };\n    }\n    registerRemote(remote, targetRemotes, options) {\n        const { host } = this;\n        const normalizeRemote = ()=>{\n            if (remote.alias) {\n                // Validate if alias equals the prefix of remote.name and remote.alias, if so, throw an error\n                // As multi-level path references cannot guarantee unique names, alias being a prefix of remote.name is not supported\n                const findEqual = targetRemotes.find((item)=>{\n                    var _item_alias;\n                    return remote.alias && (item.name.startsWith(remote.alias) || ((_item_alias = item.alias) == null ? void 0 : _item_alias.startsWith(remote.alias)));\n                });\n                assert(!findEqual, `The alias ${remote.alias} of remote ${remote.name} is not allowed to be the prefix of ${findEqual && findEqual.name} name or alias`);\n            }\n            // Set the remote entry to a complete path\n            if ('entry' in remote) {\n                if (sdk.isBrowserEnv() && !remote.entry.startsWith('http')) {\n                    remote.entry = new URL(remote.entry, window.location.origin).href;\n                }\n            }\n            if (!remote.shareScope) {\n                remote.shareScope = DEFAULT_SCOPE;\n            }\n            if (!remote.type) {\n                remote.type = DEFAULT_REMOTE_TYPE;\n            }\n        };\n        this.hooks.lifecycle.beforeRegisterRemote.emit({\n            remote,\n            origin: host\n        });\n        const registeredRemote = targetRemotes.find((item)=>item.name === remote.name);\n        if (!registeredRemote) {\n            normalizeRemote();\n            targetRemotes.push(remote);\n            this.hooks.lifecycle.registerRemote.emit({\n                remote,\n                origin: host\n            });\n        } else {\n            const messages = [\n                `The remote \"${remote.name}\" is already registered.`,\n                'Please note that overriding it may cause unexpected errors.'\n            ];\n            if (options == null ? void 0 : options.force) {\n                // remove registered remote\n                this.removeRemote(registeredRemote);\n                normalizeRemote();\n                targetRemotes.push(remote);\n                this.hooks.lifecycle.registerRemote.emit({\n                    remote,\n                    origin: host\n                });\n                sdk.warn(messages.join(' '));\n            }\n        }\n    }\n    removeRemote(remote) {\n        try {\n            const { host } = this;\n            const { name } = remote;\n            const remoteIndex = host.options.remotes.findIndex((item)=>item.name === name);\n            if (remoteIndex !== -1) {\n                host.options.remotes.splice(remoteIndex, 1);\n            }\n            const loadedModule = host.moduleCache.get(remote.name);\n            if (loadedModule) {\n                const remoteInfo = loadedModule.remoteInfo;\n                const key = remoteInfo.entryGlobalName;\n                if (CurrentGlobal[key]) {\n                    var _Object_getOwnPropertyDescriptor;\n                    if ((_Object_getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor(CurrentGlobal, key)) == null ? void 0 : _Object_getOwnPropertyDescriptor.configurable) {\n                        delete CurrentGlobal[key];\n                    } else {\n                        // @ts-ignore\n                        CurrentGlobal[key] = undefined;\n                    }\n                }\n                const remoteEntryUniqueKey = getRemoteEntryUniqueKey(loadedModule.remoteInfo);\n                if (globalLoading[remoteEntryUniqueKey]) {\n                    delete globalLoading[remoteEntryUniqueKey];\n                }\n                host.snapshotHandler.manifestCache.delete(remoteInfo.entry);\n                // delete unloaded shared and instance\n                let remoteInsId = remoteInfo.buildVersion ? sdk.composeKeyWithSeparator(remoteInfo.name, remoteInfo.buildVersion) : remoteInfo.name;\n                const remoteInsIndex = CurrentGlobal.__FEDERATION__.__INSTANCES__.findIndex((ins)=>{\n                    if (remoteInfo.buildVersion) {\n                        return ins.options.id === remoteInsId;\n                    } else {\n                        return ins.name === remoteInsId;\n                    }\n                });\n                if (remoteInsIndex !== -1) {\n                    const remoteIns = CurrentGlobal.__FEDERATION__.__INSTANCES__[remoteInsIndex];\n                    remoteInsId = remoteIns.options.id || remoteInsId;\n                    const globalShareScopeMap = getGlobalShareScope();\n                    let isAllSharedNotUsed = true;\n                    const needDeleteKeys = [];\n                    Object.keys(globalShareScopeMap).forEach((instId)=>{\n                        const shareScopeMap = globalShareScopeMap[instId];\n                        shareScopeMap && Object.keys(shareScopeMap).forEach((shareScope)=>{\n                            const shareScopeVal = shareScopeMap[shareScope];\n                            shareScopeVal && Object.keys(shareScopeVal).forEach((shareName)=>{\n                                const sharedPkgs = shareScopeVal[shareName];\n                                sharedPkgs && Object.keys(sharedPkgs).forEach((shareVersion)=>{\n                                    const shared = sharedPkgs[shareVersion];\n                                    if (shared && typeof shared === 'object' && shared.from === remoteInfo.name) {\n                                        if (shared.loaded || shared.loading) {\n                                            shared.useIn = shared.useIn.filter((usedHostName)=>usedHostName !== remoteInfo.name);\n                                            if (shared.useIn.length) {\n                                                isAllSharedNotUsed = false;\n                                            } else {\n                                                needDeleteKeys.push([\n                                                    instId,\n                                                    shareScope,\n                                                    shareName,\n                                                    shareVersion\n                                                ]);\n                                            }\n                                        } else {\n                                            needDeleteKeys.push([\n                                                instId,\n                                                shareScope,\n                                                shareName,\n                                                shareVersion\n                                            ]);\n                                        }\n                                    }\n                                });\n                            });\n                        });\n                    });\n                    if (isAllSharedNotUsed) {\n                        remoteIns.shareScopeMap = {};\n                        delete globalShareScopeMap[remoteInsId];\n                    }\n                    needDeleteKeys.forEach(([insId, shareScope, shareName, shareVersion])=>{\n                        var _globalShareScopeMap_insId_shareScope_shareName, _globalShareScopeMap_insId_shareScope, _globalShareScopeMap_insId;\n                        (_globalShareScopeMap_insId = globalShareScopeMap[insId]) == null ? true : (_globalShareScopeMap_insId_shareScope = _globalShareScopeMap_insId[shareScope]) == null ? true : (_globalShareScopeMap_insId_shareScope_shareName = _globalShareScopeMap_insId_shareScope[shareName]) == null ? true : delete _globalShareScopeMap_insId_shareScope_shareName[shareVersion];\n                    });\n                    CurrentGlobal.__FEDERATION__.__INSTANCES__.splice(remoteInsIndex, 1);\n                }\n                const { hostGlobalSnapshot } = getGlobalRemoteInfo(remote, host);\n                if (hostGlobalSnapshot) {\n                    const remoteKey = hostGlobalSnapshot && 'remotesInfo' in hostGlobalSnapshot && hostGlobalSnapshot.remotesInfo && getInfoWithoutType(hostGlobalSnapshot.remotesInfo, remote.name).key;\n                    if (remoteKey) {\n                        delete hostGlobalSnapshot.remotesInfo[remoteKey];\n                        if (//eslint-disable-next-line no-extra-boolean-cast\n                        Boolean(Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey])) {\n                            delete Global.__FEDERATION__.__MANIFEST_LOADING__[remoteKey];\n                        }\n                    }\n                }\n                host.moduleCache.delete(remote.name);\n            }\n        } catch (err) {\n            logger.log('removeRemote fail: ', err);\n        }\n    }\n    constructor(host){\n        this.hooks = new PluginSystem({\n            beforeRegisterRemote: new SyncWaterfallHook('beforeRegisterRemote'),\n            registerRemote: new SyncWaterfallHook('registerRemote'),\n            beforeRequest: new AsyncWaterfallHook('beforeRequest'),\n            onLoad: new AsyncHook('onLoad'),\n            handlePreloadModule: new SyncHook('handlePreloadModule'),\n            errorLoadRemote: new AsyncHook('errorLoadRemote'),\n            beforePreloadRemote: new AsyncHook('beforePreloadRemote'),\n            generatePreloadAssets: new AsyncHook('generatePreloadAssets'),\n            // not used yet\n            afterPreloadRemote: new AsyncHook(),\n            loadEntry: new AsyncHook()\n        });\n        this.host = host;\n        this.idToRemoteMap = {};\n    }\n}\n\nconst USE_SNAPSHOT = typeof FEDERATION_OPTIMIZE_NO_SNAPSHOT_PLUGIN === 'boolean' ? !FEDERATION_OPTIMIZE_NO_SNAPSHOT_PLUGIN : true; // Default to true (use snapshot) when not explicitly defined\nclass ModuleFederation {\n    initOptions(userOptions) {\n        this.registerPlugins(userOptions.plugins);\n        const options = this.formatOptions(this.options, userOptions);\n        this.options = options;\n        return options;\n    }\n    async loadShare(pkgName, extraOptions) {\n        return this.sharedHandler.loadShare(pkgName, extraOptions);\n    }\n    // The lib function will only be available if the shared set by eager or runtime init is set or the shared is successfully loaded.\n    // 1. If the loaded shared already exists globally, then it will be reused\n    // 2. If lib exists in local shared, it will be used directly\n    // 3. If the local get returns something other than Promise, then it will be used directly\n    loadShareSync(pkgName, extraOptions) {\n        return this.sharedHandler.loadShareSync(pkgName, extraOptions);\n    }\n    initializeSharing(shareScopeName = DEFAULT_SCOPE, extraOptions) {\n        return this.sharedHandler.initializeSharing(shareScopeName, extraOptions);\n    }\n    initRawContainer(name, url, container) {\n        const remoteInfo = getRemoteInfo({\n            name,\n            entry: url\n        });\n        const module = new Module({\n            host: this,\n            remoteInfo\n        });\n        module.remoteEntryExports = container;\n        this.moduleCache.set(name, module);\n        return module;\n    }\n    // eslint-disable-next-line max-lines-per-function\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async loadRemote(id, options) {\n        return this.remoteHandler.loadRemote(id, options);\n    }\n    // eslint-disable-next-line @typescript-eslint/member-ordering\n    async preloadRemote(preloadOptions) {\n        return this.remoteHandler.preloadRemote(preloadOptions);\n    }\n    initShareScopeMap(scopeName, shareScope, extraOptions = {}) {\n        this.sharedHandler.initShareScopeMap(scopeName, shareScope, extraOptions);\n    }\n    formatOptions(globalOptions, userOptions) {\n        const { shared } = formatShareConfigs(globalOptions, userOptions);\n        const { userOptions: userOptionsRes, options: globalOptionsRes } = this.hooks.lifecycle.beforeInit.emit({\n            origin: this,\n            userOptions,\n            options: globalOptions,\n            shareInfo: shared\n        });\n        const remotes = this.remoteHandler.formatAndRegisterRemote(globalOptionsRes, userOptionsRes);\n        const { shared: handledShared } = this.sharedHandler.registerShared(globalOptionsRes, userOptionsRes);\n        const plugins = [\n            ...globalOptionsRes.plugins\n        ];\n        if (userOptionsRes.plugins) {\n            userOptionsRes.plugins.forEach((plugin)=>{\n                if (!plugins.includes(plugin)) {\n                    plugins.push(plugin);\n                }\n            });\n        }\n        const optionsRes = polyfills._extends({}, globalOptions, userOptions, {\n            plugins,\n            remotes,\n            shared: handledShared\n        });\n        this.hooks.lifecycle.init.emit({\n            origin: this,\n            options: optionsRes\n        });\n        return optionsRes;\n    }\n    registerPlugins(plugins) {\n        const pluginRes = registerPlugins(plugins, this);\n        // Merge plugin\n        this.options.plugins = this.options.plugins.reduce((res, plugin)=>{\n            if (!plugin) return res;\n            if (res && !res.find((item)=>item.name === plugin.name)) {\n                res.push(plugin);\n            }\n            return res;\n        }, pluginRes || []);\n    }\n    registerRemotes(remotes, options) {\n        return this.remoteHandler.registerRemotes(remotes, options);\n    }\n    registerShared(shared) {\n        this.sharedHandler.registerShared(this.options, polyfills._extends({}, this.options, {\n            shared\n        }));\n    }\n    constructor(userOptions){\n        this.hooks = new PluginSystem({\n            beforeInit: new SyncWaterfallHook('beforeInit'),\n            init: new SyncHook(),\n            // maybe will change, temporarily for internal use only\n            beforeInitContainer: new AsyncWaterfallHook('beforeInitContainer'),\n            // maybe will change, temporarily for internal use only\n            initContainer: new AsyncWaterfallHook('initContainer')\n        });\n        this.version = \"0.17.1\";\n        this.moduleCache = new Map();\n        this.loaderHook = new PluginSystem({\n            // FIXME: may not be suitable , not open to the public yet\n            getModuleInfo: new SyncHook(),\n            createScript: new SyncHook(),\n            createLink: new SyncHook(),\n            fetch: new AsyncHook(),\n            loadEntryError: new AsyncHook(),\n            getModuleFactory: new AsyncHook()\n        });\n        this.bridgeHook = new PluginSystem({\n            beforeBridgeRender: new SyncHook(),\n            afterBridgeRender: new SyncHook(),\n            beforeBridgeDestroy: new SyncHook(),\n            afterBridgeDestroy: new SyncHook()\n        });\n        const plugins = USE_SNAPSHOT ? [\n            snapshotPlugin(),\n            generatePreloadAssetsPlugin()\n        ] : [];\n        // TODO: Validate the details of the options\n        // Initialize options with default values\n        const defaultOptions = {\n            id: getBuilderId(),\n            name: userOptions.name,\n            plugins,\n            remotes: [],\n            shared: {},\n            inBrowser: sdk.isBrowserEnv()\n        };\n        this.name = userOptions.name;\n        this.options = defaultOptions;\n        this.snapshotHandler = new SnapshotHandler(this);\n        this.sharedHandler = new SharedHandler(this);\n        this.remoteHandler = new RemoteHandler(this);\n        this.shareScopeMap = this.sharedHandler.shareScopeMap;\n        this.registerPlugins([\n            ...defaultOptions.plugins,\n            ...userOptions.plugins || []\n        ]);\n        this.options = this.formatOptions(defaultOptions, userOptions);\n    }\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nexports.loadScript = sdk.loadScript;\nexports.loadScriptNode = sdk.loadScriptNode;\nexports.CurrentGlobal = CurrentGlobal;\nexports.Global = Global;\nexports.Module = Module;\nexports.ModuleFederation = ModuleFederation;\nexports.addGlobalSnapshot = addGlobalSnapshot;\nexports.assert = assert;\nexports.getGlobalFederationConstructor = getGlobalFederationConstructor;\nexports.getGlobalSnapshot = getGlobalSnapshot;\nexports.getInfoWithoutType = getInfoWithoutType;\nexports.getRegisteredShare = getRegisteredShare;\nexports.getRemoteEntry = getRemoteEntry;\nexports.getRemoteInfo = getRemoteInfo;\nexports.helpers = helpers;\nexports.isStaticResourcesEqual = isStaticResourcesEqual;\nexports.matchRemoteWithNameAndExpose = matchRemoteWithNameAndExpose;\nexports.registerGlobalPlugins = registerGlobalPlugins;\nexports.resetFederationGlobalInfo = resetFederationGlobalInfo;\nexports.safeWrapper = safeWrapper;\nexports.satisfy = satisfy;\nexports.setGlobalFederationConstructor = setGlobalFederationConstructor;\nexports.setGlobalFederationInstance = setGlobalFederationInstance;\nexports.types = index;\n", "'use strict';\n\nvar runtimeCore = require('@module-federation/runtime-core');\n\n// injected by bundler, so it can not use runtime-core stuff\nfunction getBuilderId() {\n    //@ts-ignore\n    return typeof FEDERATION_BUILD_IDENTIFIER !== 'undefined' ? FEDERATION_BUILD_IDENTIFIER : '';\n}\nfunction getGlobalFederationInstance(name, version) {\n    const buildId = getBuilderId();\n    return runtimeCore.CurrentGlobal.__FEDERATION__.__INSTANCES__.find((GMInstance)=>{\n        if (buildId && GMInstance.options.id === buildId) {\n            return true;\n        }\n        if (GMInstance.options.name === name && !GMInstance.options.version && !version) {\n            return true;\n        }\n        if (GMInstance.options.name === name && version && GMInstance.options.version === version) {\n            return true;\n        }\n        return false;\n    });\n}\n\nexports.getGlobalFederationInstance = getGlobalFederationInstance;\n", "'use strict';\n\nvar runtimeCore = require('@module-federation/runtime-core');\nvar errorCodes = require('@module-federation/error-codes');\nvar utils = require('./utils.cjs.cjs');\n\nfunction createInstance(options) {\n    // Retrieve debug constructor\n    const ModuleFederationConstructor = runtimeCore.getGlobalFederationConstructor() || runtimeCore.ModuleFederation;\n    return new ModuleFederationConstructor(options);\n}\nlet FederationInstance = null;\n/**\n * @deprecated Use createInstance or getInstance instead\n */ function init(options) {\n    // Retrieve the same instance with the same name\n    const instance = utils.getGlobalFederationInstance(options.name, options.version);\n    if (!instance) {\n        FederationInstance = createInstance(options);\n        runtimeCore.setGlobalFederationInstance(FederationInstance);\n        return FederationInstance;\n    } else {\n        // Merge options\n        instance.initOptions(options);\n        if (!FederationInstance) {\n            FederationInstance = instance;\n        }\n        return instance;\n    }\n}\nfunction loadRemote(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    const loadRemote1 = FederationInstance.loadRemote;\n    // eslint-disable-next-line prefer-spread\n    return loadRemote1.apply(FederationInstance, args);\n}\nfunction loadShare(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    // eslint-disable-next-line prefer-spread\n    const loadShare1 = FederationInstance.loadShare;\n    return loadShare1.apply(FederationInstance, args);\n}\nfunction loadShareSync(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    const loadShareSync1 = FederationInstance.loadShareSync;\n    // eslint-disable-next-line prefer-spread\n    return loadShareSync1.apply(FederationInstance, args);\n}\nfunction preloadRemote(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.preloadRemote.apply(FederationInstance, args);\n}\nfunction registerRemotes(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerRemotes.apply(FederationInstance, args);\n}\nfunction registerPlugins(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerPlugins.apply(FederationInstance, args);\n}\nfunction getInstance() {\n    return FederationInstance;\n}\nfunction registerShared(...args) {\n    runtimeCore.assert(FederationInstance, errorCodes.getShortErrorMsg(errorCodes.RUNTIME_009, errorCodes.runtimeDescMap));\n    // eslint-disable-next-line prefer-spread\n    return FederationInstance.registerShared.apply(FederationInstance, args);\n}\n// Inject for debug\nruntimeCore.setGlobalFederationConstructor(runtimeCore.ModuleFederation);\n\nexports.Module = runtimeCore.Module;\nexports.ModuleFederation = runtimeCore.ModuleFederation;\nexports.getRemoteEntry = runtimeCore.getRemoteEntry;\nexports.getRemoteInfo = runtimeCore.getRemoteInfo;\nexports.loadScript = runtimeCore.loadScript;\nexports.loadScriptNode = runtimeCore.loadScriptNode;\nexports.registerGlobalPlugins = runtimeCore.registerGlobalPlugins;\nexports.createInstance = createInstance;\nexports.getInstance = getInstance;\nexports.init = init;\nexports.loadRemote = loadRemote;\nexports.loadShare = loadShare;\nexports.loadShareSync = loadShareSync;\nexports.preloadRemote = preloadRemote;\nexports.registerPlugins = registerPlugins;\nexports.registerRemotes = registerRemotes;\nexports.registerShared = registerShared;\n"], "mappings": ";;;;;AAAA;AAAA,wEAAAA,UAAA;AAAA;AAEA,aAAS,WAAW;AAChB,iBAAW,OAAO,UAAU,SAAS,OAAO,QAAQ;AAChD,iBAAQ,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAI;AACrC,cAAI,SAAS,UAAU,CAAC;AACxB,mBAAQ,OAAO,OAAO,KAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,EAAG,QAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QACzG;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,aAAS,iCAAiC,QAAQ,UAAU;AACxD,UAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,UAAI,SAAS,CAAC;AACd,UAAI,aAAa,OAAO,KAAK,MAAM;AACnC,UAAI,KAAK;AACT,WAAI,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAI;AAClC,cAAM,WAAW,CAAC;AAClB,YAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AAEA,IAAAA,SAAQ,WAAW;AACnB,IAAAA,SAAQ,mCAAmC;AAAA;AAAA;;;AC3B3C,IAAAC,yBAAA;AAAA,+DAAAC,UAAA;AAAA;AAEA,aAAS,WAAW;AAChB,iBAAW,OAAO,UAAU,SAAS,OAAO,QAAQ;AAChD,iBAAQ,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAI;AACrC,cAAI,SAAS,UAAU,CAAC;AACxB,mBAAQ,OAAO,OAAO,KAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,EAAG,QAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QACzG;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,IAAAA,SAAQ,IAAI;AAAA;AAAA;;;ACbZ;AAAA;AAAA;AAEA,QAAI,YAAY;AAEhB,QAAM,2BAA2B;AACjC,QAAM,eAAe;AACrB,QAAM,kBAAkB;AACxB,QAAM,sBAAsB;AAAA,MACxB,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,IACX;AACA,QAAM,mBAAmB;AAAA,MACrB,CAAC,oBAAoB,EAAE,GAAG;AAAA,MAC1B,CAAC,oBAAoB,MAAM,GAAG;AAAA,MAC9B,CAAC,oBAAoB,KAAK,GAAG;AAAA,IACjC;AACA,QAAM,0BAA0B;AAAA,MAC5B,CAAC,iBAAiB,oBAAoB,EAAE,CAAC,GAAG,oBAAoB;AAAA,MAChE,CAAC,iBAAiB,oBAAoB,MAAM,CAAC,GAAG,oBAAoB;AAAA,MACpE,CAAC,iBAAiB,oBAAoB,KAAK,CAAC,GAAG,oBAAoB;AAAA,IACvE;AACA,QAAM,YAAY;AAClB,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,eAAe;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AACA,QAAM,4BAA4B;AAClC,QAAM,qBAAqB;AAC3B,QAAM,WAAW;AACjB,QAAM,mBAAmB;AAAA,MACrB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,IACd;AAEA,QAAI,kBAA+B,OAAO,OAAO;AAAA,MAC7C,WAAW;AAAA,IACf,CAAC;AAED,QAAI,2BAAwC,OAAO,OAAO;AAAA,MACtD,WAAW;AAAA,IACf,CAAC;AAED,QAAI,yBAAsC,OAAO,OAAO;AAAA,MACpD,WAAW;AAAA,IACf,CAAC;AAED,QAAI,cAA2B,OAAO,OAAO;AAAA,MACzC,WAAW;AAAA,IACf,CAAC;AAED,aAAS,eAAe;AACpB,aAAO,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAAA,IACvE;AACA,aAAS,mBAAmB;AACxB,UAAI;AACJ,aAAO,OAAO,cAAc,iBAAiB,aAAa,cAAc,OAAO,SAAS,WAAW,aAAa;AAAA,IACpH;AACA,aAAS,iBAAiB;AACtB,UAAI;AACA,YAAI,aAAa,KAAK,OAAO,cAAc;AACvC,iBAAO,QAAQ,aAAa,QAAQ,eAAe,CAAC;AAAA,QACxD;AAAA,MACJ,SAASC,QAAO;AACZ,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,aAAS,cAAc;AACnB,UAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,QAAQ,IAAI,kBAAkB,GAAG;AAClF,eAAO,QAAQ,QAAQ,IAAI,kBAAkB,CAAC;AAAA,MAClD;AACA,UAAI,OAAO,qBAAqB,eAAe,QAAQ,gBAAgB,GAAG;AACtE,eAAO;AAAA,MACX;AACA,aAAO,eAAe;AAAA,IAC1B;AACA,QAAM,gBAAgB,WAAW;AAC7B,aAAO,OAAO,YAAY,eAAe,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IAC1E;AAEA,QAAM,eAAe;AAGrB,QAAM,aAAa,CAAC,KAAK,aAAa,YAAY,cAAY;AAC1D,YAAM,WAAW,IAAI,MAAM,SAAS;AACpC,YAAM,kBAAkB,cAAc,EAAE,UAAU,MAAM,iBAAiB;AACzE,YAAM,iBAAiB;AACvB,YAAM,UAAU,CAAC,MAAI,EAAE,WAAW,MAAM,KAAK,EAAE,SAAS,YAAY;AAEpE,UAAI,SAAS,UAAU,GAAG;AACtB,YAAI,CAAC,MAAM,GAAG,iBAAiB,IAAI;AAEnC,YAAI,IAAI,WAAW,SAAS,GAAG;AAC3B,iBAAO,SAAS,MAAM,GAAG,CAAC,EAAE,KAAK,SAAS;AAC1C,8BAAoB;AAAA,YAChB,mBAAmB,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS;AAAA,UACvD;AAAA,QACJ;AACA,YAAI,iBAAiB,mBAAmB,kBAAkB,KAAK,SAAS;AACxE,YAAI,QAAQ,cAAc,GAAG;AACzB,iBAAO;AAAA,YACH;AAAA,YACA,OAAO;AAAA,UACX;AAAA,QACJ,OAAO;AAGH,iBAAO;AAAA,YACH;AAAA,YACA,SAAS,kBAAkB;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ,WAAW,SAAS,WAAW,GAAG;AAC9B,cAAM,CAAC,IAAI,IAAI;AACf,YAAI,mBAAmB,QAAQ,eAAe,GAAG;AAC7C,iBAAO;AAAA,YACH;AAAA,YACA,OAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,UACH;AAAA,UACA,SAAS,mBAAmB;AAAA,QAChC;AAAA,MACJ,OAAO;AACH,cAAM,wBAAwB,GAAG;AAAA,MACrC;AAAA,IACJ;AACA,QAAM,0BAA0B,YAAY,MAAM;AAC9C,UAAI,CAAC,KAAK,QAAQ;AACd,eAAO;AAAA,MACX;AACA,aAAO,KAAK,OAAO,CAAC,KAAK,QAAM;AAC3B,YAAI,CAAC,KAAK;AACN,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,KAAK;AACN,iBAAO;AAAA,QACX;AACA,eAAO,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG;AAAA,MACnC,GAAG,EAAE;AAAA,IACT;AACA,QAAM,aAAa,SAAS,MAAM,SAAS,IAAI,UAAU,OAAO;AAC5D,UAAI;AACA,cAAM,MAAM,UAAU,QAAQ;AAC9B,eAAO,GAAG,MAAM,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,oBAAoB,EAAE,IAAI,GAAG,GAAG,iBAAiB,oBAAoB,EAAE,CAAC,EAAE,QAAQ,IAAI,OAAO,GAAG,oBAAoB,MAAM,IAAI,GAAG,GAAG,iBAAiB,oBAAoB,MAAM,CAAC,EAAE,QAAQ,IAAI,OAAO,GAAG,oBAAoB,KAAK,IAAI,GAAG,GAAG,iBAAiB,oBAAoB,KAAK,CAAC,CAAC,GAAG,GAAG;AAAA,MAC/U,SAAS,KAAK;AACV,cAAM;AAAA,MACV;AAAA,IACJ;AACA,QAAM,aAAa,SAAS,MAAM,QAAQ,SAAS;AAC/C,UAAI;AACA,YAAI,cAAc;AAClB,YAAI,QAAQ;AACR,cAAI,CAAC,YAAY,WAAW,MAAM,GAAG;AACjC,mBAAO;AAAA,UACX;AACA,wBAAc,YAAY,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG,EAAE;AAAA,QACjE;AACA,sBAAc,YAAY,QAAQ,IAAI,OAAO,GAAG,iBAAiB,oBAAoB,EAAE,CAAC,IAAI,GAAG,GAAG,wBAAwB,iBAAiB,oBAAoB,EAAE,CAAC,CAAC,EAAE,QAAQ,IAAI,OAAO,GAAG,iBAAiB,oBAAoB,KAAK,CAAC,IAAI,GAAG,GAAG,wBAAwB,iBAAiB,oBAAoB,KAAK,CAAC,CAAC,EAAE,QAAQ,IAAI,OAAO,GAAG,iBAAiB,oBAAoB,MAAM,CAAC,IAAI,GAAG,GAAG,wBAAwB,iBAAiB,oBAAoB,MAAM,CAAC,CAAC;AACvc,YAAI,SAAS;AACT,wBAAc,YAAY,QAAQ,OAAO,EAAE;AAAA,QAC/C;AACA,eAAO;AAAA,MACX,SAAS,KAAK;AACV,cAAM;AAAA,MACV;AAAA,IACJ;AACA,QAAM,yBAAyB,CAAC,YAAY,YAAU;AAClD,UAAI,CAAC,YAAY;AACb,eAAO;AAAA,MACX;AACA,UAAI,SAAS;AACb,UAAI,WAAW,KAAK;AAChB,iBAAS;AAAA,MACb;AACA,UAAI,OAAO,WAAW,IAAI,GAAG;AACzB,iBAAS,OAAO,QAAQ,MAAM,EAAE;AAAA,MACpC;AACA,aAAO,WAAW,QAAQ,wBAAwB,OAAO;AAAA,IAC7D;AACA,QAAM,wBAAwB,CAAC,SAAS,YAAU;AAC9C,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAO,WAAW,SAAS,wBAAwB,OAAO;AAAA,IAC9D;AACA,QAAM,iBAAiB,CAACC,SAAQ,cAAY;AACxC,UAAI,mBAAmBA,SAAQ;AAC3B,YAAI;AACJ,YAAI,CAACA,QAAO,cAAc,WAAW,UAAU,GAAG;AAC9C,uBAAa,IAAI,SAASA,QAAO,aAAa,EAAE;AAAA,QACpD,OAAO;AACH,uBAAa,IAAI,SAAS,YAAYA,QAAO,aAAa,EAAE,EAAE;AAAA,QAClE;AACA,eAAO,GAAG,UAAU,GAAG,SAAS;AAAA,MACpC,WAAW,gBAAgBA,SAAQ;AAC/B,YAAI,CAAC,aAAa,KAAK,CAAC,iBAAiB,KAAK,mBAAmBA,SAAQ;AACrE,iBAAO,GAAGA,QAAO,aAAa,GAAG,SAAS;AAAA,QAC9C;AACA,eAAO,GAAGA,QAAO,UAAU,GAAG,SAAS;AAAA,MAC3C,OAAO;AACH,gBAAQ,KAAK,6DAA6DA,SAAQ,SAAS;AAC3F,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAM,SAAS,CAAC,WAAW,QAAM;AAC7B,UAAI,CAAC,WAAW;AACZ,cAAM,GAAG;AAAA,MACb;AAAA,IACJ;AACA,QAAM,QAAQ,CAAC,QAAM;AACjB,YAAM,IAAI,MAAM,GAAG,YAAY,KAAK,GAAG,EAAE;AAAA,IAC7C;AACA,QAAM,OAAO,CAAC,QAAM;AAChB,cAAQ,KAAK,GAAG,YAAY,KAAK,GAAG,EAAE;AAAA,IAC1C;AACA,aAAS,aAAa,MAAM;AACxB,UAAI;AACA,eAAO,KAAK,UAAU,MAAM,MAAM,CAAC;AAAA,MACvC,SAAS,GAAG;AACR,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAM,yBAAyB;AAC/B,aAAS,kBAAkB,KAAK;AAC5B,aAAO,uBAAuB,KAAK,GAAG;AAAA,IAC1C;AAEA,QAAM,wBAAwB,CAAC,OAAO,UAAQ;AAC1C,UAAI,CAAC,OAAO;AACR,eAAO;AAAA,MACX;AACA,YAAM,gBAAgB,CAAC,QAAM;AACzB,YAAI,QAAQ,KAAK;AACb,iBAAO;AAAA,QACX;AACA,YAAI,IAAI,WAAW,IAAI,GAAG;AACtB,iBAAO,IAAI,QAAQ,MAAM,EAAE;AAAA,QAC/B;AACA,YAAI,IAAI,WAAW,GAAG,GAAG;AACrB,gBAAM,kBAAkB,IAAI,MAAM,CAAC;AACnC,cAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,mBAAO,gBAAgB,MAAM,GAAG,EAAE;AAAA,UACtC;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,YAAM,kBAAkB,cAAc,KAAK;AAC3C,UAAI,CAAC,iBAAiB;AAClB,eAAO;AAAA,MACX;AACA,UAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,eAAO,GAAG,eAAe,GAAG,KAAK;AAAA,MACrC;AACA,aAAO,GAAG,eAAe,IAAI,KAAK;AAAA,IACtC;AACA,aAAS,oBAAoBC,MAAK;AAC9B,aAAOA,KAAI,QAAQ,QAAQ,EAAE,EAAE,QAAQ,SAAS,EAAE,EAAE,QAAQ,aAAa,GAAG;AAAA,IAChF;AAGA,aAAS,6BAA6B,UAAU,UAAU,CAAC,GAAG;AAC1D,UAAI,oBAAoB;AACxB,YAAM,EAAE,UAAU,CAAC,GAAG,YAAY,CAAC,GAAG,QAAQ,IAAI;AAClD,UAAI;AACJ,YAAM,gBAAgB,MAAI;AACtB,YAAI,gBAAgB,SAAS,UAAU;AACnC,cAAI,SAAS,SAAS,eAAe,UAAU,SAAS;AAEpD,mBAAO,oBAAoB,OAAO;AAAA,UACtC;AACA,iBAAO,SAAS,SAAS;AAAA,QAC7B,OAAO;AACH,iBAAO,SAAS,SAAS;AAAA,QAC7B;AAAA,MACJ;AACA,YAAM,gBAAgB,OAAO,KAAK,SAAS;AAC3C,UAAI,cAAc,CAAC;AAEnB,UAAI,CAAC,OAAO,KAAK,OAAO,EAAE,QAAQ;AAC9B,YAAI;AACJ,wBAAgB,oBAAoB,SAAS,YAAY,OAAO,SAAS,kBAAkB,OAAO,CAACC,MAAK,SAAO;AAC3G,cAAI;AACJ,gBAAM,OAAO,KAAK;AAElB,cAAI,cAAc,SAAS,IAAI,GAAG;AAC9B,6BAAiB,UAAU,IAAI;AAAA,UACnC,OAAO;AACH,gBAAI,aAAa,MAAM;AACnB,+BAAiB,KAAK;AAAA,YAC1B,OAAO;AACH,+BAAiB,KAAK;AAAA,YAC1B;AAAA,UACJ;AACA,UAAAA,KAAI,IAAI,IAAI;AAAA,YACR;AAAA,UACJ;AACA,iBAAOA;AAAA,QACX,GAAG,CAAC,CAAC,MAAM,CAAC;AAAA,MAChB;AAEA,aAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAM,YAAY,GAAG,IAAI;AAAA;AAAA,QAE/C,gBAAgB,cAAc,SAAS,GAAG,IAAI,UAAU,GAAG,IAAI,QAAQ,GAAG;AAAA,MAC9E,CAAC;AACL,YAAM,EAAE,aAAa,EAAE,MAAM,iBAAiB,MAAM,iBAAiB,MAAM,gBAAgB,GAAG,OAAO,aAAa,WAAW,EAAE,aAAa,GAAG,YAAY,eAAe,IAAI,SAAS;AACvL,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,sBAAsB;AAAA,QACtB,SAAS,UAAU,UAAU;AAAA,QAC7B;AAAA,QACA;AAAA,QACA,aAAa,sBAAsB,iBAAiB,eAAe;AAAA,QACnE;AAAA,QACA,aAAa,sBAAsB,YAAY,MAAM,YAAY,IAAI;AAAA,QACrE,gBAAgB,YAAY,OAAO;AAAA,QACnC,gBAAgB,YAAY,OAAO;AAAA,QACnC;AAAA,QACA,QAAQ,YAAY,OAAO,SAAS,SAAS,OAAO,IAAI,CAAC,UAAQ;AAAA,UACzD,QAAQ,KAAK;AAAA,UACb,YAAY,KAAK;AAAA,UACjB,SAAS,KAAK;AAAA,QAClB,EAAE;AAAA,QACN,SAAS,WAAW,OAAO,SAAS,QAAQ,IAAI,CAAC,YAAU;AAAA,UACnD,YAAY,OAAO;AAAA,UACnB,YAAY,OAAO;AAAA,UACnB,QAAQ,OAAO;AAAA,QACnB,EAAE;AAAA,MACV;AACA,WAAK,qBAAqB,SAAS,aAAa,OAAO,SAAS,mBAAmB,mBAAmB;AAClG,cAAM,oBAAoB,SAAS,SAAS;AAC5C,8BAAsB,UAAU,EAAE,CAAC,GAAG,qBAAqB;AAAA,UACvD;AAAA,QACJ,CAAC;AAAA,MACL;AACA,WAAK,sBAAsB,SAAS,aAAa,OAAO,SAAS,oBAAoB,eAAe;AAChG,cAAM,EAAE,MAAAC,OAAM,MAAM,KAAK,IAAI,SAAS,SAAS;AAC/C,8BAAsB,UAAU,EAAE,CAAC,GAAG,qBAAqB;AAAA,UACvD,eAAe,sBAAsBA,OAAM,IAAI;AAAA,UAC/C,mBAAmB;AAAA,QACvB,CAAC;AAAA,MACL;AACA,UAAI,gBAAgB,SAAS,UAAU;AACnC,yBAAiB,UAAU,EAAE,CAAC,GAAG,qBAAqB;AAAA,UAClD,YAAY,cAAc;AAAA,UAC1B,eAAe,SAAS,SAAS;AAAA,QACrC,CAAC;AAAA,MACL,OAAO;AACH,yBAAiB,UAAU,EAAE,CAAC,GAAG,qBAAqB;AAAA,UAClD,eAAe,cAAc;AAAA,QACjC,CAAC;AAAA,MACL;AACA,UAAI,gBAAgB;AAChB,cAAM,qBAAqB,sBAAsB,eAAe,MAAM,eAAe,IAAI;AACzF,uBAAe,iBAAiB;AAChC,uBAAe,qBAAqB,eAAe,QAAQ;AAAA,MAC/D;AACA,aAAO;AAAA,IACX;AACA,aAAS,mBAAmB,YAAY;AACpC,UAAI,iBAAiB,cAAc,WAAW,YAAY,SAAS,YAAY,GAAG;AAC9E,eAAO;AAAA,MACX,OAAO;AACH,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAM,SAAS;AACf,QAAI,SAAS,MAAM,OAAO;AAAA,MACtB,UAAU,QAAQ;AACd,aAAK,SAAS;AAAA,MAClB;AAAA,MACA,OAAO,MAAM;AACT,gBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,MACpC;AAAA,MACA,QAAQ,MAAM;AACV,gBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,MACpC;AAAA,MACA,SAAS,MAAM;AACX,gBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,MACpC;AAAA,MACA,WAAW,MAAM;AACb,gBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,MACpC;AAAA,MACA,QAAQ,MAAM;AACV,gBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,MACpC;AAAA,MACA,SAAS,MAAM;AACX,gBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,MACpC;AAAA,MACA,SAAS,MAAM;AACX,YAAI,YAAY,GAAG;AACf,kBAAQ,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,QACpC;AAAA,MACJ;AAAA,MACA,YAAY,QAAO;AACf,aAAK,SAAS;AAAA,MAClB;AAAA,IACJ;AACA,aAAS,aAAa,QAAQ;AAC1B,aAAO,IAAI,OAAO,MAAM;AAAA,IAC5B;AACA,QAAM,SAAS,aAAa,MAAM;AAGlC,mBAAe,YAAY,UAAU,aAAa;AAC9C,UAAI;AACA,cAAMD,OAAM,MAAM,SAAS;AAC3B,eAAOA;AAAA,MACX,SAAS,GAAG;AACR,SAAC,eAAe,KAAK,CAAC;AACtB;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,uBAAuB,MAAM,MAAM;AACxC,YAAM,UAAU;AAEhB,YAAM,eAAe,KAAK,QAAQ,SAAS,EAAE,EAAE,QAAQ,OAAO,EAAE;AAChE,YAAM,eAAe,KAAK,QAAQ,SAAS,EAAE,EAAE,QAAQ,OAAO,EAAE;AAEhE,aAAO,iBAAiB;AAAA,IAC5B;AACA,aAAS,aAAa,MAAM;AAExB,UAAIE,UAAS;AACb,UAAI,aAAa;AACjB,UAAI,UAAU;AACd,UAAI;AACJ,YAAM,UAAU,SAAS,qBAAqB,QAAQ;AACtD,eAAQ,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAI;AACnC,cAAM,IAAI,QAAQ,CAAC;AACnB,cAAM,YAAY,EAAE,aAAa,KAAK;AACtC,YAAI,aAAa,uBAAuB,WAAW,KAAK,GAAG,GAAG;AAC1D,UAAAA,UAAS;AACT,uBAAa;AACb;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAACA,SAAQ;AACT,cAAMC,SAAQ,KAAK;AACnB,QAAAD,UAAS,SAAS,cAAc,QAAQ;AACxC,QAAAA,QAAO,QAAQC,UAAS,OAAO,SAASA,OAAM,MAAM,OAAO,WAAW,WAAW;AACjF,YAAI,kBAAkB;AACtB,YAAI,KAAK,kBAAkB;AACvB,4BAAkB,KAAK,iBAAiB,KAAK,KAAK,KAAK,KAAK;AAC5D,cAAI,2BAA2B,mBAAmB;AAC9C,YAAAD,UAAS;AAAA,UACb,WAAW,OAAO,oBAAoB,UAAU;AAC5C,gBAAI,YAAY,mBAAmB,gBAAgB,QAAQ;AACvD,cAAAA,UAAS,gBAAgB;AAAA,YAC7B;AACA,gBAAI,aAAa,mBAAmB,gBAAgB,SAAS;AACzD,wBAAU,gBAAgB;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,CAACA,QAAO,KAAK;AACb,UAAAA,QAAO,MAAM,KAAK;AAAA,QACtB;AACA,YAAIC,UAAS,CAAC,iBAAiB;AAC3B,iBAAO,KAAKA,MAAK,EAAE,QAAQ,CAAC,SAAO;AAC/B,gBAAID,SAAQ;AACR,kBAAI,SAAS,WAAW,SAAS,SAAS;AACtC,gBAAAA,QAAO,IAAI,IAAIC,OAAM,IAAI;AAAA,cAE7B,WAAW,CAACD,QAAO,aAAa,IAAI,GAAG;AACnC,gBAAAA,QAAO,aAAa,MAAMC,OAAM,IAAI,CAAC;AAAA,cACzC;AAAA,YACJ;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,YAAM,mBAAmB,OAAO,MAChC,UAAQ;AACJ,qBAAa,SAAS;AACtB,cAAM,2BAA2B,MAAI;AACjC,eAAK,SAAS,OAAO,SAAS,MAAM,UAAU,SAAS;AACnD,aAAC,QAAQ,OAAO,SAAS,KAAK,qBAAqB,QAAQ,OAAO,SAAS,KAAK,gBAAgB,KAAK;AAAA,UACzG,OAAO;AACH,aAAC,QAAQ,OAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO,SAAS,KAAK,GAAG;AAAA,UAC1E;AAAA,QACJ;AAEA,YAAID,SAAQ;AACR,UAAAA,QAAO,UAAU;AACjB,UAAAA,QAAO,SAAS;AAChB,sBAAY,MAAI;AACZ,kBAAM,EAAE,mBAAmB,KAAK,IAAI;AACpC,gBAAI,kBAAkB;AAClB,eAACA,WAAU,OAAO,SAASA,QAAO,eAAeA,QAAO,WAAW,YAAYA,OAAM;AAAA,YACzF;AAAA,UACJ,CAAC;AACD,cAAI,QAAQ,OAAO,SAAS,YAAY;AACpC,kBAAM,SAAS,KAAK,KAAK;AACzB,gBAAI,kBAAkB,SAAS;AAC3B,oBAAMF,OAAM,MAAM;AAClB,uCAAyB;AACzB,qBAAOA;AAAA,YACX;AACA,qCAAyB;AACzB,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,iCAAyB;AAAA,MAC7B;AACA,MAAAE,QAAO,UAAU,iBAAiB,KAAK,MAAMA,QAAO,OAAO;AAC3D,MAAAA,QAAO,SAAS,iBAAiB,KAAK,MAAMA,QAAO,MAAM;AACzD,kBAAY,WAAW,MAAI;AACvB,yBAAiB,MAAM,IAAI,MAAM,kBAAkB,KAAK,GAAG,eAAe,CAAC;AAAA,MAC/E,GAAG,OAAO;AACV,aAAO;AAAA,QACH,QAAAA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,WAAW,MAAM;AAGtB,UAAI,OAAO;AACX,UAAI,aAAa;AACjB,YAAM,QAAQ,SAAS,qBAAqB,MAAM;AAClD,eAAQ,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAI;AACjC,cAAM,IAAI,MAAM,CAAC;AACjB,cAAM,WAAW,EAAE,aAAa,MAAM;AACtC,cAAM,UAAU,EAAE,aAAa,KAAK;AACpC,YAAI,YAAY,uBAAuB,UAAU,KAAK,GAAG,KAAK,YAAY,KAAK,MAAM,KAAK,GAAG;AACzF,iBAAO;AACP,uBAAa;AACb;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC,MAAM;AACP,eAAO,SAAS,cAAc,MAAM;AACpC,aAAK,aAAa,QAAQ,KAAK,GAAG;AAClC,YAAI,gBAAgB;AACpB,cAAMC,SAAQ,KAAK;AACnB,YAAI,KAAK,gBAAgB;AACrB,0BAAgB,KAAK,eAAe,KAAK,KAAKA,MAAK;AACnD,cAAI,yBAAyB,iBAAiB;AAC1C,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,YAAIA,UAAS,CAAC,eAAe;AACzB,iBAAO,KAAKA,MAAK,EAAE,QAAQ,CAAC,SAAO;AAC/B,gBAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,GAAG;AAClC,mBAAK,aAAa,MAAMA,OAAM,IAAI,CAAC;AAAA,YACvC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,YAAM,iBAAiB,CAAC,MACxB,UAAQ;AACJ,cAAM,yBAAyB,MAAI;AAC/B,eAAK,SAAS,OAAO,SAAS,MAAM,UAAU,SAAS;AACnD,aAAC,QAAQ,OAAO,SAAS,KAAK,qBAAqB,QAAQ,OAAO,SAAS,KAAK,gBAAgB,KAAK;AAAA,UACzG,OAAO;AACH,aAAC,QAAQ,OAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO,SAAS,KAAK,GAAG;AAAA,UAC1E;AAAA,QACJ;AAEA,YAAI,MAAM;AACN,eAAK,UAAU;AACf,eAAK,SAAS;AACd,sBAAY,MAAI;AACZ,kBAAM,EAAE,iBAAiB,KAAK,IAAI;AAClC,gBAAI,gBAAgB;AAChB,eAAC,QAAQ,OAAO,SAAS,KAAK,eAAe,KAAK,WAAW,YAAY,IAAI;AAAA,YACjF;AAAA,UACJ,CAAC;AACD,cAAI,MAAM;AAEN,kBAAMH,OAAM,KAAK,KAAK;AACtB,mCAAuB;AACvB,mBAAOA;AAAA,UACX;AAAA,QACJ;AACA,+BAAuB;AAAA,MAC3B;AACA,WAAK,UAAU,eAAe,KAAK,MAAM,KAAK,OAAO;AACrD,WAAK,SAAS,eAAe,KAAK,MAAM,KAAK,MAAM;AACnD,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,WAAWD,MAAK,MAAM;AAC3B,YAAM,EAAE,OAAAI,SAAQ,CAAC,GAAG,iBAAiB,IAAI;AACzC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAS;AAClC,cAAM,EAAE,QAAAD,SAAQ,WAAW,IAAI,aAAa;AAAA,UACxC,KAAAH;AAAA,UACA,IAAI;AAAA,UACJ,iBAAiB;AAAA,UACjB,OAAO,UAAU,EAAE;AAAA,YACf,eAAe;AAAA,UACnB,GAAGI,MAAK;AAAA,UACR;AAAA,UACA,kBAAkB;AAAA,QACtB,CAAC;AACD,sBAAc,SAAS,KAAK,YAAYD,OAAM;AAAA,MAClD,CAAC;AAAA,IACL;AAEA,aAAS,iBAAiB,MAAM;AAC5B,UAAI,CAAC,MAAM;AACP,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAClD;AACA,YAAM,eAAe,IAAI,SAAS,QAAQ,qBAAqB;AAC/D,aAAO,aAAa,IAAI,EAAE,KAAK,CAACF,SAAMA,IAAG,EAAE,MAAM,CAACH,WAAQ;AACtD,gBAAQ,MAAM,0BAA0B,IAAI,KAAKA,MAAK;AACtD,cAAMA;AAAA,MACV,CAAC;AAAA,IACL;AACA,QAAM,gBAAgB,YAAU;AAC5B,YAAM,cAAc,MAAM,iBAAiB,YAAY;AACvD,aAAO,YAAY,WAAW;AAAA,IAClC;AACA,QAAM,sBAAsB,OAAO,OAAO,MAAMO,gBAAa;AACzD,YAAM,OAAO,CAACL,MAAKM,UAAO;AACtB,eAAOD,YAAW,UAAU,MAAM,KAAKL,MAAKM,KAAI;AAAA,MACpD;AACA,YAAML,OAAM,MAAM,KAAK,OAAO,QAAQ,CAAC,CAAC;AACxC,UAAI,CAACA,QAAO,EAAEA,gBAAe,WAAW;AACpC,cAAM,gBAAgB,OAAO,UAAU,cAAc,MAAM,cAAc,IAAI;AAC7E,eAAO,cAAc,OAAO,QAAQ,CAAC,CAAC;AAAA,MAC1C;AACA,aAAOA;AAAA,IACX;AACA,QAAM,mBAAmB,OAAO,eAAe,eAAe,eAAe,QAAQ,CAAC,KAAK,IAAI,OAAO,eAAa;AAC/G,UAAI,cAAc,OAAO,SAAS,WAAW,kBAAkB;AAC3D,cAAM,aAAa,WAAW,iBAAiB,GAAG;AAClD,YAAI,cAAc,OAAO,eAAe,YAAY,SAAS,YAAY;AACrE,gBAAM,WAAW;AAAA,QACrB;AAAA,MACJ;AACA,UAAI;AACJ,UAAI;AACA,iBAAS,IAAI,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG;AACR,gBAAQ,MAAM,2BAA2B,CAAC;AAC1C,WAAG,IAAI,MAAM,gBAAgB,CAAC,EAAE,CAAC;AACjC;AAAA,MACJ;AACA,YAAM,WAAW,YAAU;AACvB,YAAI,cAAc,OAAO,SAAS,WAAW,OAAO;AAChD,iBAAO,CAAC,OAAO,SAAO,oBAAoB,OAAO,MAAM,UAAU;AAAA,QACrE;AACA,eAAO,OAAO,UAAU,cAAc,cAAc,IAAI;AAAA,MAC5D;AACA,YAAM,oBAAoB,OAAO,GAAG,WAAS;AACzC,YAAI;AACA,cACA;AACA,gBAAM,MAAM,MAAM,EAAE,OAAO,IAAI;AAC/B,gBAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,gBAAM,CAAC,MAAM,EAAE,IAAI,MAAM,QAAQ,IAAI;AAAA,YACjC,iBAAiB,MAAM;AAAA,YACvB,iBAAiB,IAAI;AAAA,UACzB,CAAC;AACD,gBAAM,gBAAgB;AAAA,YAClB,SAAS,CAAC;AAAA,YACV,QAAQ;AAAA,cACJ,SAAS,CAAC;AAAA,YACd;AAAA,UACJ;AACA,gBAAM,aAAa,OAAO,SAAS,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG;AACnE,gBAAM,WAAW,KAAK,SAAS,OAAO,QAAQ;AAC9C,cAAI;AACJ,gBAAM,SAAS,IAAI,GAAG,OAAO,+DAA+D,IAAI;AAAA,KAAQ;AAAA,YACpG;AAAA,YACA,0BAA0B,iDAAiD,gBAAgB,GAAG,cAAc,OAAO,SAAS,cAAc,oCAAoC,OAAO,gDAAgD;AAAA,UACzO,CAAC;AACD,iBAAO,iBAAiB,EAAE,cAAc,SAAS,cAAc,QAAQ,KAAK,SAAS,GAAG,YAAY,QAAQ;AAC5G,gBAAM,oBAAoB,cAAc,OAAO,WAAW,cAAc;AACxE,cAAI,SAAS,qBAAqB,MAAM,YAAY,GAAG;AACnD,kBAAM,YAAY,kBAAkB,MAAM,YAAY,CAAC,KAAK;AAC5D,eAAG,QAAW,SAAS;AACvB;AAAA,UACJ;AACA,aAAG,QAAW,iBAAiB;AAAA,QACnC,SAAS,GAAG;AACR,aAAG,aAAa,QAAQ,IAAI,IAAI,MAAM,2BAA2B,CAAC,EAAE,CAAC;AAAA,QACzE;AAAA,MACJ;AACA,eAAS,EAAE,KAAK,OAAOM,OAAI;AACvB,aAAK,SAAS,OAAO,SAAS,MAAM,MAAM,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,MAAM,OAAO,UAAU;AAC7G,iBAAO,WAAW,OAAO,MAAM;AAAA,YAC3B,OAAOA;AAAA,YACP,IAAI,MAAM,iBAAiB,IAAI;AAAA,UACnC,CAAC,EAAE,KAAK,OAAOR,YAAS;AACpB,kBAAMA,QAAO,SAAS;AACtB,eAAG,QAAWA,QAAO,SAAS;AAAA,UAClC,CAAC,EAAE,MAAM,CAAC,MAAI;AACV,eAAG,aAAa,QAAQ,IAAI,IAAI,MAAM,2BAA2B,CAAC,EAAE,CAAC;AAAA,UACzE,CAAC;AAAA,QACL;AACA,0BAAkBQ,IAAG,MAAM;AAAA,MAC/B,CAAC,EAAE,MAAM,CAAC,QAAM;AACZ,WAAG,GAAG;AAAA,MACV,CAAC;AAAA,IACL,IAAI,CAACP,MAAKQ,KAAIJ,QAAOC,gBAAa;AAC9B,MAAAG,IAAG,IAAI,MAAM,yDAAyD,CAAC;AAAA,IAC3E;AACA,QAAM,iBAAiB,OAAO,eAAe,eAAe,eAAe,QAAQ,CAACR,MAAK,SAAO;AAC5F,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAS;AAClC,yBAAiBA,MAAK,CAACF,QAAOW,mBAAgB;AAC1C,cAAIX,QAAO;AACP,mBAAOA,MAAK;AAAA,UAChB,OAAO;AACH,gBAAI,aAAa;AACjB,kBAAM,kBAAkB,QAAQ,OAAO,UAAU,cAAc,KAAK,UAAU,OAAO,SAAS,YAAY,YAAY,MAAM,gBAAgB,QAAQ,OAAO,UAAU,eAAe,KAAK,UAAU,OAAO,SAAS,aAAa,MAAM,CAAC;AACvO,kBAAM,eAAe,WAAW,cAAc,IAAIW;AAClD,oBAAQ,YAAY;AAAA,UACxB;AAAA,QACJ,GAAG,KAAK,OAAO,KAAK,UAAU;AAAA,MAClC,CAAC;AAAA,IACL,IAAI,CAACT,MAAK,SAAO;AACb,YAAM,IAAI,MAAM,uDAAuD;AAAA,IAC3E;AACA,mBAAe,WAAWA,MAAK,SAAS;AACpC,YAAM,EAAE,OAAO,QAAQ,IAAAU,IAAG,IAAI;AAC9B,YAAM,WAAW,MAAM,OAAOV,IAAG;AACjC,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,YAAMD,UAAS,IAAIW,IAAG,iBAAiB,MAAM;AAAA;AAAA,QAEzC,yBAAyB,OAAO,WAAWP,YAAS;AAChD,gBAAM,cAAc,IAAI,IAAI,WAAWH,IAAG,EAAE;AAC5C,iBAAO,WAAW,aAAa,OAAO;AAAA,QAC1C;AAAA,MACJ,CAAC;AACD,YAAMD,QAAO,KAAK,OAAO,cAAY;AACjC,cAAM,cAAc,IAAI,IAAI,WAAWC,IAAG,EAAE;AAC5C,cAAMD,UAAS,MAAM,WAAW,aAAa,OAAO;AACpD,eAAOA;AAAA,MACX,CAAC;AACD,aAAOA;AAAA,IACX;AAEA,aAAS,iBAAiB,eAAe,gBAAgB,KAAK;AAC1D,aAAO,SAAS,SAAS;AACrB,YAAI,YAAY,OAAO;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,OAAO,YAAY,aAAa;AAChC,cAAI,eAAe;AACf,mBAAO;AAAA,UACX,OAAO;AACH,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,YAAI,YAAY,MAAM;AAClB,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,OAAO,YAAY,UAAU;AACxC,iBAAO,UAAU,EAAE,CAAC,GAAG,gBAAgB,OAAO;AAAA,QAClD;AACA,cAAM,IAAI,MAAM,yBAAyB,GAAG,6CAA6C,OAAO,OAAO,EAAE;AAAA,MAC7G;AAAA,IACJ;AAEA,QAAM,+BAA+B,CAAC,YAAU;AAC5C,aAAO;AAAA,IACX;AAEA,YAAQ,kBAAkB;AAC1B,YAAQ,qBAAqB;AAC7B,YAAQ,0BAA0B;AAClC,YAAQ,2BAA2B;AACnC,YAAQ,eAAe;AACvB,YAAQ,eAAe;AACvB,YAAQ,mBAAmB;AAC3B,YAAQ,4BAA4B;AACpC,YAAQ,mBAAmB;AAC3B,YAAQ,mBAAmB;AAC3B,YAAQ,sBAAsB;AAC9B,YAAQ,YAAY;AACpB,YAAQ,gBAAgB;AACxB,YAAQ,WAAW;AACnB,YAAQ,SAAS;AACjB,YAAQ,0BAA0B;AAClC,YAAQ,kBAAkB;AAC1B,YAAQ,2BAA2B;AACnC,YAAQ,aAAa;AACrB,YAAQ,eAAe;AACvB,YAAQ,+BAA+B;AACvC,YAAQ,eAAe;AACvB,YAAQ,mBAAmB;AAC3B,YAAQ,aAAa;AACrB,YAAQ,aAAa;AACrB,YAAQ,QAAQ;AAChB,YAAQ,yBAAyB;AACjC,YAAQ,wBAAwB;AAChC,YAAQ,+BAA+B;AACvC,YAAQ,gBAAgB;AACxB,YAAQ,iBAAiB;AACzB,YAAQ,sBAAsB;AAC9B,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,qBAAqB;AAC7B,YAAQ,mBAAmB;AAC3B,YAAQ,oBAAoB;AAC5B,YAAQ,yBAAyB;AACjC,YAAQ,aAAa;AACrB,YAAQ,iBAAiB;AACzB,YAAQ,SAAS;AACjB,YAAQ,yBAAyB;AACjC,YAAQ,mBAAmB;AAC3B,YAAQ,aAAa;AACrB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,cAAc;AACtB,YAAQ,wBAAwB;AAChC,YAAQ,OAAO;AAAA;AAAA;;;ACpzBf,IAAAY,qBAAA;AAAA,kEAAAC,UAAA;AAAA;AAEA,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,YAAY;AAElB,QAAM,aAAa,CAAC,cAAY;AAC5B,YAAM,OAAO,UAAU,MAAM,GAAG,EAAE,CAAC,EAAE,YAAY;AACjD,aAAO,yFAAyF,IAAI,IAAI,SAAS;AAAA,IACrH;AACA,QAAM,mBAAmB,CAAC,WAAWC,eAAc,MAAM,qBAAmB;AACxE,YAAM,MAAM;AAAA,QACR,GAAG;AAAA,UACCA,cAAa,SAAS;AAAA,QAC1B,CAAC,KAAK,SAAS;AAAA,MACnB;AACA,cAAQ,IAAI,KAAK,SAAS,KAAK,UAAU,IAAI,CAAC,EAAE;AAChD,UAAI,KAAK,WAAW,SAAS,CAAC;AAC9B,0BAAoB,IAAI,KAAK;AAAA,GAA6B,gBAAgB,EAAE;AAC5E,aAAO,IAAI,KAAK,IAAI;AAAA,IACxB;AAEA,aAAS,WAAW;AAChB,iBAAW,OAAO,UAAU,SAAS,OAAO,QAAQ;AAChD,iBAAQ,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAI;AACrC,cAAI,SAAS,UAAU,CAAC;AACxB,mBAAQ,OAAO,OAAO,KAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,EAAG,QAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QACzG;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAM,iBAAiB;AAAA,MACnB,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,IACnB;AACA,QAAM,cAAc;AAAA,MAChB,CAAC,QAAQ,GAAG;AAAA,IAChB;AACA,QAAM,eAAe;AAAA,MACjB,CAAC,SAAS,GAAG;AAAA,MACb,CAAC,SAAS,GAAG;AAAA,IACjB;AACA,QAAM,eAAe,SAAS,CAAC,GAAG,gBAAgB,aAAa,YAAY;AAE3E,IAAAD,SAAQ,YAAY;AACpB,IAAAA,SAAQ,YAAY;AACpB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,WAAW;AACnB,IAAAA,SAAQ,eAAe;AACvB,IAAAA,SAAQ,eAAe;AACvB,IAAAA,SAAQ,mBAAmB;AAC3B,IAAAA,SAAQ,iBAAiB;AACzB,IAAAA,SAAQ,cAAc;AAAA;AAAA;;;AC9EtB,IAAAE,qBAAA;AAAA,oEAAAC,UAAA;AAAA;AAEA,QAAIC,aAAY;AAChB,QAAI,MAAM;AACV,QAAI,aAAa;AAEjB,QAAMC,gBAAe;AAErB,QAAMC,UAAS,IAAI,aAAaD,aAAY;AAE5C,aAASE,QAAO,WAAW,KAAK;AAC5B,UAAI,CAAC,WAAW;AACZ,QAAAC,OAAM,GAAG;AAAA,MACb;AAAA,IACJ;AACA,aAASA,OAAM,KAAK;AAChB,UAAI,eAAe,OAAO;AAEtB,YAAI,CAAC,IAAI,QAAQ,WAAWH,aAAY,GAAG;AACvC,cAAI,UAAU,GAAGA,aAAY,KAAK,IAAI,OAAO;AAAA,QACjD;AACA,cAAM;AAAA,MACV;AACA,YAAM,IAAI,MAAM,GAAGA,aAAY,KAAK,GAAG,EAAE;AAAA,IAC7C;AACA,aAASI,MAAK,KAAK;AACf,UAAI,eAAe,OAAO;AAEtB,YAAI,CAAC,IAAI,QAAQ,WAAWJ,aAAY,GAAG;AACvC,cAAI,UAAU,GAAGA,aAAY,KAAK,IAAI,OAAO;AAAA,QACjD;AACA,QAAAC,QAAO,KAAK,GAAG;AAAA,MACnB,OAAO;AACH,QAAAA,QAAO,KAAK,GAAG;AAAA,MACnB;AAAA,IACJ;AAEA,aAAS,cAAc,KAAK,MAAM;AAC9B,UAAI,IAAI,UAAU,CAAC,SAAO,SAAS,IAAI,MAAM,IAAI;AAC7C,YAAI,KAAK,IAAI;AAAA,MACjB;AACA,aAAO;AAAA,IACX;AACA,aAAS,QAAQ,YAAY;AACzB,UAAI,aAAa,cAAc,WAAW,SAAS;AAC/C,eAAO,GAAG,WAAW,IAAI,IAAI,WAAW,OAAO;AAAA,MACnD,WAAW,WAAW,cAAc,WAAW,OAAO;AAClD,eAAO,GAAG,WAAW,IAAI,IAAI,WAAW,KAAK;AAAA,MACjD,OAAO;AACH,eAAO,GAAG,WAAW,IAAI;AAAA,MAC7B;AAAA,IACJ;AACA,aAAS,sBAAsB,QAAQ;AACnC,aAAO,OAAO,OAAO,UAAU;AAAA,IACnC;AACA,aAAS,kBAAkB,QAAQ;AAC/B,aAAO,CAAC,OAAO,MAAM,SAAS,OAAO;AAAA,IACzC;AAEA,mBAAeI,aAAY,UAAU,aAAa;AAC9C,UAAI;AACA,cAAMC,OAAM,MAAM,SAAS;AAC3B,eAAOA;AAAA,MACX,SAAS,GAAG;AACR,SAAC,eAAeF,MAAK,CAAC;AACtB;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,SAAS,KAAK;AACnB,aAAO,OAAO,OAAO,QAAQ;AAAA,IACjC;AACA,QAAM,iBAAiB,OAAO,UAAU;AAExC,aAAS,cAAc,KAAK;AACxB,aAAO,eAAe,KAAK,GAAG,MAAM;AAAA,IACxC;AACA,aAASG,wBAAuB,MAAM,MAAM;AACxC,YAAM,UAAU;AAEhB,YAAM,eAAe,KAAK,QAAQ,SAAS,EAAE,EAAE,QAAQ,OAAO,EAAE;AAChE,YAAM,eAAe,KAAK,QAAQ,SAAS,EAAE,EAAE,QAAQ,OAAO,EAAE;AAEhE,aAAO,iBAAiB;AAAA,IAC5B;AACA,aAAS,aAAa,SAAS;AAC3B,aAAO,MAAM,QAAQ,OAAO,IAAI,UAAU;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,+BAA+B,UAAU;AAC9C,YAAM,yBAAyB;AAAA,QAC3B,KAAK;AAAA,QACL,MAAM;AAAA,QACN,YAAY;AAAA,MAChB;AACA,UAAI,IAAI,aAAa,KAAK,IAAI,iBAAiB,GAAG;AAC9C,eAAO,iBAAiB,WAAW;AAAA,UAC/B,KAAK,SAAS;AAAA,UACd,MAAM,SAAS;AAAA,UACf,YAAY,SAAS;AAAA,QACzB,IAAI;AAAA,MACR;AACA,UAAI,oBAAoB,UAAU;AAC9B,eAAO;AAAA,UACH,KAAK,SAAS,kBAAkB,uBAAuB;AAAA,UACvD,MAAM,SAAS,sBAAsB,uBAAuB;AAAA,UAC5D,YAAY,SAAS;AAAA,QACzB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAM,qBAAqB,CAAC,MAAM,YAAU;AAExC,UAAI;AACJ,UAAI,KAAK,SAAS,GAAG,GAAG;AACpB,qBAAa,KAAK,MAAM,GAAG,EAAE;AAAA,MACjC,OAAO;AACH,qBAAa;AAAA,MACjB;AACA,UAAI,QAAQ,WAAW,GAAG,GAAG;AACzB,kBAAU,QAAQ,MAAM,CAAC;AAAA,MAC7B;AACA,mBAAa,aAAa;AAC1B,aAAO;AAAA,IACX;AAEA,QAAM,gBAAgB,OAAO,eAAe,WAAW,aAAa;AACpE,QAAM,gBAAgB,MAAI;AACtB,UAAI;AAEA,eAAO,SAAS;AAAA,MACpB,SAAS,GAAG;AAER,eAAO;AAAA,MACX;AAAA,IACJ,GAAG;AACH,QAAM,SAAS;AACf,aAAS,wBAAwB,QAAQ,KAAK,KAAK;AAC/C,aAAO,eAAe,QAAQ,KAAK;AAAA,QAC/B,OAAO;AAAA,QACP,cAAc;AAAA,QACd,UAAU;AAAA,MACd,CAAC;AAAA,IACL;AACA,aAAS,mBAAmB,QAAQ,KAAK;AACrC,aAAO,OAAO,eAAe,KAAK,QAAQ,GAAG;AAAA,IACjD;AAIA,QAAI,CAAC,mBAAmB,eAAe,iCAAiC,GAAG;AACvE,8BAAwB,eAAe,mCAAmC,CAAC,CAAC;AAAA,IAChF;AACA,QAAM,gBAAgB,cAAc;AACpC,aAAS,oBAAoB,QAAQ;AACjC,UAAI,wBAAwB,yBAAyB,yBAAyB,yBAAyB,yBAAyB;AAChI,UAAI,mBAAmB,QAAQ,UAAU,KAAK,CAAC,mBAAmB,QAAQ,gBAAgB,GAAG;AACzF,gCAAwB,QAAQ,kBAAkB,OAAO,QAAQ;AAAA,MACrE;AACA,UAAI,CAAC,mBAAmB,QAAQ,gBAAgB,GAAG;AAC/C,gCAAwB,QAAQ,kBAAkB;AAAA,UAC9C,mBAAmB,CAAC;AAAA,UACpB,eAAe,CAAC;AAAA,UAChB,YAAY,CAAC;AAAA,UACb,WAAW,CAAC;AAAA,UACZ,sBAAsB,CAAC;AAAA,UACvB,mBAAmB,oBAAI,IAAI;AAAA,QAC/B,CAAC;AACD,gCAAwB,QAAQ,YAAY,OAAO,cAAc;AAAA,MACrE;AACA,UAAI;AACJ,OAAC,sBAAsB,yBAAyB,OAAO,gBAAgB,sBAAsB,OAAO,qBAAqB,uBAAuB,oBAAoB,CAAC;AACrK,UAAI;AACJ,OAAC,kBAAkB,0BAA0B,OAAO,gBAAgB,kBAAkB,OAAO,iBAAiB,wBAAwB,gBAAgB,CAAC;AACvJ,UAAI;AACJ,OAAC,eAAe,0BAA0B,OAAO,gBAAgB,eAAe,OAAO,cAAc,wBAAwB,aAAa,CAAC;AAC3I,UAAI;AACJ,OAAC,cAAc,0BAA0B,OAAO,gBAAgB,cAAc,OAAO,aAAa,wBAAwB,YAAY,CAAC;AACvI,UAAI;AACJ,OAAC,yBAAyB,0BAA0B,OAAO,gBAAgB,yBAAyB,OAAO,wBAAwB,wBAAwB,uBAAuB,CAAC;AACnL,UAAI;AACJ,OAAC,sBAAsB,0BAA0B,OAAO,gBAAgB,sBAAsB,OAAO,qBAAqB,wBAAwB,oBAAoB,oBAAI,IAAI;AAAA,IAClL;AACA,wBAAoB,aAAa;AACjC,wBAAoB,YAAY;AAChC,aAAS,4BAA4B;AACjC,oBAAc,eAAe,oBAAoB,CAAC;AAClD,oBAAc,eAAe,gBAAgB,CAAC;AAC9C,oBAAc,eAAe,aAAa,CAAC;AAC3C,oBAAc,eAAe,YAAY,CAAC;AAC1C,oBAAc,eAAe,uBAAuB,CAAC;AACrD,aAAO,KAAK,aAAa,EAAE,QAAQ,CAAC,QAAM;AACtC,eAAO,cAAc,GAAG;AAAA,MAC5B,CAAC;AAAA,IACL;AACA,aAAS,4BAA4B,oBAAoB;AACrD,oBAAc,eAAe,cAAc,KAAK,kBAAkB;AAAA,IACtE;AACA,aAAS,iCAAiC;AACtC,aAAO,cAAc,eAAe;AAAA,IACxC;AACA,aAAS,+BAA+B,uBAAuB,UAAU,IAAI,YAAY,GAAG;AACxF,UAAI,SAAS;AACT,sBAAc,eAAe,wBAAwB;AACrD,sBAAc,eAAe,gCAAgC;AAAA,MACjE;AAAA,IACJ;AAEA,aAAS,mBAAmB,QAAQ,KAAK;AACrC,UAAI,OAAO,QAAQ,UAAU;AACzB,cAAM,SAAS,OAAO,GAAG;AACzB,YAAI,QAAQ;AACR,iBAAO;AAAA,YACH,OAAO,OAAO,GAAG;AAAA,YACjB;AAAA,UACJ;AAAA,QACJ,OAAO;AACH,gBAAM,aAAa,OAAO,KAAK,MAAM;AACrC,qBAAW,aAAa,YAAW;AAC/B,kBAAM,CAAC,kBAAkB,CAAC,IAAI,UAAU,MAAM,GAAG;AACjD,kBAAM,OAAO,GAAG,gBAAgB,IAAI,GAAG;AACvC,kBAAM,iBAAiB,OAAO,IAAI;AAClC,gBAAI,gBAAgB;AAChB,qBAAO;AAAA,gBACH,OAAO;AAAA,gBACP,KAAK;AAAA,cACT;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,OAAO;AAAA,YACP;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACxC;AAAA,IACJ;AACA,QAAM,oBAAoB,MAAI,aAAa,eAAe;AAC1D,QAAM,oCAAoC,CAAC,YAAY,aAAW;AAE9D,YAAM,YAAY,QAAQ,UAAU;AACpC,YAAM,gBAAgB,mBAAmB,UAAU,SAAS,EAAE;AAE9D,UAAI,iBAAiB,CAAC,cAAc,WAAW,aAAa,cAAc,WAAW,SAAS,GAAG;AAC7F,sBAAc,UAAU,WAAW,SAAS;AAAA,MAChD;AACA,UAAI,eAAe;AACf,eAAO;AAAA,MACX;AAEA,UAAI,aAAa,cAAc,WAAW,SAAS,GAAG;AAClD,cAAM,EAAE,QAAQ,IAAI,YAAY,gBAAgBR,WAAU,iCAAiC,YAAY;AAAA,UACnG;AAAA,QACJ,CAAC;AACD,cAAM,0BAA0B,QAAQ,aAAa;AACrD,cAAM,8BAA8B,mBAAmB,aAAa,eAAe,YAAY,uBAAuB,EAAE;AACxH,aAAK,+BAA+B,OAAO,SAAS,4BAA4B,aAAa,SAAS;AAClG,iBAAO;AAAA,QACX;AAAA,MACJ;AACA;AAAA,IACJ;AACA,QAAM,oCAAoC,CAAC,eAAa,kCAAkC,YAAY,aAAa,eAAe,UAAU;AAC5I,QAAM,oCAAoC,CAAC,YAAY,qBAAmB;AACtE,YAAM,YAAY,QAAQ,UAAU;AACpC,mBAAa,eAAe,WAAW,SAAS,IAAI;AACpD,aAAO,aAAa,eAAe;AAAA,IACvC;AACA,QAAM,oBAAoB,CAAC,gBAAc;AACrC,mBAAa,eAAe,aAAaA,WAAU,SAAS,CAAC,GAAG,aAAa,eAAe,YAAY,WAAW;AACnH,aAAO,MAAI;AACP,cAAM,OAAO,OAAO,KAAK,WAAW;AACpC,mBAAW,OAAO,MAAK;AACnB,iBAAO,aAAa,eAAe,WAAW,GAAG;AAAA,QACrD;AAAA,MACJ;AAAA,IACJ;AACA,QAAM,wBAAwB,CAAC,MAAM,eAAa;AAC9C,YAAM,iBAAiB,cAAc,gBAAgB,IAAI;AACzD,YAAM,eAAe,cAAc,cAAc;AACjD,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAKA,QAAM,wBAAwB,CAAC,YAAU;AACrC,YAAM,EAAE,kBAAkB,IAAI,aAAa;AAC3C,cAAQ,QAAQ,CAAC,WAAS;AACtB,YAAI,kBAAkB,UAAU,CAAC,MAAI,EAAE,SAAS,OAAO,IAAI,MAAM,IAAI;AACjE,4BAAkB,KAAK,MAAM;AAAA,QACjC,OAAO;AACH,UAAAK,MAAK,cAAc,OAAO,IAAI,uBAAuB;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAM,uBAAuB,MAAI,aAAa,eAAe;AAC7D,QAAM,eAAe,CAAC,OAAK,cAAc,eAAe,kBAAkB,IAAI,EAAE;AAChF,QAAM,eAAe,CAAC,OAAK,cAAc,eAAe,kBAAkB,IAAI,IAAI,IAAI;AAEtF,QAAM,gBAAgB;AACtB,QAAM,sBAAsB;AAW5B,QAAM,kBAAkB;AACxB,QAAM,QAAQ,UAAU,eAAe,SAAS,eAAe;AAC/D,QAAM,oBAAoB;AAC1B,QAAM,yBAAyB;AAC/B,QAAM,uBAAuB;AAC7B,QAAM,4BAA4B,MAAM,sBAAsB,IAAI,oBAAoB;AACtF,QAAM,kBAAkB,SAAS,yBAAyB,SAAS,yBAAyB;AAC5F,QAAM,uBAAuB,MAAM,iBAAiB,IAAI,oBAAoB;AAC5E,QAAM,aAAa,QAAQ,oBAAoB,SAAS,oBAAoB;AAC5E,QAAM,mBAAmB,GAAG,iBAAiB;AAC7C,QAAM,cAAc,YAAY,gBAAgB,WAAW,gBAAgB,WAAW,gBAAgB,OAAO,UAAU,KAAK,KAAK;AACjI,QAAM,cAAc,SAAS,WAAW,cAAc,WAAW;AACjE,QAAM,mBAAmB,IAAI,sBAAsB,QAAQ,sBAAsB,QAAQ,sBAAsB;AAC/G,QAAM,aAAa,WAAW,gBAAgB,GAAG,eAAe,IAAI,KAAK;AACzE,QAAM,OAAO;AACb,QAAM,iBAAiB,SAAS,IAAI,QAAQ,UAAU,IAAI,WAAW;AACrE,QAAM,YAAY;AAClB,QAAM,YAAY,SAAS,SAAS;AACpC,QAAM,YAAY;AAClB,QAAM,YAAY,SAAS,SAAS;AACpC,QAAM,OAAO;AACb,QAAM,QAAQ,IAAI,SAAS,GAAG,WAAW;AACzC,QAAM,cAAc,IAAI,iBAAiB,QAAQ,iBAAiB,QAAQ,iBAAiB;AAC3F,QAAM,YAAY,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK;AACxD,QAAM,QAAQ,IAAI,SAAS,GAAG,WAAW;AACzC,QAAM,SAAS,IAAI,IAAI,OAAO,WAAW;AACzC,QAAM,aAAa,IAAI,IAAI,QAAQ,SAAS;AAE5C,QAAM,OAAO;AAUb,aAAS,WAAW,QAAQ;AACxB,aAAO,IAAI,OAAO,MAAM;AAAA,IAC5B;AACA,aAAS,WAAW,SAAS;AACzB,aAAO,CAAC,WAAW,QAAQ,YAAY,MAAM,OAAO,YAAY;AAAA,IACpE;AACA,aAAS,QAAQ,KAAK;AAClB,aAAO,CAAC,MAAI,IAAI,OAAO,CAAC,GAAGI,OAAIA,GAAE,CAAC,GAAG,CAAC;AAAA,IAC1C;AACA,aAAS,kBAAkB,kBAAkB;AACzC,aAAO,iBAAiB,MAAM,WAAW,UAAU,CAAC;AAAA,IACxD;AACA,aAAS,eAAe,OAAO,OAAO,OAAOC,aAAY;AACrD,YAAMC,eAAc,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK;AAC9C,UAAID,aAAY;AACZ,eAAO,GAAGC,YAAW,IAAID,WAAU;AAAA,MACvC;AACA,aAAOC;AAAA,IACX;AAUA,aAAS,YAAY,OAAO;AACxB,aAAO,MAAM,QAAQ,WAAW,WAAW,GAAG,CAAC,QAAQ,MAAM,WAAW,WAAW,WAAW,iBAAiB,YAAY,IAAI,SAAS,SAAS,SAAS,iBAAe;AACrK,YAAI,WAAW,SAAS,GAAG;AACvB,iBAAO;AAAA,QACX,WAAW,WAAW,SAAS,GAAG;AAC9B,iBAAO,KAAK,SAAS;AAAA,QACzB,WAAW,WAAW,SAAS,GAAG;AAC9B,iBAAO,KAAK,SAAS,IAAI,SAAS;AAAA,QACtC,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AACA,YAAI,WAAW,OAAO,GAAG;AACrB,eAAK;AAAA,QACT,WAAW,WAAW,OAAO,GAAG;AAC5B,eAAK,IAAI,OAAO,OAAO,IAAI,CAAC;AAAA,QAChC,WAAW,WAAW,OAAO,GAAG;AAC5B,eAAK,IAAI,OAAO,IAAI,OAAO,OAAO,IAAI,CAAC;AAAA,QAC3C,WAAW,cAAc;AACrB,eAAK,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,YAAY;AAAA,QAC3D,OAAO;AACH,eAAK,KAAK,EAAE;AAAA,QAChB;AACA,eAAO,GAAG,IAAI,IAAI,EAAE,GAAG,KAAK;AAAA,MAChC,CAAC;AAAA,IACL;AACA,aAAS,oBAAoB,OAAO;AAChC,aAAO,MAAM,QAAQ,WAAW,cAAc,GAAG,QAAQ;AAAA,IAC7D;AACA,aAAS,eAAe,OAAO;AAC3B,aAAO,MAAM,QAAQ,WAAW,SAAS,GAAG,KAAK;AAAA,IACrD;AACA,aAAS,eAAe,OAAO;AAC3B,aAAO,MAAM,QAAQ,WAAW,SAAS,GAAG,KAAK;AAAA,IACrD;AACA,aAAS,YAAY,OAAO;AACxB,aAAO,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,CAAC,iBAAe,aAAa,QAAQ,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,OAAO,OAAOD,gBAAa;AAC3H,YAAI,WAAW,KAAK,GAAG;AACnB,iBAAO;AAAA,QACX,WAAW,WAAW,KAAK,GAAG;AAC1B,iBAAO,KAAK,KAAK,SAAS,OAAO,KAAK,IAAI,CAAC;AAAA,QAC/C,WAAW,WAAW,KAAK,GAAG;AAC1B,cAAI,UAAU,KAAK;AACf,mBAAO,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,UAC/D,OAAO;AACH,mBAAO,KAAK,KAAK,IAAI,KAAK,OAAO,OAAO,KAAK,IAAI,CAAC;AAAA,UACtD;AAAA,QACJ,WAAWA,aAAY;AACnB,cAAI,UAAU,KAAK;AACf,gBAAI,UAAU,KAAK;AACf,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAIA,WAAU,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,YAC7F,OAAO;AACH,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAIA,WAAU,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,YACpF;AAAA,UACJ,OAAO;AACH,mBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAIA,WAAU,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,UAC3E;AAAA,QACJ,OAAO;AACH,cAAI,UAAU,KAAK;AACf,gBAAI,UAAU,KAAK;AACf,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,YAC/E,OAAO;AACH,qBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,YACtE;AAAA,UACJ;AACA,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,QAC7D;AAAA,MACJ,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACpB;AACA,aAAS,YAAY,OAAO;AACxB,aAAO,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,CAAC,iBAAe,aAAa,QAAQ,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,OAAO,OAAOA,gBAAa;AAC3H,YAAI,WAAW,KAAK,GAAG;AACnB,iBAAO;AAAA,QACX,WAAW,WAAW,KAAK,GAAG;AAC1B,iBAAO,KAAK,KAAK,SAAS,OAAO,KAAK,IAAI,CAAC;AAAA,QAC/C,WAAW,WAAW,KAAK,GAAG;AAC1B,iBAAO,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,QAC/D,WAAWA,aAAY;AACnB,iBAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAIA,WAAU,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,QACpF;AACA,eAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,MACtE,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACpB;AACA,aAAS,aAAa,OAAO;AACzB,aAAO,MAAM,MAAM,KAAK,EAAE,IAAI,CAAC,iBAAe,aAAa,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG,CAAC,KAAKE,OAAM,OAAO,OAAO,OAAOF,gBAAa;AACpI,cAAM,WAAW,WAAW,KAAK;AACjC,cAAM,WAAW,YAAY,WAAW,KAAK;AAC7C,cAAM,WAAW,YAAY,WAAW,KAAK;AAC7C,YAAIE,UAAS,OAAO,UAAU;AAC1B,UAAAA,QAAO;AAAA,QACX;AACA,QAAAF,cAAa;AACb,YAAI,UAAU;AACV,cAAIE,UAAS,OAAOA,UAAS,KAAK;AAE9B,mBAAO;AAAA,UACX,OAAO;AAEH,mBAAO;AAAA,UACX;AAAA,QACJ,WAAWA,SAAQ,UAAU;AAEzB,cAAI,UAAU;AACV,oBAAQ;AAAA,UACZ;AACA,kBAAQ;AACR,cAAIA,UAAS,KAAK;AAGd,YAAAA,QAAO;AACP,gBAAI,UAAU;AACV,sBAAQ,OAAO,KAAK,IAAI;AACxB,sBAAQ;AACR,sBAAQ;AAAA,YACZ,OAAO;AACH,sBAAQ,OAAO,KAAK,IAAI;AACxB,sBAAQ;AAAA,YACZ;AAAA,UACJ,WAAWA,UAAS,MAAM;AAGtB,YAAAA,QAAO;AACP,gBAAI,UAAU;AACV,sBAAQ,OAAO,KAAK,IAAI;AAAA,YAC5B,OAAO;AACH,sBAAQ,OAAO,KAAK,IAAI;AAAA,YAC5B;AAAA,UACJ;AACA,cAAIA,UAAS,KAAK;AACd,YAAAF,cAAa;AAAA,UACjB;AACA,iBAAO,GAAGE,QAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAGF,WAAU;AAAA,QACzD,WAAW,UAAU;AACjB,iBAAO,KAAK,KAAK,OAAOA,WAAU,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,QAC5D,WAAW,UAAU;AACjB,iBAAO,KAAK,KAAK,IAAI,KAAK,KAAKA,WAAU,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,QAC5E;AACA,eAAO;AAAA,MACX,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACpB;AACA,aAAS,UAAU,OAAO;AACtB,aAAO,MAAM,KAAK,EAAE,QAAQ,WAAW,IAAI,GAAG,EAAE;AAAA,IACpD;AACA,aAAS,UAAU,kBAAkB;AACjC,aAAO,iBAAiB,KAAK,EAAE,QAAQ,WAAW,IAAI,GAAG,EAAE;AAAA,IAC/D;AAUA,aAAS,YAAY,WAAW,aAAa;AACzC,kBAAY,OAAO,SAAS,KAAK;AACjC,oBAAc,OAAO,WAAW,KAAK;AACrC,UAAI,YAAY,aAAa;AACzB,eAAO;AAAA,MACX;AACA,UAAI,cAAc,aAAa;AAC3B,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,aAAS,kBAAkB,WAAW,aAAa;AAC/C,YAAM,EAAE,YAAY,gBAAgB,IAAI;AACxC,YAAM,EAAE,YAAY,kBAAkB,IAAI;AAC1C,UAAI,oBAAoB,UAAa,QAAQ,iBAAiB,GAAG;AAC7D,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,eAAe,KAAK,sBAAsB,QAAW;AAC7D,eAAO;AAAA,MACX;AACA,UAAI,oBAAoB,UAAa,sBAAsB,QAAW;AAClE,eAAO;AAAA,MACX;AACA,eAAQ,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK,GAAG,KAAI;AACnD,cAAM,eAAe,gBAAgB,CAAC;AACtC,cAAM,iBAAiB,kBAAkB,CAAC;AAC1C,YAAI,iBAAiB,gBAAgB;AACjC;AAAA,QACJ;AACA,YAAI,iBAAiB,UAAa,mBAAmB,QAAW;AAC5D,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,cAAc;AACf,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,gBAAgB;AACjB,iBAAO;AAAA,QACX;AACA,eAAO,YAAY,cAAc,cAAc;AAAA,MACnD;AACA,aAAO;AAAA,IACX;AACA,aAAS,eAAe,WAAW,aAAa;AAC5C,aAAO,YAAY,UAAU,OAAO,YAAY,KAAK,KAAK,YAAY,UAAU,OAAO,YAAY,KAAK,KAAK,YAAY,UAAU,OAAO,YAAY,KAAK,KAAK,kBAAkB,WAAW,WAAW;AAAA,IAC5M;AACA,aAAS,GAAG,WAAW,aAAa;AAChC,aAAO,UAAU,YAAY,YAAY;AAAA,IAC7C;AACA,aAAS,QAAQ,WAAW,aAAa;AACrC,cAAO,UAAU,UAAS;AAAA,QACtB,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,GAAG,WAAW,WAAW;AAAA,QACpC,KAAK;AACD,iBAAO,eAAe,WAAW,WAAW,IAAI;AAAA,QACpD,KAAK;AACD,iBAAO,GAAG,WAAW,WAAW,KAAK,eAAe,WAAW,WAAW,IAAI;AAAA,QAClF,KAAK;AACD,iBAAO,eAAe,WAAW,WAAW,IAAI;AAAA,QACpD,KAAK;AACD,iBAAO,GAAG,WAAW,WAAW,KAAK,eAAe,WAAW,WAAW,IAAI;AAAA,QAClF,KAAK,QACD;AAEI,iBAAO;AAAA,QACX;AAAA,QACJ;AACI,iBAAO;AAAA,MACf;AAAA,IACJ;AAUA,aAAS,sBAAsB,OAAO;AAClC,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA;AAAA,QAAa;AAAA,QAAc;AAAA,MAAS,EAAE,KAAK;AAAA,IAC/C;AACA,aAAS,WAAW,OAAO;AACvB,aAAO;AAAA;AAAA;AAAA,QAEP;AAAA;AAAA;AAAA,QAEA;AAAA;AAAA;AAAA,QAEA;AAAA;AAAA;AAAA,QAEA;AAAA,MAAc,EAAE,MAAM,KAAK,CAAC,EAAE,MAAM,KAAK,EAAE,KAAK,GAAG;AAAA,IACvD;AACA,aAAS,QAAQ,SAAS,OAAO;AAC7B,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AAEA,YAAM,mBAAmB,kBAAkB,OAAO;AAClD,UAAI,CAAC,kBAAkB;AAEnB,eAAO;AAAA,MACX;AACA,YAAM,CAAC,EAAE,iBAAiB,EAAE,cAAc,cAAc,cAAc,iBAAiB,IAAI;AAC3F,YAAM,cAAc;AAAA,QAChB,UAAU;AAAA,QACV,SAAS,eAAe,cAAc,cAAc,cAAc,iBAAiB;AAAA,QACnF,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY,qBAAqB,OAAO,SAAS,kBAAkB,MAAM,GAAG;AAAA,MAChF;AAEA,YAAM,WAAW,MAAM,MAAM,IAAI;AACjC,iBAAW,WAAW,UAAS;AAC3B,cAAM,iBAAiB,QAAQ,KAAK;AACpC,YAAI,CAAC,gBAAgB;AAGjB,iBAAO;AAAA,QACX;AAEA,YAAI,mBAAmB,OAAO,mBAAmB,KAAK;AAClD,iBAAO;AAAA,QACX;AACA,YAAI;AAEA,gBAAM,iBAAiB,WAAW,cAAc;AAIhD,cAAI,CAAC,eAAe,KAAK,GAAG;AAExB,mBAAO;AAAA,UACX;AACA,gBAAM,yBAAyB,eAAe,MAAM,GAAG,EAAE,IAAI,CAAC,iBAAe,sBAAsB,YAAY,CAAC,EAC/G,KAAK,GAAG;AAET,cAAI,CAAC,uBAAuB,KAAK,GAAG;AAChC,mBAAO;AAAA,UACX;AAEA,gBAAM,cAAc,uBAAuB,MAAM,KAAK,EAAE,IAAI,CAACG,gBAAa,UAAUA,WAAU,CAAC,EAC9F,OAAO,OAAO;AAGf,cAAI,YAAY,WAAW,GAAG;AAC1B;AAAA,UACJ;AACA,cAAI,oBAAoB;AACxB,qBAAWA,eAAc,aAAY;AACjC,kBAAM,sBAAsB,kBAAkBA,WAAU;AAExD,gBAAI,CAAC,qBAAqB;AACtB,kCAAoB;AACpB;AAAA,YACJ;AACA,kBAAM,CAAC,EAAE,eAAe,EAAE,YAAY,YAAY,YAAY,eAAe,IAAI;AACjF,kBAAM,YAAY;AAAA,cACd,UAAU;AAAA,cACV,SAAS,eAAe,YAAY,YAAY,YAAY,eAAe;AAAA,cAC3E,OAAO;AAAA,cACP,OAAO;AAAA,cACP,OAAO;AAAA,cACP,YAAY,mBAAmB,OAAO,SAAS,gBAAgB,MAAM,GAAG;AAAA,YAC5E;AAEA,gBAAI,CAAC,QAAQ,WAAW,WAAW,GAAG;AAClC,kCAAoB;AACpB;AAAA,YACJ;AAAA,UACJ;AAEA,cAAI,mBAAmB;AACnB,mBAAO;AAAA,UACX;AAAA,QACJ,SAAS,GAAG;AAER,kBAAQ,MAAM,yCAAyC,cAAc,MAAM,CAAC;AAC5E;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,YAAY,WAAW,MAAM,MAAM,eAAe;AACvD,UAAI;AACJ,UAAI,SAAS,WAAW;AAEpB,cAAM,UAAU;AAAA,MACpB,WAAW,SAAS,WAAW;AAC3B,cAAM,MAAI,QAAQ,QAAQ,UAAU,GAAG;AAAA,MAC3C,OAAO;AACH,cAAM,MAAI,QAAQ,QAAQ,MAAI;AACtB,gBAAM,IAAI,MAAM,uBAAuB,IAAI,IAAI;AAAA,QACnD,CAAC;AAAA,MACT;AACA,UAAI,oBAAoB,kBAAkB;AAC1C,aAAOb,WAAU,SAAS;AAAA,QACtB,MAAM,CAAC;AAAA,QACP,OAAO,CAAC;AAAA,QACR;AAAA,QACA,SAAS;AAAA,MACb,GAAG,WAAW;AAAA,QACV,aAAaA,WAAU,SAAS;AAAA,UAC5B,iBAAiB,IAAI,UAAU,OAAO;AAAA,UACtC,WAAW;AAAA,UACX,OAAO;AAAA,UACP,eAAe;AAAA,QACnB,GAAG,UAAU,WAAW;AAAA,QACxB;AAAA,QACA,SAAS,aAAa,OAAO,SAAS,UAAU,WAAW,SAAS,YAAY,OAAO;AAAA,QACvF,UAAU,qBAAqB,UAAU,YAAY,OAAO,qBAAqB;AAAA,QACjF,OAAO,MAAM,QAAQ,UAAU,KAAK,IAAI,UAAU,QAAQ;AAAA,WACrD,mBAAmB,UAAU,UAAU,OAAO,mBAAmB;AAAA,QACtE;AAAA,QACA,YAAY,sBAAsB,UAAU,aAAa,OAAO,sBAAsB,kBAAkB;AAAA,MAC5G,CAAC;AAAA,IACL;AACA,aAAS,mBAAmB,eAAe,aAAa;AACpD,YAAM,YAAY,YAAY,UAAU,CAAC;AACzC,YAAM,OAAO,YAAY;AACzB,YAAM,aAAa,OAAO,KAAK,SAAS,EAAE,OAAO,CAACO,MAAK,YAAU;AAC7D,cAAM,iBAAiB,aAAa,UAAU,OAAO,CAAC;AACtD,QAAAA,KAAI,OAAO,IAAIA,KAAI,OAAO,KAAK,CAAC;AAChC,uBAAe,QAAQ,CAAC,gBAAc;AAClC,UAAAA,KAAI,OAAO,EAAE,KAAK,YAAY,aAAa,MAAM,SAAS,YAAY,aAAa,CAAC;AAAA,QACxF,CAAC;AACD,eAAOA;AAAA,MACX,GAAG,CAAC,CAAC;AACL,YAAM,SAASP,WAAU,SAAS,CAAC,GAAG,cAAc,MAAM;AAC1D,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,aAAW;AACxC,YAAI,CAAC,OAAO,QAAQ,GAAG;AACnB,iBAAO,QAAQ,IAAI,WAAW,QAAQ;AAAA,QAC1C,OAAO;AACH,qBAAW,QAAQ,EAAE,QAAQ,CAAC,yBAAuB;AACjD,kBAAM,gBAAgB,OAAO,QAAQ,EAAE,KAAK,CAAC,cAAY,UAAU,YAAY,qBAAqB,OAAO;AAC3G,gBAAI,CAAC,eAAe;AAChB,qBAAO,QAAQ,EAAE,KAAK,oBAAoB;AAAA,YAC9C;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,UAAU,GAAG,GAAG;AACrB,YAAM,0BAA0B,CAAC,YAAU;AACvC,cAAM,kBAAkB,CAAC,OAAO,MAAM,OAAO,OAAO,CAAC;AACrD,YAAI,iBAAiB;AACjB,gBAAM,WAAW,QAAQ,MAAM,GAAG;AAClC,cAAI,eAAe;AACnB,mBAAQ,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAI;AACxC,4BAAgB;AAAA,UACpB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,wBAAwB,CAAC,GAAG,KAAK,wBAAwB,CAAC,CAAC,EAAE,GAAG;AACxE,eAAO;AAAA,MACX,OAAO;AACH,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAM,cAAc,CAAC,iBAAiBc,QAAK;AACvC,YAAM,WAAWA,OAAM,SAAS,MAAM,KAAK;AACvC,eAAO,UAAU,MAAM,GAAG;AAAA,MAC9B;AACA,aAAO,OAAO,KAAK,eAAe,EAAE,OAAO,CAAC,MAAM,QAAM;AACpD,YAAI,CAAC,MAAM;AACP,iBAAO;AAAA,QACX;AACA,YAAI,SAAS,MAAM,GAAG,GAAG;AACrB,iBAAO;AAAA,QACX;AAEA,YAAI,SAAS,KAAK;AACd,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GAAG,CAAC;AAAA,IACR;AACA,QAAM,WAAW,CAAC,WAAS;AACvB,aAAO,QAAQ,OAAO,MAAM,KAAK,OAAO,OAAO,QAAQ;AAAA,IAC3D;AACA,QAAM,YAAY,CAAC,WAAS;AACxB,aAAO,QAAQ,OAAO,OAAO;AAAA,IACjC;AACA,aAAS,mCAAmC,eAAe,OAAO,SAAS;AACvE,YAAM,WAAW,cAAc,KAAK,EAAE,OAAO;AAC7C,YAAM,WAAW,SAAS,MAAM,KAAK;AACjC,eAAO,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK,UAAU,MAAM,GAAG;AAAA,MAC3D;AACA,aAAO,YAAY,cAAc,KAAK,EAAE,OAAO,GAAG,QAAQ;AAAA,IAC9D;AACA,aAAS,kCAAkC,eAAe,OAAO,SAAS;AACtE,YAAM,WAAW,cAAc,KAAK,EAAE,OAAO;AAC7C,YAAM,WAAW,SAAS,MAAM,KAAK;AACjC,cAAM,oBAAoB,CAAC,WAAS;AAChC,iBAAO,SAAS,MAAM,KAAK,UAAU,MAAM;AAAA,QAC/C;AACA,YAAI,kBAAkB,SAAS,GAAG,CAAC,GAAG;AAClC,cAAI,kBAAkB,SAAS,IAAI,CAAC,GAAG;AACnC,mBAAO,QAAQ,UAAU,MAAM,GAAG,CAAC;AAAA,UACvC,OAAO;AACH,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,YAAI,kBAAkB,SAAS,IAAI,CAAC,GAAG;AACnC,iBAAO;AAAA,QACX;AACA,eAAO,UAAU,MAAM,GAAG;AAAA,MAC9B;AACA,aAAO,YAAY,cAAc,KAAK,EAAE,OAAO,GAAG,QAAQ;AAAA,IAC9D;AACA,aAAS,qBAAqB,UAAU;AACpC,UAAI,aAAa,gBAAgB;AAC7B,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,aAAS,mBAAmB,oBAAoB,SAAS,WAAW,cAAc;AAC9E,UAAI,CAAC,oBAAoB;AACrB;AAAA,MACJ;AACA,YAAM,EAAE,aAAa,QAAQ,eAAe,SAAS,IAAI;AACzD,YAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ;AAAA,QAC1C;AAAA,MACJ;AACA,iBAAW,MAAM,QAAO;AACpB,YAAI,eAAe,mBAAmB,EAAE,KAAK,mBAAmB,EAAE,EAAE,OAAO,GAAG;AAC1E,gBAAM,EAAE,gBAAgB,IAAI;AAC5B,gBAAM,oBAAoB,qBAAqB,QAAQ;AACvD,gBAAM,wBAAwB,kBAAkB,oBAAoB,IAAI,OAAO;AAE/E,gBAAM,kBAAkB,MAAI;AACxB,gBAAI,YAAY,WAAW;AACvB,kBAAI,OAAO,oBAAoB,YAAY,CAAC,QAAQ,uBAAuB,eAAe,GAAG;AACzF,sBAAM,MAAM,WAAW,qBAAqB,SAAS,yBAAyB,mBAAmB,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,IAAI,+BAA+B,OAAO,wCAAwC,UAAU,IAAI,gBAAgB,eAAe;AACpQ,oBAAI,YAAY,eAAe;AAC3B,kBAAAV,OAAM,GAAG;AAAA,gBACb,OAAO;AACH,kBAAAC,MAAK,GAAG;AAAA,gBACZ;AAAA,cACJ;AACA,qBAAO,mBAAmB,EAAE,EAAE,OAAO,EAAE,qBAAqB;AAAA,YAChE,OAAO;AACH,kBAAI,oBAAoB,SAAS,oBAAoB,KAAK;AACtD,uBAAO,mBAAmB,EAAE,EAAE,OAAO,EAAE,qBAAqB;AAAA,cAChE;AACA,kBAAI,QAAQ,uBAAuB,eAAe,GAAG;AACjD,uBAAO,mBAAmB,EAAE,EAAE,OAAO,EAAE,qBAAqB;AAAA,cAChE;AACA,yBAAW,CAAC,YAAY,YAAY,KAAK,OAAO,QAAQ,mBAAmB,EAAE,EAAE,OAAO,CAAC,GAAE;AACrF,oBAAI,QAAQ,YAAY,eAAe,GAAG;AACtC,yBAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,gBAAM,SAAS;AAAA,YACX,eAAe;AAAA,YACf,OAAO;AAAA,YACP;AAAA,YACA,SAAS;AAAA,YACT,kBAAkB,OAAO;AAAA,YACzB,UAAU;AAAA,UACd;AACA,gBAAM,gBAAgB,aAAa,KAAK,MAAM,KAAK;AACnD,iBAAO,cAAc,SAAS;AAAA,QAClC;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,sBAAsB;AAC3B,aAAO,OAAO,eAAe;AAAA,IACjC;AACA,aAAS,uBAAuB,SAAS;AACrC,YAAM,EAAE,SAAS,cAAc,WAAW,IAAI;AAC9C,YAAM,kBAAkB,CAAC,kBAAgB;AACrC,YAAI,CAAC,eAAe;AAChB,iBAAO;AAAA,QACX;AACA,cAAM,kBAAkB,CAAC;AACzB,sBAAc,QAAQ,CAAC,WAAS;AAC5B,0BAAgB,OAAO,OAAO,IAAI;AAAA,QACtC,CAAC;AACD,cAAM,WAAW,SAAS,MAAM,KAAK;AACjC,iBAAO,CAAC,SAAS,gBAAgB,IAAI,CAAC,KAAK,UAAU,MAAM,GAAG;AAAA,QAClE;AACA,cAAM,aAAa,YAAY,iBAAiB,QAAQ;AACxD,eAAO,gBAAgB,UAAU;AAAA,MACrC;AACA,UAAI;AACJ,YAAM,YAAY,yBAAyB,gBAAgB,OAAO,SAAS,aAAa,aAAa,OAAO,yBAAyB;AACrI,aAAO,OAAO,OAAO,CAAC,GAAG,SAAS,WAAW,OAAO,CAAC,GAAG,gBAAgB,OAAO,SAAS,aAAa,eAAe;AAAA,IACxH;AAEA,aAAS,eAAe;AAEpB,aAAO,OAAO,gCAAgC,cAAc,8BAA8B;AAAA,IAC9F;AAMA,aAAS,6BAA6B,SAAS,IAAI;AAC/C,iBAAW,UAAU,SAAQ;AAEzB,cAAM,gBAAgB,GAAG,WAAW,OAAO,IAAI;AAC/C,YAAI,SAAS,GAAG,QAAQ,OAAO,MAAM,EAAE;AACvC,YAAI,eAAe;AACf,cAAI,OAAO,WAAW,GAAG,GAAG;AACxB,kBAAM,iBAAiB,OAAO;AAC9B,qBAAS,IAAI,MAAM;AACnB,mBAAO;AAAA,cACH;AAAA,cACA;AAAA,cACA;AAAA,YACJ;AAAA,UACJ,WAAW,WAAW,IAAI;AACtB,mBAAO;AAAA,cACH,gBAAgB,OAAO;AAAA,cACvB,QAAQ;AAAA,cACR;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,cAAM,iBAAiB,OAAO,SAAS,GAAG,WAAW,OAAO,KAAK;AACjE,YAAI,kBAAkB,OAAO,SAAS,GAAG,QAAQ,OAAO,OAAO,EAAE;AACjE,YAAI,OAAO,SAAS,gBAAgB;AAChC,cAAI,mBAAmB,gBAAgB,WAAW,GAAG,GAAG;AACpD,kBAAM,iBAAiB,OAAO;AAC9B,8BAAkB,IAAI,eAAe;AACrC,mBAAO;AAAA,cACH;AAAA,cACA,QAAQ;AAAA,cACR;AAAA,YACJ;AAAA,UACJ,WAAW,oBAAoB,IAAI;AAC/B,mBAAO;AAAA,cACH,gBAAgB,OAAO;AAAA,cACvB,QAAQ;AAAA,cACR;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,IACJ;AAEA,aAAS,YAAY,SAAS,aAAa;AACvC,iBAAW,UAAU,SAAQ;AACzB,cAAM,gBAAgB,gBAAgB,OAAO;AAC7C,YAAI,eAAe;AACf,iBAAO;AAAA,QACX;AACA,cAAM,iBAAiB,OAAO,SAAS,gBAAgB,OAAO;AAC9D,YAAI,gBAAgB;AAChB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA;AAAA,IACJ;AAEA,aAAS,gBAAgB,SAAS,UAAU;AACxC,YAAM,gBAAgB,qBAAqB;AAC3C,YAAM,gBAAgB;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,cAAc;AAAA,QACvB,SAAS,cAAc;AAAA,QACvB,SAAS,gBAAgB;AAAA,QACzB,SAAS;AAAA,QACT,SAAS;AAAA,MACb;AAEA,UAAI,cAAc,SAAS,GAAG;AAC1B,sBAAc,QAAQ,CAAC,WAAS;AAC5B,cAAI,WAAW,OAAO,SAAS,QAAQ,KAAK,CAAC,SAAO,KAAK,SAAS,OAAO,IAAI,GAAG;AAC5E,oBAAQ,KAAK,MAAM;AAAA,UACvB;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,WAAW,QAAQ,SAAS,GAAG;AAC/B,gBAAQ,QAAQ,CAAC,WAAS;AACtB,wBAAc,QAAQ,CAAC,iBAAe;AAClC,yBAAa,YAAY,QAAQ,QAAQ;AAAA,UAC7C,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AAEA,QAAM,iBAAiB;AACvB,mBAAe,aAAa,EAAE,OAAO,mBAAmB,GAAG;AACvD,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAS;AAClC,YAAI;AACA,cAAI,CAAC,oBAAoB;AACrB,gBAAI,OAAO,kCAAkC,aAAa;AACtD,kBAAI,SAAS,aAAa,WAAW,KAAK,KAAK,cAAc,EAAE,EAAE;AAAA,gBAC7D;AAAA,gBACA;AAAA,cACJ,CAAC;AAAA,YACL,OAAO;AACH;AAAA;AAAA;AAAA,gBAAoD;AAAA,gBAAO,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,YACzF;AAAA,UACJ,OAAO;AACH,oBAAQ,kBAAkB;AAAA,UAC9B;AAAA,QACJ,SAAS,GAAG;AACR,iBAAO,CAAC;AAAA,QACZ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,mBAAe,kBAAkB,EAAE,OAAO,mBAAmB,GAAG;AAC5D,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAS;AAClC,YAAI;AACA,cAAI,CAAC,oBAAoB;AAErB,gBAAI,OAAO,uBAAuB,aAAa;AAE3C,qBAAO,OAAO,KAAK,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,YACnD,OAAO;AACH,kBAAI,SAAS,aAAa,kBAAkB,KAAK,KAAK,cAAc,EAAE,EAAE;AAAA,gBACpE;AAAA,gBACA;AAAA,cACJ,CAAC;AAAA,YACL;AAAA,UACJ,OAAO;AACH,oBAAQ,kBAAkB;AAAA,UAC9B;AAAA,QACJ,SAAS,GAAG;AACR,iBAAO,CAAC;AAAA,QACZ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,aAAS,wBAAwB,MAAM,YAAY,OAAO;AACtD,YAAM,EAAE,gBAAgB,aAAa,IAAI,sBAAsB,MAAM,UAAU;AAC/E,MAAAF,QAAO,cAAc,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,QAChG,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB;AAAA,MACJ,CAAC,CAAC;AACF,aAAO;AAAA,IACX;AACA,mBAAe,gBAAgB,EAAE,MAAM,YAAY,OAAO,YAAAY,YAAW,GAAG;AACpE,YAAM,EAAE,cAAc,mBAAmB,IAAI,sBAAsB,MAAM,UAAU;AACnF,UAAI,oBAAoB;AACpB,eAAO;AAAA,MACX;AACA,aAAO,IAAI,WAAW,OAAO;AAAA,QACzB,OAAO,CAAC;AAAA,QACR,kBAAkB,CAACC,MAAKC,WAAQ;AAC5B,gBAAMV,OAAMQ,YAAW,UAAU,aAAa,KAAK;AAAA,YAC/C,KAAAC;AAAA,YACA,OAAAC;AAAA,UACJ,CAAC;AACD,cAAI,CAACV,KAAK;AACV,cAAIA,gBAAe,mBAAmB;AAClC,mBAAOA;AAAA,UACX;AACA,cAAI,YAAYA,QAAO,aAAaA,MAAK;AACrC,mBAAOA;AAAA,UACX;AACA;AAAA,QACJ;AAAA,MACJ,CAAC,EAAE,KAAK,MAAI;AACR,eAAO,wBAAwB,MAAM,YAAY,KAAK;AAAA,MAC1D,CAAC,EAAE,MAAM,CAAC,MAAI;AACV,QAAAJ,QAAO,QAAW,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,UAC7F,YAAY;AAAA,UACZ,aAAa;AAAA,QACjB,CAAC,CAAC;AACF,cAAM;AAAA,MACV,CAAC;AAAA,IACL;AACA,mBAAe,aAAa,EAAE,YAAY,oBAAoB,YAAAY,YAAW,GAAG;AACxE,YAAM,EAAE,OAAO,iBAAiB,YAAY,MAAM,KAAK,IAAI;AAC3D,cAAO,MAAK;AAAA,QACR,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,aAAa;AAAA,YAChB;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL,KAAK;AACD,iBAAO,kBAAkB;AAAA,YACrB;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AACI,iBAAO,gBAAgB;AAAA,YACnB;AAAA,YACA;AAAA,YACA;AAAA,YACA,YAAAA;AAAA,UACJ,CAAC;AAAA,MACT;AAAA,IACJ;AACA,mBAAe,cAAc,EAAE,YAAY,YAAAA,YAAW,GAAG;AACrD,YAAM,EAAE,OAAO,iBAAiB,YAAY,MAAM,KAAK,IAAI;AAC3D,YAAM,EAAE,cAAc,mBAAmB,IAAI,sBAAsB,MAAM,UAAU;AACnF,UAAI,oBAAoB;AACpB,eAAO;AAAA,MACX;AACA,aAAO,IAAI,eAAe,OAAO;AAAA,QAC7B,OAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,QACA,YAAY;AAAA,UACR,kBAAkB,CAACC,MAAKC,SAAQ,CAAC,MAAI;AACjC,kBAAMV,OAAMQ,YAAW,UAAU,aAAa,KAAK;AAAA,cAC/C,KAAAC;AAAA,cACA,OAAAC;AAAA,YACJ,CAAC;AACD,gBAAI,CAACV,KAAK;AACV,gBAAI,SAASA,MAAK;AACd,qBAAOA;AAAA,YACX;AACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC,EAAE,KAAK,MAAI;AACR,eAAO,wBAAwB,MAAM,YAAY,KAAK;AAAA,MAC1D,CAAC,EAAE,MAAM,CAAC,MAAI;AACV,cAAM;AAAA,MACV,CAAC;AAAA,IACL;AACA,aAAS,wBAAwB,YAAY;AACzC,YAAM,EAAE,OAAO,KAAK,IAAI;AACxB,aAAO,IAAI,wBAAwB,MAAM,KAAK;AAAA,IAClD;AACA,mBAAe,eAAe,EAAE,QAAQ,oBAAoB,WAAW,GAAG;AACtE,YAAM,YAAY,wBAAwB,UAAU;AACpD,UAAI,oBAAoB;AACpB,eAAO;AAAA,MACX;AACA,UAAI,CAAC,cAAc,SAAS,GAAG;AAC3B,cAAM,gBAAgB,OAAO,cAAc,MAAM,UAAU;AAC3D,cAAMQ,cAAa,OAAO;AAC1B,sBAAc,SAAS,IAAI,cAAc,KAAK;AAAA,UAC1C,YAAAA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC,EAAE,KAAK,CAACR,SAAM;AACX,cAAIA,MAAK;AACL,mBAAOA;AAAA,UACX;AAEA,gBAAM,mBAAmB,OAAO,eAAe,cAAc,eAAe,QAAQ,IAAI,aAAa;AACrG,iBAAO,mBAAmB,aAAa;AAAA,YACnC;AAAA,YACA;AAAA,YACA,YAAAQ;AAAA,UACJ,CAAC,IAAI,cAAc;AAAA,YACf;AAAA,YACA,YAAAA;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AACA,aAAO,cAAc,SAAS;AAAA,IAClC;AACA,aAAS,cAAc,QAAQ;AAC3B,aAAOf,WAAU,SAAS,CAAC,GAAG,QAAQ;AAAA,QAClC,OAAO,WAAW,SAAS,OAAO,QAAQ;AAAA,QAC1C,MAAM,OAAO,QAAQ;AAAA,QACrB,iBAAiB,OAAO,mBAAmB,OAAO;AAAA,QAClD,YAAY,OAAO,cAAc;AAAA,MACrC,CAAC;AAAA,IACL;AAEA,aAAS,mBAAmB,eAAe;AACvC,aAAOA,WAAU,SAAS;AAAA,QACtB,kBAAkB;AAAA,QAClB,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,mBAAmB;AAAA,MACvB,GAAG,aAAa;AAAA,IACpB;AACA,aAAS,kBAAkB,SAAS,aAAa;AAC7C,aAAO,YAAY,IAAI,CAAC,SAAO;AAC3B,cAAM,aAAa,YAAY,SAAS,KAAK,WAAW;AACxD,QAAAG,QAAO,YAAY,qBAAqB,KAAK,WAAW,6BAA6B,CAAC,cAAc,IAAI,aAAa;AAAA,UACjH;AAAA,UACA;AAAA,QACJ,CAAC,CAAC,EAAE;AACJ,eAAO;AAAA,UACH,QAAQ;AAAA,UACR,eAAe,mBAAmB,IAAI;AAAA,QAC1C;AAAA,MACJ,CAAC;AAAA,IACL;AACA,aAAS,wBAAwB,SAAS;AACtC,UAAI,CAAC,SAAS;AACV,eAAO,CAAC;AAAA,MACZ;AACA,aAAO,QAAQ,IAAI,CAAC,WAAS;AACzB,YAAI,WAAW,KAAK;AAChB,iBAAO;AAAA,QACX;AACA,YAAI,OAAO,WAAW,IAAI,GAAG;AACzB,iBAAO,OAAO,QAAQ,MAAM,EAAE;AAAA,QAClC;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,aAAS,cAAc,YAAY,MAAM,QACzC,iBAAiB,MAAM;AACnB,YAAM,EAAE,WAAW,sBAAsB,YAAY,IAAI;AACzD,UAAI,KAAK,QAAQ,WAAW;AACxB,oBAAY,QAAQ,CAAC,UAAQ;AACzB,gBAAM,EAAE,WAAW,IAAI;AACvB,gBAAMe,UAAS,KAAK,YAAY,IAAI,WAAW,IAAI;AACnD,cAAIA,SAAQ;AACR,2BAAe;AAAA,cACX,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,oBAAoBA,QAAO;AAAA,YAC/B,CAAC;AAAA,UACL,OAAO;AACH,2BAAe;AAAA,cACX,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,oBAAoB;AAAA,YACxB,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD,YAAI,gBAAgB;AAChB,gBAAM,eAAe;AAAA,YACjB,KAAK;AAAA,YACL,IAAI;AAAA,UACR;AACA,oBAAU,QAAQ,CAAC,WAAS;AACxB,kBAAM,EAAE,MAAM,OAAO,WAAW,IAAI,IAAI,WAAW;AAAA,cAC/C,KAAK;AAAA,cACL,IAAI,MAAI;AAAA,cAER;AAAA,cACA,OAAO;AAAA,cACP,gBAAgB,CAACF,MAAKC,WAAQ;AAC1B,sBAAMV,OAAM,KAAK,WAAW,UAAU,WAAW,KAAK;AAAA,kBAClD,KAAAS;AAAA,kBACA,OAAAC;AAAA,gBACJ,CAAC;AACD,oBAAIV,gBAAe,iBAAiB;AAChC,yBAAOA;AAAA,gBACX;AACA;AAAA,cACJ;AAAA,YACJ,CAAC;AACD,0BAAc,SAAS,KAAK,YAAY,KAAK;AAAA,UACjD,CAAC;AAAA,QACL,OAAO;AACH,gBAAM,eAAe;AAAA,YACjB,KAAK;AAAA,YACL,MAAM;AAAA,UACV;AACA,oBAAU,QAAQ,CAAC,WAAS;AACxB,kBAAM,EAAE,MAAM,OAAO,WAAW,IAAI,IAAI,WAAW;AAAA,cAC/C,KAAK;AAAA,cACL,IAAI,MAAI;AAAA,cAER;AAAA,cACA,OAAO;AAAA,cACP,gBAAgB,CAACS,MAAKC,WAAQ;AAC1B,sBAAMV,OAAM,KAAK,WAAW,UAAU,WAAW,KAAK;AAAA,kBAClD,KAAAS;AAAA,kBACA,OAAAC;AAAA,gBACJ,CAAC;AACD,oBAAIV,gBAAe,iBAAiB;AAChC,yBAAOA;AAAA,gBACX;AACA;AAAA,cACJ;AAAA,cACA,gBAAgB;AAAA,YACpB,CAAC;AACD,0BAAc,SAAS,KAAK,YAAY,KAAK;AAAA,UACjD,CAAC;AAAA,QACL;AACA,YAAI,gBAAgB;AAChB,gBAAM,eAAe;AAAA,YACjB,KAAK;AAAA,YACL,IAAI;AAAA,UACR;AACA,+BAAqB,QAAQ,CAAC,UAAQ;AAClC,kBAAM,EAAE,MAAM,QAAQ,WAAW,IAAI,IAAI,WAAW;AAAA,cAChD,KAAK;AAAA,cACL,IAAI,MAAI;AAAA,cAER;AAAA,cACA,OAAO;AAAA,cACP,gBAAgB,CAACS,MAAKC,WAAQ;AAC1B,sBAAMV,OAAM,KAAK,WAAW,UAAU,WAAW,KAAK;AAAA,kBAClD,KAAAS;AAAA,kBACA,OAAAC;AAAA,gBACJ,CAAC;AACD,oBAAIV,gBAAe,iBAAiB;AAChC,yBAAOA;AAAA,gBACX;AACA;AAAA,cACJ;AAAA,YACJ,CAAC;AACD,0BAAc,SAAS,KAAK,YAAY,MAAM;AAAA,UAClD,CAAC;AAAA,QACL,OAAO;AACH,gBAAM,eAAe;AAAA,YACjB,eAAe;AAAA,YACf,OAAO,cAAc,OAAO,SAAS,WAAW,UAAU,WAAW,WAAW;AAAA,UACpF;AACA,+BAAqB,QAAQ,CAAC,UAAQ;AAClC,kBAAM,EAAE,QAAQ,UAAU,WAAW,IAAI,IAAI,aAAa;AAAA,cACtD,KAAK;AAAA,cACL,IAAI,MAAI;AAAA,cAER;AAAA,cACA,OAAO;AAAA,cACP,kBAAkB,CAACS,MAAKC,WAAQ;AAC5B,sBAAMV,OAAM,KAAK,WAAW,UAAU,aAAa,KAAK;AAAA,kBACpD,KAAAS;AAAA,kBACA,OAAAC;AAAA,gBACJ,CAAC;AACD,oBAAIV,gBAAe,mBAAmB;AAClC,yBAAOA;AAAA,gBACX;AACA;AAAA,cACJ;AAAA,cACA,kBAAkB;AAAA,YACtB,CAAC;AACD,0BAAc,SAAS,KAAK,YAAY,QAAQ;AAAA,UACpD,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAEA,QAAM,aAAa;AAAA,MACf;AAAA,MACA;AAAA,IACJ;AACA,QAAM,cAAc;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAI,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,SAAS,MAAM,OAAO;AAAA,MACtB,MAAM,WAAW;AACb,YAAI,KAAK,oBAAoB;AACzB,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI;AACJ,YAAI;AACA,+BAAqB,MAAM,eAAe;AAAA,YACtC,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK;AAAA,YACjB,oBAAoB,KAAK;AAAA,UAC7B,CAAC;AAAA,QACL,SAAS,KAAK;AACV,gBAAM,YAAY,wBAAwB,KAAK,UAAU;AACzD,+BAAqB,MAAM,KAAK,KAAK,WAAW,UAAU,eAAe,KAAK;AAAA,YAC1E;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK;AAAA,YACjB,oBAAoB,KAAK;AAAA,YACzB;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AACA,QAAAJ,QAAO,oBAAoB;AAAA,GAAsC,IAAI,aAAa,KAAK,UAAU,CAAC,EAAE;AACpG,aAAK,qBAAqB;AAC1B,eAAO,KAAK;AAAA,MAChB;AAAA;AAAA,MAEA,MAAM,IAAI,IAAI,QAAQ,SAAS,gBAAgB;AAC3C,cAAM,EAAE,cAAc,KAAK,IAAI,WAAW;AAAA,UACtC,aAAa;AAAA,QACjB;AAEA,cAAM,qBAAqB,MAAM,KAAK,SAAS;AAC/C,YAAI,CAAC,KAAK,QAAQ;AACd,gBAAM,qBAAqB,KAAK,KAAK;AACrC,gBAAM,iBAAiB,MAAM,QAAQ,KAAK,WAAW,UAAU,IAAI,KAAK,WAAW,aAAa;AAAA,YAC5F,KAAK,WAAW;AAAA,UACpB;AACA,cAAI,CAAC,eAAe,QAAQ;AACxB,2BAAe,KAAK,SAAS;AAAA,UACjC;AACA,yBAAe,QAAQ,CAAC,kBAAgB;AACpC,gBAAI,CAAC,mBAAmB,aAAa,GAAG;AACpC,iCAAmB,aAAa,IAAI,CAAC;AAAA,YACzC;AAAA,UACJ,CAAC;AAED,gBAAM,aAAa,mBAAmB,eAAe,CAAC,CAAC;AACvD,gBAAM,YAAY,CAAC;AACnB,gBAAM,yBAAyB;AAAA,YAC3B,SAAS,KAAK,WAAW,WAAW;AAAA,YACpC,gBAAgB,MAAM,QAAQ,KAAK,WAAW,UAAU,IAAI,iBAAiB,KAAK,WAAW,cAAc;AAAA,UAC/G;AAEA,iBAAO,eAAe,wBAAwB,iBAAiB;AAAA,YAC3D,OAAO;AAAA;AAAA,YAEP,YAAY;AAAA,UAChB,CAAC;AACD,gBAAM,uBAAuB,MAAM,KAAK,KAAK,MAAM,UAAU,oBAAoB,KAAK;AAAA,YAClF;AAAA;AAAA,YAEA;AAAA,YACA;AAAA,YACA,YAAY,KAAK;AAAA,YACjB,QAAQ,KAAK;AAAA,UACjB,CAAC;AACD,cAAI,QAAQ,sBAAsB,OAAO,SAAS,mBAAmB,UAAU,aAAa;AACxF,YAAAC,OAAM,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,cACjF,UAAU,KAAK,KAAK;AAAA,cACpB,YAAY,KAAK,WAAW;AAAA,cAC5B,gBAAgB,KAAK,WAAW;AAAA,cAChC,gBAAgB,KAAK,WAAW;AAAA,YACpC,CAAC,CAAC;AAAA,UACN;AACA,gBAAM,mBAAmB,KAAK,qBAAqB,YAAY,qBAAqB,WAAW,qBAAqB,sBAAsB;AAC1I,gBAAM,KAAK,KAAK,MAAM,UAAU,cAAc,KAAKJ,WAAU,SAAS,CAAC,GAAG,sBAAsB;AAAA,YAC5F;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC,CAAC;AAAA,QACN;AACA,aAAK,MAAM;AACX,aAAK,SAAS;AACd,YAAI;AACJ,wBAAgB,MAAM,KAAK,KAAK,WAAW,UAAU,iBAAiB,KAAK;AAAA,UACvE;AAAA,UACA;AAAA,UACA,YAAY,KAAK;AAAA,QACrB,CAAC;AAED,YAAI,CAAC,eAAe;AAChB,0BAAgB,MAAM,mBAAmB,IAAI,MAAM;AAAA,QACvD;AACA,QAAAG,QAAO,eAAe,GAAG,QAAQ,KAAK,UAAU,CAAC,wBAAwB,MAAM,GAAG;AAElF,cAAM,aAAa,mBAAmB,KAAK,WAAW,MAAM,MAAM;AAClE,cAAM,oBAAoB,KAAK,cAAc,eAAe,UAAU;AACtE,YAAI,CAAC,aAAa;AACd,iBAAO;AAAA,QACX;AACA,cAAM,gBAAgB,MAAM,kBAAkB;AAC9C,eAAO;AAAA,MACX;AAAA,MACA,cAAc,eAAe,IAAI;AAC7B,iBAAS,eAAeI,MAAKY,KAAI;AAC7B,cAAIZ,QAAO,OAAOA,SAAQ,YAAY,OAAO,aAAaA,IAAG,KAAK,CAAC,OAAO,yBAAyBA,MAAK,OAAO,IAAI,cAAc,CAAC,GAAG;AACjI,mBAAO,eAAeA,MAAK,OAAO,IAAI,cAAc,GAAG;AAAA,cACnD,OAAOY;AAAA,cACP,YAAY;AAAA,YAChB,CAAC;AAAA,UACL;AAAA,QACJ;AACA,YAAI,yBAAyB,SAAS;AAClC,iBAAO,YAAU;AACb,kBAAMZ,OAAM,MAAM,cAAc;AAEhC,2BAAeA,MAAK,EAAE;AACtB,mBAAOA;AAAA,UACX;AAAA,QACJ,OAAO;AACH,iBAAO,MAAI;AACP,kBAAMA,OAAM,cAAc;AAE1B,2BAAeA,MAAK,EAAE;AACtB,mBAAOA;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,YAAY,EAAE,YAAY,KAAK,GAAE;AAC7B,aAAK,SAAS;AACd,aAAK,MAAM;AACX,aAAK,aAAa;AAClB,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AAEA,QAAM,WAAN,MAAe;AAAA,MACX,GAAG,IAAI;AACH,YAAI,OAAO,OAAO,YAAY;AAC1B,eAAK,UAAU,IAAI,EAAE;AAAA,QACzB;AAAA,MACJ;AAAA,MACA,KAAK,IAAI;AAEL,cAAM,OAAO;AACb,aAAK,GAAG,SAAS,WAAW,MAAM;AAC9B,eAAK,OAAO,OAAO;AAEnB,iBAAO,GAAG,MAAM,MAAM,IAAI;AAAA,QAC9B,CAAC;AAAA,MACL;AAAA,MACA,QAAQa,OAAM;AACV,YAAI;AACJ,YAAI,KAAK,UAAU,OAAO,GAAG;AAEzB,eAAK,UAAU,QAAQ,CAAC,OAAK;AACzB,qBAAS,GAAG,GAAGA,KAAI;AAAA,UACvB,CAAC;AAAA,QACL;AACA,eAAO;AAAA,MACX;AAAA,MACA,OAAO,IAAI;AACP,aAAK,UAAU,OAAO,EAAE;AAAA,MAC5B;AAAA,MACA,YAAY;AACR,aAAK,UAAU,MAAM;AAAA,MACzB;AAAA,MACA,YAAY,MAAK;AACb,aAAK,OAAO;AACZ,aAAK,YAAY,oBAAI,IAAI;AACzB,YAAI,MAAM;AACN,eAAK,OAAO;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAM,YAAN,cAAwB,SAAS;AAAA,MAC7B,QAAQA,OAAM;AACV,YAAI;AACJ,cAAM,KAAK,MAAM,KAAK,KAAK,SAAS;AACpC,YAAI,GAAG,SAAS,GAAG;AACf,cAAI,IAAI;AACR,gBAAM,OAAO,CAAC,SAAO;AACjB,gBAAI,SAAS,OAAO;AAChB,qBAAO;AAAA,YACX,WAAW,IAAI,GAAG,QAAQ;AACtB,qBAAO,QAAQ,QAAQ,GAAG,GAAG,EAAE,MAAM,MAAMA,KAAI,CAAC,EAAE,KAAK,IAAI;AAAA,YAC/D,OAAO;AACH,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,mBAAS,KAAK;AAAA,QAClB;AACA,eAAO,QAAQ,QAAQ,MAAM;AAAA,MACjC;AAAA,IACJ;AAGA,aAAS,gBAAgB,cAAc,cAAc;AACjD,UAAI,CAAC,SAAS,YAAY,GAAG;AACzB,eAAO;AAAA,MACX;AACA,UAAI,iBAAiB,cAAc;AAE/B,mBAAU,OAAO,cAAa;AAC1B,cAAI,EAAE,OAAO,eAAe;AACxB,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAM,oBAAN,cAAgC,SAAS;AAAA,MACrC,KAAKA,OAAM;AACP,YAAI,CAAC,SAASA,KAAI,GAAG;AACjB,UAAAhB,OAAM,qBAAqB,KAAK,IAAI,6BAA6B;AAAA,QACrE;AACA,mBAAW,MAAM,KAAK,WAAU;AAC5B,cAAI;AACA,kBAAM,WAAW,GAAGgB,KAAI;AACxB,gBAAI,gBAAgBA,OAAM,QAAQ,GAAG;AACjC,cAAAA,QAAO;AAAA,YACX,OAAO;AACH,mBAAK,QAAQ,oDAAoD,KAAK,IAAI,SAAS;AACnF;AAAA,YACJ;AAAA,UACJ,SAAS,GAAG;AACR,YAAAf,MAAK,CAAC;AACN,iBAAK,QAAQ,CAAC;AAAA,UAClB;AAAA,QACJ;AACA,eAAOe;AAAA,MACX;AAAA,MACA,YAAY,MAAK;AACb,cAAM;AACN,aAAK,UAAUhB;AACf,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AAEA,QAAM,qBAAN,cAAiC,SAAS;AAAA,MACtC,KAAKgB,OAAM;AACP,YAAI,CAAC,SAASA,KAAI,GAAG;AACjB,UAAAhB,OAAM,8BAA8B,KAAK,IAAI,2BAA2B;AAAA,QAC5E;AACA,cAAM,KAAK,MAAM,KAAK,KAAK,SAAS;AACpC,YAAI,GAAG,SAAS,GAAG;AACf,cAAI,IAAI;AACR,gBAAM,eAAe,CAAC,MAAI;AACtB,YAAAC,MAAK,CAAC;AACN,iBAAK,QAAQ,CAAC;AACd,mBAAOe;AAAA,UACX;AACA,gBAAM,OAAO,CAAC,aAAW;AACrB,gBAAI,gBAAgBA,OAAM,QAAQ,GAAG;AACjC,cAAAA,QAAO;AACP,kBAAI,IAAI,GAAG,QAAQ;AACf,oBAAI;AACA,yBAAO,QAAQ,QAAQ,GAAG,GAAG,EAAEA,KAAI,CAAC,EAAE,KAAK,MAAM,YAAY;AAAA,gBACjE,SAAS,GAAG;AACR,yBAAO,aAAa,CAAC;AAAA,gBACzB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,mBAAK,QAAQ,iDAAiD,KAAK,IAAI,SAAS;AAAA,YACpF;AACA,mBAAOA;AAAA,UACX;AACA,iBAAO,QAAQ,QAAQ,KAAKA,KAAI,CAAC;AAAA,QACrC;AACA,eAAO,QAAQ,QAAQA,KAAI;AAAA,MAC/B;AAAA,MACA,YAAY,MAAK;AACb,cAAM;AACN,aAAK,UAAUhB;AACf,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AAEA,QAAM,eAAN,MAAmB;AAAA,MACf,YAAY,QAAQ,UAAU;AAC1B,QAAAD,QAAO,cAAc,MAAM,GAAG,kCAAkC;AAEhE,cAAM,aAAa,OAAO;AAC1B,QAAAA,QAAO,YAAY,wCAAwC;AAC3D,YAAI,CAAC,KAAK,gBAAgB,UAAU,GAAG;AACnC,eAAK,gBAAgB,UAAU,IAAI;AACnC,iBAAO,SAAS,OAAO,SAAS,OAAO,MAAM,KAAK,QAAQ,QAAQ;AAClE,iBAAO,KAAK,KAAK,SAAS,EAAE,QAAQ,CAAC,QAAM;AACvC,kBAAM,aAAa,OAAO,GAAG;AAC7B,gBAAI,YAAY;AACZ,mBAAK,UAAU,GAAG,EAAE,GAAG,UAAU;AAAA,YACrC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,MACA,aAAa,YAAY;AACrB,QAAAA,QAAO,YAAY,qBAAqB;AACxC,cAAM,SAAS,KAAK,gBAAgB,UAAU;AAC9C,QAAAA,QAAO,QAAQ,eAAe,UAAU,sBAAsB;AAC9D,eAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAM;AAC/B,cAAI,QAAQ,QAAQ;AAChB,iBAAK,UAAU,GAAG,EAAE,OAAO,OAAO,GAAG,CAAC;AAAA,UAC1C;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,YAAY,WAAU;AAClB,aAAK,kBAAkB,CAAC;AACxB,aAAK,YAAY;AACjB,aAAK,gBAAgB,OAAO,KAAK,SAAS;AAAA,MAC9C;AAAA,IACJ;AAEA,aAAS,iBAAiB,YAAY,gBAAgB;AAClD,YAAM,kBAAkB,+BAA+B,cAAc;AACrE,UAAI,CAAC,gBAAgB,KAAK;AACtB,QAAAC,OAAM,gCAAgC,WAAW,IAAI,yBAAyB;AAAA,MAClF;AACA,UAAI,WAAW,IAAI,eAAe,gBAAgB,gBAAgB,GAAG;AACrE,UAAI,CAAC,IAAI,aAAa,KAAK,CAAC,SAAS,WAAW,MAAM,GAAG;AACrD,mBAAW,SAAS,QAAQ;AAAA,MAChC;AACA,iBAAW,OAAO,gBAAgB;AAClC,iBAAW,kBAAkB,gBAAgB;AAC7C,iBAAW,QAAQ;AACnB,iBAAW,UAAU,eAAe;AACpC,iBAAW,eAAe,eAAe;AAAA,IAC7C;AACA,aAAS,iBAAiB;AACtB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,aAAc,MAAM;AACtB,gBAAM,EAAE,QAAQ,gBAAgB,QAAQ,QAAQ,YAAY,GAAG,IAAI;AACnE,cAAI,CAAC,sBAAsB,MAAM,KAAK,CAAC,kBAAkB,MAAM,GAAG;AAC9D,kBAAM,EAAE,gBAAgB,eAAe,IAAI,MAAM,OAAO,gBAAgB,uBAAuB;AAAA,cAC3F,YAAY;AAAA,cACZ;AAAA,YACJ,CAAC;AACD,6BAAiB,YAAY,cAAc;AAE3C,kBAAM,iBAAiB;AAAA,cACnB;AAAA,cACA,eAAe;AAAA,gBACX,aAAa;AAAA,gBACb,SAAS;AAAA,kBACL;AAAA,gBACJ;AAAA,gBACA,kBAAkB;AAAA,gBAClB,OAAO;AAAA,gBACP,YAAY;AAAA,cAChB;AAAA,YACJ;AACA,kBAAM,SAAS,MAAM,OAAO,cAAc,MAAM,UAAU,sBAAsB,KAAK;AAAA,cACjF;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACJ,CAAC;AACD,gBAAI,QAAQ;AACR,4BAAc,YAAY,QAAQ,QAAQ,KAAK;AAAA,YACnD;AACA,mBAAOJ,WAAU,SAAS,CAAC,GAAG,MAAM;AAAA,cAChC;AAAA,YACJ,CAAC;AAAA,UACL;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAIA,aAAS,QAAQ,IAAI;AACjB,YAAM,YAAY,GAAG,MAAM,GAAG;AAC9B,UAAI,UAAU,WAAW,GAAG;AACxB,eAAO;AAAA,UACH,MAAM,UAAU,CAAC;AAAA,UACjB,SAAS;AAAA,QACb;AAAA,MACJ,WAAW,UAAU,WAAW,GAAG;AAC/B,eAAO;AAAA,UACH,MAAM,UAAU,CAAC;AAAA,UACjB,SAAS,UAAU,CAAC;AAAA,QACxB;AAAA,MACJ,OAAO;AACH,eAAO;AAAA,UACH,MAAM,UAAU,CAAC;AAAA,UACjB,SAAS,UAAU,CAAC;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,mBAAmB,gBAAgB,YAAY,UAAU,QAAQ,OAAO,CAAC,GAAG,gBAAgB;AACjG,YAAM,KAAK,QAAQ,UAAU;AAC7B,YAAM,EAAE,OAAO,cAAc,IAAI,mBAAmB,gBAAgB,EAAE;AACtE,YAAM,0BAA0B,kBAAkB;AAClD,UAAI,2BAA2B,CAAC,IAAI,mBAAmB,uBAAuB,GAAG;AAC7E,iBAAS,yBAAyB,YAAY,MAAM;AACpD,YAAI,wBAAwB,aAAa;AACrC,gBAAM,aAAa,OAAO,KAAK,wBAAwB,WAAW;AAClE,qBAAW,OAAO,YAAW;AACzB,gBAAI,KAAK,GAAG,GAAG;AACX;AAAA,YACJ;AACA,iBAAK,GAAG,IAAI;AACZ,kBAAM,gBAAgB,QAAQ,GAAG;AACjC,kBAAM,cAAc,wBAAwB,YAAY,GAAG;AAC3D,+BAAmB,gBAAgB;AAAA,cAC/B,MAAM,cAAc;AAAA,cACpB,SAAS,YAAY;AAAA,YACzB,GAAG,UAAU,OAAO,MAAM,MAAS;AAAA,UACvC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAM,YAAY,CAAC,MAAMgB,SAAM;AAC3B,aAAO,SAAS,cAAc,GAAG,IAAI,IAAI,SAAS,SAAS,SAAS,KAAK,KAAKA,IAAG,IAAI;AAAA,IACzF;AAEA,aAAS,sBAAsB,QAAQ,gBAAgB,QAAQ,gBAAgB,gBAAgB;AAC3F,YAAM,YAAY,CAAC;AACnB,YAAM,WAAW,CAAC;AAClB,YAAM,cAAc,CAAC;AACrB,YAAM,uBAAuB,oBAAI,IAAI;AACrC,YAAM,wBAAwB,oBAAI,IAAI;AACtC,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,EAAE,eAAe,kBAAkB,IAAI;AAC7C,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,OAAO,CAAC;AACd,yBAAmB,gBAAgB,QAAQ,CAAC,oBAAoB,YAAY,WAAS;AACjF,YAAI;AACJ,YAAI,QAAQ;AACR,0BAAgB;AAAA,QACpB,OAAO;AACH,cAAI,MAAM,QAAQ,UAAU,GAAG;AAE3B,kBAAM,oBAAoB,WAAW,KAAK,CAAC,iBAAe;AACtD,kBAAI,aAAa,gBAAgB,WAAW,QAAQ,aAAa,gBAAgB,WAAW,OAAO;AAC/F,uBAAO;AAAA,cACX;AACA,qBAAO;AAAA,YACX,CAAC;AACD,gBAAI,CAAC,mBAAmB;AACpB;AAAA,YACJ;AACA,4BAAgB,mBAAmB,iBAAiB;AAAA,UACxD,WAAW,eAAe,MAAM;AAC5B,4BAAgB;AAAA,UACpB,OAAO;AACH;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,iBAAiB,IAAI,eAAe,oBAAoB,+BAA+B,kBAAkB,EAAE,GAAG;AACpH,YAAI,gBAAgB;AAChB,sBAAY,KAAK;AAAA,YACb,MAAM,WAAW;AAAA,YACjB,YAAY;AAAA,cACR,MAAM,WAAW;AAAA,cACjB,OAAO;AAAA,cACP,MAAM,qBAAqB,qBAAqB,mBAAmB,kBAAkB;AAAA,cACrF,iBAAiB,gBAAgB,qBAAqB,mBAAmB,aAAa,WAAW;AAAA,cACjG,YAAY;AAAA,cACZ,SAAS,aAAa,qBAAqB,mBAAmB,UAAU;AAAA,YAC5E;AAAA,YACA,KAAK;AAAA,UACT,CAAC;AAAA,QACL;AACA,YAAI,mBAAmB,aAAa,qBAAqB,mBAAmB,UAAU,CAAC;AACvF,cAAM,2BAA2B,wBAAwB,cAAc,OAAO;AAC9E,YAAI,yBAAyB,UAAU,aAAa,oBAAoB;AACpE,cAAI;AACJ,6BAAmB,sBAAsB,OAAO,UAAU,8BAA8B,mBAAmB,YAAY,OAAO,SAAS,4BAA4B,OAAO,CAAC,QAAQ,oBAAkB;AACjM,iBAAK,4BAA4B,OAAO,SAAS,yBAAyB,QAAQ,gBAAgB,UAAU,OAAO,IAAI;AACnH,qBAAO,KAAK,eAAe;AAAA,YAC/B;AACA,mBAAO;AAAA,UACX,GAAG,CAAC,CAAC;AAAA,QACT;AACA,iBAAS,aAAa,QAAQ;AAC1B,gBAAM,YAAY,OAAO,IAAI,CAAC,UAAQ,IAAI,eAAe,oBAAoB,KAAK,CAAC;AACnF,cAAI,cAAc,QAAQ;AACtB,mBAAO,UAAU,OAAO,cAAc,MAAM;AAAA,UAChD;AACA,iBAAO;AAAA,QACX;AACA,YAAI,kBAAkB;AAClB,gBAAM,eAAe,iBAAiB;AACtC,mBAAQK,SAAQ,GAAGA,SAAQ,cAAcA,UAAQ;AAC7C,kBAAM,aAAa,iBAAiBA,MAAK;AACzC,kBAAM,iBAAiB,GAAG,WAAW,IAAI,IAAI,WAAW,UAAU;AAClE,mBAAO,cAAc,MAAM,UAAU,oBAAoB,KAAK;AAAA,cAC1D,IAAI,WAAW,eAAe,MAAM,WAAW,OAAO;AAAA,cACtD,MAAM,WAAW;AAAA,cACjB,gBAAgB;AAAA,cAChB;AAAA,cACA,QAAQ;AAAA,cACR;AAAA,YACJ,CAAC;AACD,kBAAM,YAAY,aAAa,cAAc;AAC7C,gBAAI,WAAW;AACX;AAAA,YACJ;AACA,gBAAI,cAAc,qBAAqB,OAAO;AAC1C,wBAAU,KAAK,GAAG,aAAa,WAAW,OAAO,IAAI,KAAK,CAAC;AAC3D,wBAAU,KAAK,GAAG,aAAa,WAAW,OAAO,IAAI,IAAI,CAAC;AAC1D,uBAAS,KAAK,GAAG,aAAa,WAAW,OAAO,GAAG,KAAK,CAAC;AACzD,uBAAS,KAAK,GAAG,aAAa,WAAW,OAAO,GAAG,IAAI,CAAC;AAAA,YAE5D,WAAW,cAAc,mBAAmB,QAAQ;AAChD,wBAAU,KAAK,GAAG,aAAa,WAAW,OAAO,IAAI,IAAI,CAAC;AAC1D,uBAAS,KAAK,GAAG,aAAa,WAAW,OAAO,GAAG,IAAI,CAAC;AAAA,YAC5D;AACA,yBAAa,cAAc;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ,GAAG,MAAM,MAAM,cAAc;AAC7B,UAAI,eAAe,UAAU,eAAe,OAAO,SAAS,GAAG;AAC3D,cAAM,sBAAsB,CAAC,WAAW,mBAAiB;AACrD,gBAAM,mBAAmB,mBAAmB,OAAO,eAAe,eAAe,YAAY,WAAW,OAAO,cAAc,MAAM,UAAU,YAAY;AAEzJ,cAAI,oBAAoB,OAAO,iBAAiB,QAAQ,YAAY;AAChE,2BAAe,OAAO,GAAG,KAAK,QAAQ,CAAC,UAAQ;AAC3C,mCAAqB,IAAI,KAAK;AAAA,YAClC,CAAC;AACD,2BAAe,OAAO,IAAI,KAAK,QAAQ,CAAC,UAAQ;AAC5C,oCAAsB,IAAI,KAAK;AAAA,YACnC,CAAC;AAAA,UACL;AAAA,QACJ;AACA,uBAAe,OAAO,QAAQ,CAAC,WAAS;AACpC,cAAI;AACJ,gBAAM,cAAc,kBAAkB,QAAQ,WAAW,OAAO,SAAS,gBAAgB,OAAO,UAAU;AAC1G,cAAI,CAAC,YAAY;AACb;AAAA,UACJ;AAEA,gBAAM,gBAAgB,OAAO,UAAU,WAAW,KAAK,CAAC,MAAI,EAAE,YAAY,OAAO,OAAO,IAAI;AAC5F,cAAI,CAAC,eAAe;AAChB;AAAA,UACJ;AACA,gBAAM,iBAAiB,aAAa,aAAa;AACjD,yBAAe,QAAQ,CAAC,MAAI;AACxB,gCAAoB,GAAG,MAAM;AAAA,UACjC,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AACA,YAAM,sBAAsB,SAAS,OAAO,CAAC,UAAQ,CAAC,qBAAqB,IAAI,KAAK,KAAK,CAAC,UAAU,UAAU,KAAK,CAAC;AACpH,YAAM,uBAAuB,UAAU,OAAO,CAAC,UAAQ,CAAC,sBAAsB,IAAI,KAAK,KAAK,CAAC,UAAU,QAAQ,KAAK,CAAC;AACrH,aAAO;AAAA,QACH,WAAW;AAAA,QACX,sBAAsB;AAAA,QACtB,aAAa,YAAY,OAAO,CAAC,UAAQ,CAAC,UAAU,UAAU,MAAM,GAAG,CAAC;AAAA,MAC5E;AAAA,IACJ;AACA,QAAM,8BAA8B,WAAW;AAC3C,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,sBAAuB,MAAM;AAC/B,gBAAM,EAAE,QAAQ,gBAAgB,YAAY,QAAQ,gBAAgB,eAAe,IAAI;AACvF,cAAI,CAAC,IAAI,aAAa,GAAG;AACrB,mBAAO;AAAA,cACH,WAAW,CAAC;AAAA,cACZ,sBAAsB,CAAC;AAAA,cACvB,aAAa,CAAC;AAAA,YAClB;AAAA,UACJ;AACA,cAAI,sBAAsB,MAAM,KAAK,kBAAkB,MAAM,GAAG;AAC5D,mBAAO;AAAA,cACH,WAAW,CAAC;AAAA,cACZ,sBAAsB,CAAC;AAAA,cACvB,aAAa;AAAA,gBACT;AAAA,kBACI,MAAM,OAAO;AAAA,kBACb,KAAK,OAAO;AAAA,kBACZ,YAAY;AAAA,oBACR,MAAM,WAAW;AAAA,oBACjB,OAAO,OAAO;AAAA,oBACd,MAAM,WAAW,QAAQ;AAAA,oBACzB,iBAAiB;AAAA,oBACjB,YAAY;AAAA,kBAChB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,2BAAiB,YAAY,cAAc;AAC3C,gBAAM,SAAS,sBAAsB,QAAQ,gBAAgB,YAAY,gBAAgB,cAAc;AACvG,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,oBAAoB,YAAY,QAAQ;AAC7C,YAAM,qBAAqB,kCAAkC;AAAA,QACzD,MAAM,OAAO;AAAA,QACb,SAAS,OAAO,QAAQ;AAAA,MAC5B,CAAC;AAED,YAAM,mBAAmB,sBAAsB,iBAAiB,sBAAsB,mBAAmB,eAAe,mBAAmB,mBAAmB,aAAa,WAAW,IAAI,EAAE;AAC5L,UAAI,oBAAoB,iBAAiB,gBAAgB;AACrD,eAAO;AAAA,UACH;AAAA,UACA,gBAAgB,kBAAkB;AAAA,UAClC,gBAAgB,kCAAkC;AAAA,YAC9C,MAAM,WAAW;AAAA,YACjB,SAAS,iBAAiB;AAAA,UAC9B,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO;AAAA,QACH,oBAAoB;AAAA,QACpB,gBAAgB,kBAAkB;AAAA,QAClC,gBAAgB,kCAAkC;AAAA,UAC9C,MAAM,WAAW;AAAA,UACjB,SAAS,aAAa,aAAa,WAAW,UAAU;AAAA,QAC5D,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAM,kBAAN,MAAsB;AAAA;AAAA,MAElB,MAAM,uBAAuB,EAAE,YAAY,IAAI,OAAO,GAAG;AACrD,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,KAAK,MAAM,UAAU,yBAAyB,KAAK;AAAA,UACrD;AAAA,UACA;AAAA,QACJ,CAAC;AACD,YAAI,eAAe,kCAAkC;AAAA,UACjD,MAAM,KAAK,aAAa,QAAQ;AAAA,UAChC,SAAS,KAAK,aAAa,QAAQ;AAAA,QACvC,CAAC;AACD,YAAI,CAAC,cAAc;AACf,yBAAe;AAAA,YACX,SAAS,KAAK,aAAa,QAAQ,WAAW;AAAA,YAC9C,aAAa;AAAA,YACb,aAAa,CAAC;AAAA,UAClB;AACA,4BAAkB;AAAA,YACd,CAAC,KAAK,aAAa,QAAQ,IAAI,GAAG;AAAA,UACtC,CAAC;AAAA,QACL;AAGA,YAAI,gBAAgB,iBAAiB,gBAAgB,CAAC,mBAAmB,aAAa,aAAa,WAAW,IAAI,EAAE,OAAO;AACvH,cAAI,aAAa,cAAc,WAAW,YAAY;AAClD,yBAAa,cAAcrB,WAAU,SAAS,CAAC,GAAG,gBAAgB,OAAO,SAAS,aAAa,aAAa;AAAA,cACxG,CAAC,WAAW,IAAI,GAAG;AAAA,gBACf,gBAAgB,aAAa,aAAa,WAAW,UAAU,WAAW;AAAA,cAC9E;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,cAAM,EAAE,oBAAoB,gBAAgB,eAAe,IAAI,KAAK,oBAAoB,UAAU;AAClG,cAAM,EAAE,gBAAgB,sBAAsB,gBAAgB,kBAAkB,IAAI,MAAM,KAAK,MAAM,UAAU,aAAa,KAAK;AAAA,UAC7H;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,YAAI;AACJ,YAAI;AAEJ,YAAI,sBAAsB;AACtB,cAAI,IAAI,mBAAmB,oBAAoB,GAAG;AAC9C,kBAAM,cAAc,IAAI,aAAa,IAAI,qBAAqB,cAAc,qBAAqB,kBAAkB,qBAAqB,eAAe;AACvJ,kBAAM,iBAAiB,MAAM,KAAK,gBAAgB,aAAa,YAAY,CAAC,CAAC;AAE7E,kBAAMsB,qBAAoB,kCAAkCtB,WAAU,SAAS,CAAC,GAAG,YAAY;AAAA;AAAA;AAAA,cAG3F,OAAO;AAAA,YACX,CAAC,GAAG,cAAc;AAClB,wBAAY;AACZ,wBAAYsB;AAAA,UAChB,OAAO;AACH,kBAAM,EAAE,gBAAgB,kBAAkB,IAAI,MAAM,KAAK,MAAM,UAAU,mBAAmB,KAAK;AAAA,cAC7F,SAAS,KAAK,aAAa;AAAA,cAC3B;AAAA,cACA,gBAAgB;AAAA,cAChB,MAAM;AAAA,YACV,CAAC;AACD,wBAAY;AACZ,wBAAY;AAAA,UAChB;AAAA,QACJ,OAAO;AACH,cAAI,sBAAsB,UAAU,GAAG;AAEnC,kBAAM,iBAAiB,MAAM,KAAK,gBAAgB,WAAW,OAAO,YAAY,CAAC,CAAC;AAElF,kBAAMA,qBAAoB,kCAAkC,YAAY,cAAc;AACtF,kBAAM,EAAE,gBAAgB,kBAAkB,IAAI,MAAM,KAAK,MAAM,UAAU,mBAAmB,KAAK;AAAA,cAC7F,SAAS,KAAK,aAAa;AAAA,cAC3B;AAAA,cACA,gBAAgB;AAAA,cAChB,MAAM;AAAA,YACV,CAAC;AACD,wBAAY;AACZ,wBAAYA;AAAA,UAChB,OAAO;AACH,YAAAlB,OAAM,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,cACjF,UAAU,WAAW;AAAA,cACrB,aAAa,WAAW;AAAA,cACxB,gBAAgB,KAAK,UAAU,iBAAiB;AAAA,YACpD,CAAC,CAAC;AAAA,UACN;AAAA,QACJ;AACA,cAAM,KAAK,MAAM,UAAU,kBAAkB,KAAK;AAAA,UAC9C;AAAA,UACA,MAAM,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA,gBAAgB;AAAA,QACpB,CAAC;AACD,eAAO;AAAA,UACH,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,QACpB;AAAA,MACJ;AAAA,MACA,oBAAoB,YAAY;AAC5B,eAAO,oBAAoB,YAAY,KAAK,YAAY;AAAA,MAC5D;AAAA,MACA,MAAM,gBAAgB,aAAa,YAAY,cAAc;AACzD,cAAM,cAAc,YAAU;AAC1B,cAAI,eAAe,KAAK,cAAc,IAAI,WAAW;AACrD,cAAI,cAAc;AACd,mBAAO;AAAA,UACX;AACA,cAAI;AACA,gBAAIG,OAAM,MAAM,KAAK,WAAW,UAAU,MAAM,KAAK,aAAa,CAAC,CAAC;AACpE,gBAAI,CAACA,QAAO,EAAEA,gBAAe,WAAW;AACpC,cAAAA,OAAM,MAAM,MAAM,aAAa,CAAC,CAAC;AAAA,YACrC;AACA,2BAAe,MAAMA,KAAI,KAAK;AAAA,UAClC,SAAS,KAAK;AACV,2BAAe,MAAM,KAAK,aAAa,cAAc,MAAM,UAAU,gBAAgB,KAAK;AAAA,cACtF,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,WAAW;AAAA,cACX,QAAQ,KAAK;AAAA,YACjB,CAAC;AACD,gBAAI,CAAC,cAAc;AACf,qBAAO,KAAK,gBAAgB,WAAW;AACvC,cAAAH,OAAM,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,gBACjF;AAAA,gBACA,YAAY,WAAW;AAAA,gBACvB,UAAU,KAAK,aAAa,QAAQ;AAAA,cACxC,GAAG,GAAG,GAAG,EAAE,CAAC;AAAA,YAChB;AAAA,UACJ;AACA,UAAAD,QAAO,aAAa,YAAY,aAAa,WAAW,aAAa,QAAQ,GAAG,WAAW,+BAA+B;AAC1H,eAAK,cAAc,IAAI,aAAa,YAAY;AAChD,iBAAO;AAAA,QACX;AACA,cAAM,mBAAmB,YAAU;AAC/B,gBAAM,eAAe,MAAM,YAAY;AACvC,gBAAM,iBAAiB,IAAI,6BAA6B,cAAc;AAAA,YAClE,SAAS;AAAA,UACb,CAAC;AACD,gBAAM,EAAE,gBAAgB,kBAAkB,IAAI,MAAM,KAAK,MAAM,UAAU,mBAAmB,KAAK;AAAA,YAC7F,SAAS,KAAK,aAAa;AAAA,YAC3B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,MAAM;AAAA,UACV,CAAC;AACD,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,KAAK,gBAAgB,WAAW,GAAG;AACpC,eAAK,gBAAgB,WAAW,IAAI,iBAAiB,EAAE,KAAK,CAACI,SAAMA,IAAG;AAAA,QAC1E;AACA,eAAO,KAAK,gBAAgB,WAAW;AAAA,MAC3C;AAAA,MACA,YAAY,cAAa;AACrB,aAAK,sBAAsB;AAC3B,aAAK,gBAAgB,oBAAI,IAAI;AAC7B,aAAK,QAAQ,IAAI,aAAa;AAAA,UAC1B,0BAA0B,IAAI,UAAU,0BAA0B;AAAA,UAClE,cAAc,IAAI,mBAAmB,oBAAoB;AAAA,UACzD,oBAAoB,IAAI,mBAAmB,oBAAoB;AAAA,UAC/D,mBAAmB,IAAI,mBAAmB,mBAAmB;AAAA,QACjE,CAAC;AACD,aAAK,kBAAkB,OAAO,eAAe;AAC7C,aAAK,eAAe;AACpB,aAAK,aAAa,aAAa;AAAA,MACnC;AAAA,IACJ;AAEA,QAAM,gBAAN,MAAoB;AAAA;AAAA,MAEhB,eAAe,eAAe,aAAa;AACvC,cAAM,EAAE,YAAY,OAAO,IAAI,mBAAmB,eAAe,WAAW;AAC5E,cAAM,aAAa,OAAO,KAAK,UAAU;AACzC,mBAAW,QAAQ,CAAC,cAAY;AAC5B,gBAAM,aAAa,WAAW,SAAS;AACvC,qBAAW,QAAQ,CAAC,cAAY;AAC5B,kBAAM,mBAAmB,mBAAmB,KAAK,eAAe,WAAW,WAAW,KAAK,MAAM,UAAU,YAAY;AACvH,gBAAI,CAAC,oBAAoB,aAAa,UAAU,KAAK;AACjD,mBAAK,UAAU;AAAA,gBACX,SAAS;AAAA,gBACT,KAAK,UAAU;AAAA,gBACf,KAAK,UAAU;AAAA,gBACf,QAAQ;AAAA,gBACR,QAAQ;AAAA,gBACR,MAAM,YAAY;AAAA,cACtB,CAAC;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AACD,eAAO;AAAA,UACH;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,MAAM,UAAU,SAAS,cAAc;AACnC,cAAM,EAAE,KAAK,IAAI;AAKjB,cAAM,YAAY,uBAAuB;AAAA,UACrC;AAAA,UACA;AAAA,UACA,YAAY,KAAK,QAAQ;AAAA,QAC7B,CAAC;AACD,YAAI,aAAa,OAAO,SAAS,UAAU,OAAO;AAC9C,gBAAM,QAAQ,IAAI,UAAU,MAAM,IAAI,OAAO,eAAa;AACtD,kBAAM,QAAQ,IAAI,KAAK,kBAAkB,YAAY;AAAA,cACjD,UAAU,UAAU;AAAA,YACxB,CAAC,CAAC;AACF;AAAA,UACJ,CAAC,CAAC;AAAA,QACN;AACA,cAAM,eAAe,MAAM,KAAK,MAAM,UAAU,gBAAgB,KAAK;AAAA,UACjE;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,QAAQ;AAAA,UACrB,QAAQ;AAAA,QACZ,CAAC;AACD,cAAM,EAAE,WAAW,aAAa,IAAI;AAEpC,QAAAJ,QAAO,cAAc,eAAe,OAAO,iBAAiB,KAAK,QAAQ,IAAI,4BAA4B,OAAO,sCAAsC;AAEtJ,cAAM,mBAAmB,mBAAmB,KAAK,eAAe,SAAS,cAAc,KAAK,MAAM,UAAU,YAAY;AACxH,cAAM,WAAW,CAAC,WAAS;AACvB,cAAI,CAAC,OAAO,OAAO;AACf,mBAAO,QAAQ,CAAC;AAAA,UACpB;AACA,wBAAc,OAAO,OAAO,KAAK,QAAQ,IAAI;AAAA,QACjD;AACA,YAAI,oBAAoB,iBAAiB,KAAK;AAC1C,mBAAS,gBAAgB;AACzB,iBAAO,iBAAiB;AAAA,QAC5B,WAAW,oBAAoB,iBAAiB,WAAW,CAAC,iBAAiB,QAAQ;AACjF,gBAAM,UAAU,MAAM,iBAAiB;AACvC,2BAAiB,SAAS;AAC1B,cAAI,CAAC,iBAAiB,KAAK;AACvB,6BAAiB,MAAM;AAAA,UAC3B;AACA,mBAAS,gBAAgB;AACzB,iBAAO;AAAA,QACX,WAAW,kBAAkB;AACzB,gBAAM,mBAAmB,YAAU;AAC/B,kBAAM,UAAU,MAAM,iBAAiB,IAAI;AAC3C,yBAAa,MAAM;AACnB,yBAAa,SAAS;AACtB,qBAAS,YAAY;AACrB,kBAAM,UAAU,mBAAmB,KAAK,eAAe,SAAS,cAAc,KAAK,MAAM,UAAU,YAAY;AAC/G,gBAAI,SAAS;AACT,sBAAQ,MAAM;AACd,sBAAQ,SAAS;AACjB,uBAAS,OAAO;AAAA,YACpB;AACA,mBAAO;AAAA,UACX;AACA,gBAAM,UAAU,iBAAiB;AACjC,eAAK,UAAU;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,MAAM,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX,OAAO;AACH,cAAI,gBAAgB,OAAO,SAAS,aAAa,iBAAiB;AAC9D,mBAAO;AAAA,UACX;AACA,gBAAM,mBAAmB,YAAU;AAC/B,kBAAM,UAAU,MAAM,aAAa,IAAI;AACvC,yBAAa,MAAM;AACnB,yBAAa,SAAS;AACtB,qBAAS,YAAY;AACrB,kBAAM,UAAU,mBAAmB,KAAK,eAAe,SAAS,cAAc,KAAK,MAAM,UAAU,YAAY;AAC/G,gBAAI,SAAS;AACT,sBAAQ,MAAM;AACd,sBAAQ,SAAS;AAAA,YACrB;AACA,mBAAO;AAAA,UACX;AACA,gBAAM,UAAU,iBAAiB;AACjC,eAAK,UAAU;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,MAAM,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,kBAAkB,iBAAiB,eAAe,cAAc;AAC5D,cAAM,EAAE,KAAK,IAAI;AACjB,cAAM,OAAO,gBAAgB,OAAO,SAAS,aAAa;AAC1D,cAAM,WAAW,gBAAgB,OAAO,SAAS,aAAa;AAC9D,YAAI,YAAY,gBAAgB,OAAO,SAAS,aAAa;AAC7D,cAAM,WAAW,CAAC;AAClB,YAAI,SAAS,SAAS;AAClB,gBAAM,EAAE,WAAW,IAAI;AACvB,cAAI,CAAC,UAAW,aAAY,CAAC;AAC7B,cAAI,YAAY,WAAW,cAAc;AACzC,cAAI,CAAC,UAAW,aAAY,WAAW,cAAc,IAAI;AAAA,YACrD,MAAM,KAAK,KAAK;AAAA,UACpB;AACA,cAAI,UAAU,QAAQ,SAAS,KAAK,EAAG,QAAO;AAC9C,oBAAU,KAAK,SAAS;AAAA,QAC5B;AACA,cAAM,aAAa,KAAK;AACxB,cAAM,WAAW,KAAK,QAAQ;AAE9B,YAAI,CAAC,WAAW,cAAc,GAAG;AAC7B,qBAAW,cAAc,IAAI,CAAC;AAAA,QAClC;AAEA,cAAM,QAAQ,WAAW,cAAc;AACvC,cAAM,WAAW,CAAC,MAAM,WAAS;AAC7B,cAAI;AACJ,gBAAM,EAAE,SAAS,MAAM,IAAI;AAC3B,gBAAM,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAC9B,gBAAM,WAAW,MAAM,IAAI;AAC3B,gBAAM,gBAAgB,SAAS,OAAO;AACtC,gBAAM,qBAAqB,QAAQ,kBAAkB,cAAc,WAAW,6BAA6B,cAAc,gBAAgB,OAAO,SAAS,2BAA2B,OAAO;AAC3L,cAAI,CAAC,iBAAiB,cAAc,aAAa,kBAAkB,CAAC,cAAc,WAAW,QAAQ,CAAC,KAAK,MAAM,CAAC,qBAAqB,QAAQ,WAAW,cAAc,OAAO;AAC3K,qBAAS,OAAO,IAAI;AAAA,UACxB;AAAA,QACJ;AACA,cAAM,SAAS,CAAC,QAAM,OAAO,IAAI,QAAQ,IAAI,KAAK,WAAW,cAAc,GAAG,SAAS;AACvF,cAAM,mBAAmB,OAAO,QAAM;AAClC,gBAAM,EAAE,QAAAe,QAAO,IAAI,MAAM,KAAK,cAAc,0BAA0B;AAAA,YAClE,IAAI;AAAA,UACR,CAAC;AACD,cAAIA,QAAO,UAAU;AACjB,gBAAI;AACJ,gBAAI;AACA,mCAAqB,MAAMA,QAAO,SAAS;AAAA,YAC/C,SAASd,QAAO;AACZ,mCAAqB,MAAM,KAAK,cAAc,MAAM,UAAU,gBAAgB,KAAK;AAAA,gBAC/E,IAAI;AAAA,gBACJ,OAAAA;AAAA,gBACA,MAAM;AAAA,gBACN,WAAW;AAAA,gBACX,QAAQ;AAAA,cACZ,CAAC;AAAA,YACL;AACA,gBAAI,CAACc,QAAO,QAAQ;AAChB,oBAAM,OAAO,kBAAkB;AAC/B,cAAAA,QAAO,SAAS;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,KAAK,KAAK,QAAQ,MAAM,EAAE,QAAQ,CAAC,cAAY;AAClD,gBAAM,YAAY,KAAK,QAAQ,OAAO,SAAS;AAC/C,oBAAU,QAAQ,CAAC,WAAS;AACxB,gBAAI,OAAO,MAAM,SAAS,cAAc,GAAG;AACvC,uBAAS,WAAW,MAAM;AAAA,YAC9B;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAED,YAAI,KAAK,QAAQ,kBAAkB,mBAAmB,aAAa,iBAAiB;AAChF,eAAK,QAAQ,QAAQ,QAAQ,CAAC,WAAS;AACnC,gBAAI,OAAO,eAAe,gBAAgB;AACtC,uBAAS,KAAK,iBAAiB,OAAO,IAAI,CAAC;AAAA,YAC/C;AAAA,UACJ,CAAC;AAAA,QACL;AACA,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,cAAc,SAAS,cAAc;AACjC,cAAM,EAAE,KAAK,IAAI;AACjB,cAAM,YAAY,uBAAuB;AAAA,UACrC;AAAA,UACA;AAAA,UACA,YAAY,KAAK,QAAQ;AAAA,QAC7B,CAAC;AACD,YAAI,aAAa,OAAO,SAAS,UAAU,OAAO;AAC9C,oBAAU,MAAM,QAAQ,CAAC,eAAa;AAClC,iBAAK,kBAAkB,YAAY;AAAA,cAC/B,UAAU,UAAU;AAAA,YACxB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,cAAM,mBAAmB,mBAAmB,KAAK,eAAe,SAAS,WAAW,KAAK,MAAM,UAAU,YAAY;AACrH,cAAM,WAAW,CAAC,WAAS;AACvB,cAAI,CAAC,OAAO,OAAO;AACf,mBAAO,QAAQ,CAAC;AAAA,UACpB;AACA,wBAAc,OAAO,OAAO,KAAK,QAAQ,IAAI;AAAA,QACjD;AACA,YAAI,kBAAkB;AAClB,cAAI,OAAO,iBAAiB,QAAQ,YAAY;AAC5C,qBAAS,gBAAgB;AACzB,gBAAI,CAAC,iBAAiB,QAAQ;AAC1B,+BAAiB,SAAS;AAC1B,kBAAI,iBAAiB,SAAS,KAAK,QAAQ,MAAM;AAC7C,0BAAU,SAAS;AAAA,cACvB;AAAA,YACJ;AACA,mBAAO,iBAAiB;AAAA,UAC5B;AACA,cAAI,OAAO,iBAAiB,QAAQ,YAAY;AAC5C,kBAAMA,UAAS,iBAAiB,IAAI;AACpC,gBAAI,EAAEA,mBAAkB,UAAU;AAC9B,uBAAS,gBAAgB;AACzB,mBAAK,UAAU;AAAA,gBACX;AAAA,gBACA,QAAQ;AAAA,gBACR,MAAM,KAAK,QAAQ;AAAA,gBACnB,KAAKA;AAAA,gBACL,QAAQ;AAAA,cACZ,CAAC;AACD,qBAAOA;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,UAAU,KAAK;AACf,cAAI,CAAC,UAAU,QAAQ;AACnB,sBAAU,SAAS;AAAA,UACvB;AACA,iBAAO,UAAU;AAAA,QACrB;AACA,YAAI,UAAU,KAAK;AACf,gBAAMA,UAAS,UAAU,IAAI;AAC7B,cAAIA,mBAAkB,SAAS;AAC3B,kBAAM,aAAa,gBAAgB,OAAO,SAAS,aAAa,UAAU,UAAU,WAAW,cAAc,WAAW;AACxH,kBAAM,IAAI,MAAM,WAAW,iBAAiB,WAAW,WAAW,gBAAgB;AAAA,cAC9E,UAAU,KAAK,QAAQ;AAAA,cACvB,eAAe;AAAA,YACnB,CAAC,CAAC;AAAA,UACN;AACA,oBAAU,MAAMA;AAChB,eAAK,UAAU;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,YACR,MAAM,KAAK,QAAQ;AAAA,YACnB,KAAK,UAAU;AAAA,YACf,QAAQ;AAAA,UACZ,CAAC;AACD,iBAAO,UAAU;AAAA,QACrB;AACA,cAAM,IAAI,MAAM,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,UAC3F,UAAU,KAAK,QAAQ;AAAA,UACvB,eAAe;AAAA,QACnB,CAAC,CAAC;AAAA,MACN;AAAA,MACA,kBAAkB,WAAW,YAAY,eAAe,CAAC,GAAG;AACxD,cAAM,EAAE,KAAK,IAAI;AACjB,aAAK,cAAc,SAAS,IAAI;AAChC,aAAK,MAAM,UAAU,2BAA2B,KAAK;AAAA,UACjD;AAAA,UACA,SAAS,KAAK;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,UACA,mBAAmB,aAAa;AAAA,QACpC,CAAC;AAAA,MACL;AAAA,MACA,UAAU,EAAE,SAAS,QAAQ,MAAM,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC5D,cAAM,EAAE,SAAS,QAAQ,UAAU,IAAI,QAAQ,YAAYlB,WAAU,iCAAiC,QAAQ;AAAA,UAC1G;AAAA,UACA;AAAA,QACJ,CAAC;AACD,cAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ;AAAA,UAC1C;AAAA,QACJ;AACA,eAAO,QAAQ,CAAC,OAAK;AACjB,cAAI,CAAC,KAAK,cAAc,EAAE,GAAG;AACzB,iBAAK,cAAc,EAAE,IAAI,CAAC;AAAA,UAC9B;AACA,cAAI,CAAC,KAAK,cAAc,EAAE,EAAE,OAAO,GAAG;AAClC,iBAAK,cAAc,EAAE,EAAE,OAAO,IAAI,CAAC;AAAA,UACvC;AACA,cAAI,CAAC,KAAK,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,GAAG;AAC3C,iBAAK,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,IAAIA,WAAU,SAAS;AAAA,cAC1D;AAAA,cACA,OAAO;AAAA,gBACH;AAAA,cACJ;AAAA,YACJ,GAAG,WAAW;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,YACJ,CAAC;AACD,gBAAI,KAAK;AACL,mBAAK,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;AAAA,YACnD;AACA;AAAA,UACJ;AACA,gBAAM,mBAAmB,KAAK,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO;AAChE,cAAI,WAAW,CAAC,iBAAiB,SAAS;AACtC,6BAAiB,UAAU;AAAA,UAC/B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,wBAAwB,aAAa;AACjC,cAAM,sBAAsB,oBAAoB;AAChD,cAAM,aAAa,YAAY,MAAM,YAAY;AACjD,YAAI,cAAc,CAAC,oBAAoB,UAAU,GAAG;AAChD,8BAAoB,UAAU,IAAI,KAAK;AAAA,QAC3C;AAAA,MACJ;AAAA,MACA,YAAY,MAAK;AACb,aAAK,QAAQ,IAAI,aAAa;AAAA,UAC1B,cAAc,IAAI,mBAAmB,cAAc;AAAA,UACnD,iBAAiB,IAAI,mBAAmB,iBAAiB;AAAA;AAAA,UAEzD,WAAW,IAAI,UAAU;AAAA,UACzB,cAAc,IAAI,kBAAkB,cAAc;AAAA;AAAA,UAElD,4BAA4B,IAAI,kBAAkB,4BAA4B;AAAA,QAClF,CAAC;AACD,aAAK,OAAO;AACZ,aAAK,gBAAgB,CAAC;AACtB,aAAK,aAAa,CAAC;AACnB,aAAK,wBAAwB,KAAK,OAAO;AAAA,MAC7C;AAAA,IACJ;AAEA,QAAM,gBAAN,MAAoB;AAAA,MAChB,wBAAwB,eAAe,aAAa;AAChD,cAAM,cAAc,YAAY,WAAW,CAAC;AAC5C,eAAO,YAAY,OAAO,CAACO,MAAK,WAAS;AACrC,eAAK,eAAe,QAAQA,MAAK;AAAA,YAC7B,OAAO;AAAA,UACX,CAAC;AACD,iBAAOA;AAAA,QACX,GAAG,cAAc,OAAO;AAAA,MAC5B;AAAA,MACA,iBAAiB,IAAI,iBAAiB;AAClC,cAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,cAAM,EAAE,MAAM,MAAM,IAAI;AACxB,aAAK,cAAc,EAAE,IAAI;AAAA,UACrB,MAAM,OAAO;AAAA,UACb;AAAA,QACJ;AACA,YAAI,SAAS,GAAG,WAAW,IAAI,GAAG;AAC9B,gBAAM,cAAc,GAAG,QAAQ,MAAM,KAAK;AAC1C,eAAK,cAAc,WAAW,IAAI;AAAA,YAC9B,MAAM,OAAO;AAAA,YACb;AAAA,UACJ;AACA;AAAA,QACJ;AACA,YAAI,SAAS,GAAG,WAAW,KAAK,GAAG;AAC/B,gBAAM,aAAa,GAAG,QAAQ,OAAO,IAAI;AACzC,eAAK,cAAc,UAAU,IAAI;AAAA,YAC7B,MAAM,OAAO;AAAA,YACb;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA;AAAA,MAGA,MAAM,WAAW,IAAI,SAAS;AAC1B,cAAM,EAAE,KAAK,IAAI;AACjB,YAAI;AACA,gBAAM,EAAE,cAAc,KAAK,IAAI,WAAW;AAAA,YACtC,aAAa;AAAA,UACjB;AAQA,gBAAM,EAAE,QAAAW,SAAQ,eAAe,gBAAgB,IAAI,MAAM,KAAK,0BAA0B;AAAA,YACpF;AAAA,UACJ,CAAC;AACD,gBAAM,EAAE,gBAAgB,QAAQ,QAAQ,IAAI,OAAO,eAAe,IAAI;AACtE,gBAAM,kBAAkB,MAAMA,QAAO,IAAI,OAAO,QAAQ,SAAS,cAAc;AAC/E,gBAAM,gBAAgB,MAAM,KAAK,MAAM,UAAU,OAAO,KAAK;AAAA,YACzD,IAAI;AAAA,YACJ;AAAA,YACA;AAAA,YACA,cAAc,cAAc,kBAAkB;AAAA,YAC9C,qBAAqB,cAAc,SAAY;AAAA,YAC/C;AAAA,YACA,SAAS;AAAA,YACT,gBAAgBA;AAAA,YAChB,QAAQ;AAAA,UACZ,CAAC;AACD,eAAK,iBAAiB,IAAI,eAAe;AACzC,cAAI,OAAO,kBAAkB,YAAY;AACrC,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX,SAASd,QAAO;AACZ,gBAAM,EAAE,OAAO,UAAU,IAAI,WAAW;AAAA,YACpC,MAAM;AAAA,UACV;AACA,gBAAM,WAAW,MAAM,KAAK,MAAM,UAAU,gBAAgB,KAAK;AAAA,YAC7D;AAAA,YACA,OAAAA;AAAA,YACA;AAAA,YACA,WAAW;AAAA,YACX,QAAQ;AAAA,UACZ,CAAC;AACD,cAAI,CAAC,UAAU;AACX,kBAAMA;AAAA,UACV;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA;AAAA,MAEA,MAAM,cAAc,gBAAgB;AAChC,cAAM,EAAE,KAAK,IAAI;AACjB,cAAM,KAAK,MAAM,UAAU,oBAAoB,KAAK;AAAA,UAChD,YAAY;AAAA,UACZ,SAAS,KAAK;AAAA,UACd,QAAQ;AAAA,QACZ,CAAC;AACD,cAAM,aAAa,kBAAkB,KAAK,QAAQ,SAAS,cAAc;AACzE,cAAM,QAAQ,IAAI,WAAW,IAAI,OAAO,QAAM;AAC1C,gBAAM,EAAE,OAAO,IAAI;AACnB,gBAAM,aAAa,cAAc,MAAM;AACvC,gBAAM,EAAE,gBAAgB,eAAe,IAAI,MAAM,KAAK,gBAAgB,uBAAuB;AAAA,YACzF,YAAY;AAAA,UAChB,CAAC;AACD,gBAAM,SAAS,MAAM,KAAK,MAAM,UAAU,sBAAsB,KAAK;AAAA,YACjE,QAAQ;AAAA,YACR,gBAAgB;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC;AACD,cAAI,CAAC,QAAQ;AACT;AAAA,UACJ;AACA,wBAAc,YAAY,MAAM,MAAM;AAAA,QAC1C,CAAC,CAAC;AAAA,MACN;AAAA,MACA,gBAAgB,SAAS,SAAS;AAC9B,cAAM,EAAE,KAAK,IAAI;AACjB,gBAAQ,QAAQ,CAAC,WAAS;AACtB,eAAK,eAAe,QAAQ,KAAK,QAAQ,SAAS;AAAA,YAC9C,OAAO,WAAW,OAAO,SAAS,QAAQ;AAAA,UAC9C,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,MACA,MAAM,0BAA0B,SAAS;AACrC,cAAM,EAAE,KAAK,IAAI;AACjB,cAAM,EAAE,GAAG,IAAI;AACf,YAAI;AACJ,YAAI;AACA,2BAAiB,MAAM,KAAK,MAAM,UAAU,cAAc,KAAK;AAAA,YAC3D;AAAA,YACA,SAAS,KAAK;AAAA,YACd,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL,SAASA,QAAO;AACZ,2BAAiB,MAAM,KAAK,MAAM,UAAU,gBAAgB,KAAK;AAAA,YAC7D;AAAA,YACA,SAAS,KAAK;AAAA,YACd,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,OAAAA;AAAA,YACA,WAAW;AAAA,UACf,CAAC;AACD,cAAI,CAAC,gBAAgB;AACjB,kBAAMA;AAAA,UACV;AAAA,QACJ;AACA,cAAM,EAAE,IAAI,MAAM,IAAI;AACtB,cAAM,kBAAkB,6BAA6B,KAAK,QAAQ,SAAS,KAAK;AAChF,QAAAD,QAAO,iBAAiB,WAAW,iBAAiB,WAAW,aAAa,WAAW,gBAAgB;AAAA,UACnG,UAAU,KAAK,QAAQ;AAAA,UACvB,WAAW;AAAA,QACf,CAAC,CAAC;AACF,cAAM,EAAE,QAAQ,UAAU,IAAI;AAC9B,cAAM,aAAa,cAAc,SAAS;AAC1C,cAAM,YAAY,MAAM,KAAK,cAAc,MAAM,UAAU,aAAa,KAAKH,WAAU,SAAS;AAAA,UAC5F,IAAI;AAAA,QACR,GAAG,iBAAiB;AAAA,UAChB,SAAS,KAAK;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,QACJ,CAAC,CAAC;AACF,cAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,QAAAG,QAAO,UAAU,QAAQ,yHAAyH,KAAK,GAAG;AAC1J,YAAIe,UAAS,KAAK,YAAY,IAAI,OAAO,IAAI;AAC7C,cAAM,gBAAgB;AAAA,UAClB;AAAA,UACA;AAAA,QACJ;AACA,YAAI,CAACA,SAAQ;AACT,UAAAA,UAAS,IAAI,OAAO,aAAa;AACjC,eAAK,YAAY,IAAI,OAAO,MAAMA,OAAM;AAAA,QAC5C;AACA,eAAO;AAAA,UACH,QAAAA;AAAA,UACA;AAAA,UACA,iBAAiB;AAAA,QACrB;AAAA,MACJ;AAAA,MACA,eAAe,QAAQ,eAAe,SAAS;AAC3C,cAAM,EAAE,KAAK,IAAI;AACjB,cAAM,kBAAkB,MAAI;AACxB,cAAI,OAAO,OAAO;AAGd,kBAAM,YAAY,cAAc,KAAK,CAAC,SAAO;AACzC,kBAAI;AACJ,qBAAO,OAAO,UAAU,KAAK,KAAK,WAAW,OAAO,KAAK,OAAO,cAAc,KAAK,UAAU,OAAO,SAAS,YAAY,WAAW,OAAO,KAAK;AAAA,YACpJ,CAAC;AACD,YAAAf,QAAO,CAAC,WAAW,aAAa,OAAO,KAAK,cAAc,OAAO,IAAI,uCAAuC,aAAa,UAAU,IAAI,gBAAgB;AAAA,UAC3J;AAEA,cAAI,WAAW,QAAQ;AACnB,gBAAI,IAAI,aAAa,KAAK,CAAC,OAAO,MAAM,WAAW,MAAM,GAAG;AACxD,qBAAO,QAAQ,IAAI,IAAI,OAAO,OAAO,OAAO,SAAS,MAAM,EAAE;AAAA,YACjE;AAAA,UACJ;AACA,cAAI,CAAC,OAAO,YAAY;AACpB,mBAAO,aAAa;AAAA,UACxB;AACA,cAAI,CAAC,OAAO,MAAM;AACd,mBAAO,OAAO;AAAA,UAClB;AAAA,QACJ;AACA,aAAK,MAAM,UAAU,qBAAqB,KAAK;AAAA,UAC3C;AAAA,UACA,QAAQ;AAAA,QACZ,CAAC;AACD,cAAM,mBAAmB,cAAc,KAAK,CAAC,SAAO,KAAK,SAAS,OAAO,IAAI;AAC7E,YAAI,CAAC,kBAAkB;AACnB,0BAAgB;AAChB,wBAAc,KAAK,MAAM;AACzB,eAAK,MAAM,UAAU,eAAe,KAAK;AAAA,YACrC;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC;AAAA,QACL,OAAO;AACH,gBAAM,WAAW;AAAA,YACb,eAAe,OAAO,IAAI;AAAA,YAC1B;AAAA,UACJ;AACA,cAAI,WAAW,OAAO,SAAS,QAAQ,OAAO;AAE1C,iBAAK,aAAa,gBAAgB;AAClC,4BAAgB;AAChB,0BAAc,KAAK,MAAM;AACzB,iBAAK,MAAM,UAAU,eAAe,KAAK;AAAA,cACrC;AAAA,cACA,QAAQ;AAAA,YACZ,CAAC;AACD,gBAAI,KAAK,SAAS,KAAK,GAAG,CAAC;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,aAAa,QAAQ;AACjB,YAAI;AACA,gBAAM,EAAE,KAAK,IAAI;AACjB,gBAAM,EAAE,KAAK,IAAI;AACjB,gBAAM,cAAc,KAAK,QAAQ,QAAQ,UAAU,CAAC,SAAO,KAAK,SAAS,IAAI;AAC7E,cAAI,gBAAgB,IAAI;AACpB,iBAAK,QAAQ,QAAQ,OAAO,aAAa,CAAC;AAAA,UAC9C;AACA,gBAAM,eAAe,KAAK,YAAY,IAAI,OAAO,IAAI;AACrD,cAAI,cAAc;AACd,kBAAM,aAAa,aAAa;AAChC,kBAAM,MAAM,WAAW;AACvB,gBAAI,cAAc,GAAG,GAAG;AACpB,kBAAI;AACJ,mBAAK,mCAAmC,OAAO,yBAAyB,eAAe,GAAG,MAAM,OAAO,SAAS,iCAAiC,cAAc;AAC3J,uBAAO,cAAc,GAAG;AAAA,cAC5B,OAAO;AAEH,8BAAc,GAAG,IAAI;AAAA,cACzB;AAAA,YACJ;AACA,kBAAM,uBAAuB,wBAAwB,aAAa,UAAU;AAC5E,gBAAI,cAAc,oBAAoB,GAAG;AACrC,qBAAO,cAAc,oBAAoB;AAAA,YAC7C;AACA,iBAAK,gBAAgB,cAAc,OAAO,WAAW,KAAK;AAE1D,gBAAI,cAAc,WAAW,eAAe,IAAI,wBAAwB,WAAW,MAAM,WAAW,YAAY,IAAI,WAAW;AAC/H,kBAAM,iBAAiB,cAAc,eAAe,cAAc,UAAU,CAAC,QAAM;AAC/E,kBAAI,WAAW,cAAc;AACzB,uBAAO,IAAI,QAAQ,OAAO;AAAA,cAC9B,OAAO;AACH,uBAAO,IAAI,SAAS;AAAA,cACxB;AAAA,YACJ,CAAC;AACD,gBAAI,mBAAmB,IAAI;AACvB,oBAAM,YAAY,cAAc,eAAe,cAAc,cAAc;AAC3E,4BAAc,UAAU,QAAQ,MAAM;AACtC,oBAAM,sBAAsB,oBAAoB;AAChD,kBAAI,qBAAqB;AACzB,oBAAM,iBAAiB,CAAC;AACxB,qBAAO,KAAK,mBAAmB,EAAE,QAAQ,CAAC,WAAS;AAC/C,sBAAM,gBAAgB,oBAAoB,MAAM;AAChD,iCAAiB,OAAO,KAAK,aAAa,EAAE,QAAQ,CAAC,eAAa;AAC9D,wBAAM,gBAAgB,cAAc,UAAU;AAC9C,mCAAiB,OAAO,KAAK,aAAa,EAAE,QAAQ,CAAC,cAAY;AAC7D,0BAAM,aAAa,cAAc,SAAS;AAC1C,kCAAc,OAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,iBAAe;AAC1D,4BAAM,SAAS,WAAW,YAAY;AACtC,0BAAI,UAAU,OAAO,WAAW,YAAY,OAAO,SAAS,WAAW,MAAM;AACzE,4BAAI,OAAO,UAAU,OAAO,SAAS;AACjC,iCAAO,QAAQ,OAAO,MAAM,OAAO,CAAC,iBAAe,iBAAiB,WAAW,IAAI;AACnF,8BAAI,OAAO,MAAM,QAAQ;AACrB,iDAAqB;AAAA,0BACzB,OAAO;AACH,2CAAe,KAAK;AAAA,8BAChB;AAAA,8BACA;AAAA,8BACA;AAAA,8BACA;AAAA,4BACJ,CAAC;AAAA,0BACL;AAAA,wBACJ,OAAO;AACH,yCAAe,KAAK;AAAA,4BAChB;AAAA,4BACA;AAAA,4BACA;AAAA,4BACA;AAAA,0BACJ,CAAC;AAAA,wBACL;AAAA,sBACJ;AAAA,oBACJ,CAAC;AAAA,kBACL,CAAC;AAAA,gBACL,CAAC;AAAA,cACL,CAAC;AACD,kBAAI,oBAAoB;AACpB,0BAAU,gBAAgB,CAAC;AAC3B,uBAAO,oBAAoB,WAAW;AAAA,cAC1C;AACA,6BAAe,QAAQ,CAAC,CAAC,OAAO,YAAY,WAAW,YAAY,MAAI;AACnE,oBAAI,iDAAiD,uCAAuC;AAC5F,iBAAC,6BAA6B,oBAAoB,KAAK,MAAM,OAAO,QAAQ,wCAAwC,2BAA2B,UAAU,MAAM,OAAO,QAAQ,kDAAkD,sCAAsC,SAAS,MAAM,OAAO,OAAO,OAAO,gDAAgD,YAAY;AAAA,cAC1W,CAAC;AACD,4BAAc,eAAe,cAAc,OAAO,gBAAgB,CAAC;AAAA,YACvE;AACA,kBAAM,EAAE,mBAAmB,IAAI,oBAAoB,QAAQ,IAAI;AAC/D,gBAAI,oBAAoB;AACpB,oBAAM,YAAY,sBAAsB,iBAAiB,sBAAsB,mBAAmB,eAAe,mBAAmB,mBAAmB,aAAa,OAAO,IAAI,EAAE;AACjL,kBAAI,WAAW;AACX,uBAAO,mBAAmB,YAAY,SAAS;AAC/C;AAAA;AAAA,kBACA,QAAQ,OAAO,eAAe,qBAAqB,SAAS,CAAC;AAAA,kBAAG;AAC5D,yBAAO,OAAO,eAAe,qBAAqB,SAAS;AAAA,gBAC/D;AAAA,cACJ;AAAA,YACJ;AACA,iBAAK,YAAY,OAAO,OAAO,IAAI;AAAA,UACvC;AAAA,QACJ,SAAS,KAAK;AACV,UAAAD,QAAO,IAAI,uBAAuB,GAAG;AAAA,QACzC;AAAA,MACJ;AAAA,MACA,YAAY,MAAK;AACb,aAAK,QAAQ,IAAI,aAAa;AAAA,UAC1B,sBAAsB,IAAI,kBAAkB,sBAAsB;AAAA,UAClE,gBAAgB,IAAI,kBAAkB,gBAAgB;AAAA,UACtD,eAAe,IAAI,mBAAmB,eAAe;AAAA,UACrD,QAAQ,IAAI,UAAU,QAAQ;AAAA,UAC9B,qBAAqB,IAAI,SAAS,qBAAqB;AAAA,UACvD,iBAAiB,IAAI,UAAU,iBAAiB;AAAA,UAChD,qBAAqB,IAAI,UAAU,qBAAqB;AAAA,UACxD,uBAAuB,IAAI,UAAU,uBAAuB;AAAA;AAAA,UAE5D,oBAAoB,IAAI,UAAU;AAAA,UAClC,WAAW,IAAI,UAAU;AAAA,QAC7B,CAAC;AACD,aAAK,OAAO;AACZ,aAAK,gBAAgB,CAAC;AAAA,MAC1B;AAAA,IACJ;AAEA,QAAM,eAAe,OAAO,2CAA2C,YAAY,CAAC,yCAAyC;AAC7H,QAAM,mBAAN,MAAuB;AAAA,MACnB,YAAY,aAAa;AACrB,aAAK,gBAAgB,YAAY,OAAO;AACxC,cAAM,UAAU,KAAK,cAAc,KAAK,SAAS,WAAW;AAC5D,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AAAA,MACA,MAAM,UAAU,SAAS,cAAc;AACnC,eAAO,KAAK,cAAc,UAAU,SAAS,YAAY;AAAA,MAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,cAAc,SAAS,cAAc;AACjC,eAAO,KAAK,cAAc,cAAc,SAAS,YAAY;AAAA,MACjE;AAAA,MACA,kBAAkB,iBAAiB,eAAe,cAAc;AAC5D,eAAO,KAAK,cAAc,kBAAkB,gBAAgB,YAAY;AAAA,MAC5E;AAAA,MACA,iBAAiB,MAAMc,MAAK,WAAW;AACnC,cAAM,aAAa,cAAc;AAAA,UAC7B;AAAA,UACA,OAAOA;AAAA,QACX,CAAC;AACD,cAAME,UAAS,IAAI,OAAO;AAAA,UACtB,MAAM;AAAA,UACN;AAAA,QACJ,CAAC;AACD,QAAAA,QAAO,qBAAqB;AAC5B,aAAK,YAAY,IAAI,MAAMA,OAAM;AACjC,eAAOA;AAAA,MACX;AAAA;AAAA;AAAA,MAGA,MAAM,WAAW,IAAI,SAAS;AAC1B,eAAO,KAAK,cAAc,WAAW,IAAI,OAAO;AAAA,MACpD;AAAA;AAAA,MAEA,MAAM,cAAc,gBAAgB;AAChC,eAAO,KAAK,cAAc,cAAc,cAAc;AAAA,MAC1D;AAAA,MACA,kBAAkB,WAAW,YAAY,eAAe,CAAC,GAAG;AACxD,aAAK,cAAc,kBAAkB,WAAW,YAAY,YAAY;AAAA,MAC5E;AAAA,MACA,cAAc,eAAe,aAAa;AACtC,cAAM,EAAE,OAAO,IAAI,mBAAmB,eAAe,WAAW;AAChE,cAAM,EAAE,aAAa,gBAAgB,SAAS,iBAAiB,IAAI,KAAK,MAAM,UAAU,WAAW,KAAK;AAAA,UACpG,QAAQ;AAAA,UACR;AAAA,UACA,SAAS;AAAA,UACT,WAAW;AAAA,QACf,CAAC;AACD,cAAM,UAAU,KAAK,cAAc,wBAAwB,kBAAkB,cAAc;AAC3F,cAAM,EAAE,QAAQ,cAAc,IAAI,KAAK,cAAc,eAAe,kBAAkB,cAAc;AACpG,cAAM,UAAU;AAAA,UACZ,GAAG,iBAAiB;AAAA,QACxB;AACA,YAAI,eAAe,SAAS;AACxB,yBAAe,QAAQ,QAAQ,CAAC,WAAS;AACrC,gBAAI,CAAC,QAAQ,SAAS,MAAM,GAAG;AAC3B,sBAAQ,KAAK,MAAM;AAAA,YACvB;AAAA,UACJ,CAAC;AAAA,QACL;AACA,cAAM,aAAalB,WAAU,SAAS,CAAC,GAAG,eAAe,aAAa;AAAA,UAClE;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACZ,CAAC;AACD,aAAK,MAAM,UAAU,KAAK,KAAK;AAAA,UAC3B,QAAQ;AAAA,UACR,SAAS;AAAA,QACb,CAAC;AACD,eAAO;AAAA,MACX;AAAA,MACA,gBAAgB,SAAS;AACrB,cAAM,YAAY,gBAAgB,SAAS,IAAI;AAE/C,aAAK,QAAQ,UAAU,KAAK,QAAQ,QAAQ,OAAO,CAACO,MAAK,WAAS;AAC9D,cAAI,CAAC,OAAQ,QAAOA;AACpB,cAAIA,QAAO,CAACA,KAAI,KAAK,CAAC,SAAO,KAAK,SAAS,OAAO,IAAI,GAAG;AACrD,YAAAA,KAAI,KAAK,MAAM;AAAA,UACnB;AACA,iBAAOA;AAAA,QACX,GAAG,aAAa,CAAC,CAAC;AAAA,MACtB;AAAA,MACA,gBAAgB,SAAS,SAAS;AAC9B,eAAO,KAAK,cAAc,gBAAgB,SAAS,OAAO;AAAA,MAC9D;AAAA,MACA,eAAe,QAAQ;AACnB,aAAK,cAAc,eAAe,KAAK,SAASP,WAAU,SAAS,CAAC,GAAG,KAAK,SAAS;AAAA,UACjF;AAAA,QACJ,CAAC,CAAC;AAAA,MACN;AAAA,MACA,YAAY,aAAY;AACpB,aAAK,QAAQ,IAAI,aAAa;AAAA,UAC1B,YAAY,IAAI,kBAAkB,YAAY;AAAA,UAC9C,MAAM,IAAI,SAAS;AAAA;AAAA,UAEnB,qBAAqB,IAAI,mBAAmB,qBAAqB;AAAA;AAAA,UAEjE,eAAe,IAAI,mBAAmB,eAAe;AAAA,QACzD,CAAC;AACD,aAAK,UAAU;AACf,aAAK,cAAc,oBAAI,IAAI;AAC3B,aAAK,aAAa,IAAI,aAAa;AAAA;AAAA,UAE/B,eAAe,IAAI,SAAS;AAAA,UAC5B,cAAc,IAAI,SAAS;AAAA,UAC3B,YAAY,IAAI,SAAS;AAAA,UACzB,OAAO,IAAI,UAAU;AAAA,UACrB,gBAAgB,IAAI,UAAU;AAAA,UAC9B,kBAAkB,IAAI,UAAU;AAAA,QACpC,CAAC;AACD,aAAK,aAAa,IAAI,aAAa;AAAA,UAC/B,oBAAoB,IAAI,SAAS;AAAA,UACjC,mBAAmB,IAAI,SAAS;AAAA,UAChC,qBAAqB,IAAI,SAAS;AAAA,UAClC,oBAAoB,IAAI,SAAS;AAAA,QACrC,CAAC;AACD,cAAM,UAAU,eAAe;AAAA,UAC3B,eAAe;AAAA,UACf,4BAA4B;AAAA,QAChC,IAAI,CAAC;AAGL,cAAM,iBAAiB;AAAA,UACnB,IAAI,aAAa;AAAA,UACjB,MAAM,YAAY;AAAA,UAClB;AAAA,UACA,SAAS,CAAC;AAAA,UACV,QAAQ,CAAC;AAAA,UACT,WAAW,IAAI,aAAa;AAAA,QAChC;AACA,aAAK,OAAO,YAAY;AACxB,aAAK,UAAU;AACf,aAAK,kBAAkB,IAAI,gBAAgB,IAAI;AAC/C,aAAK,gBAAgB,IAAI,cAAc,IAAI;AAC3C,aAAK,gBAAgB,IAAI,cAAc,IAAI;AAC3C,aAAK,gBAAgB,KAAK,cAAc;AACxC,aAAK,gBAAgB;AAAA,UACjB,GAAG,eAAe;AAAA,UAClB,GAAG,YAAY,WAAW,CAAC;AAAA,QAC/B,CAAC;AACD,aAAK,UAAU,KAAK,cAAc,gBAAgB,WAAW;AAAA,MACjE;AAAA,IACJ;AAEA,QAAI,QAAqB,OAAO,OAAO;AAAA,MACnC,WAAW;AAAA,IACf,CAAC;AAED,IAAAD,SAAQ,aAAa,IAAI;AACzB,IAAAA,SAAQ,iBAAiB,IAAI;AAC7B,IAAAA,SAAQ,gBAAgB;AACxB,IAAAA,SAAQ,SAAS;AACjB,IAAAA,SAAQ,SAAS;AACjB,IAAAA,SAAQ,mBAAmB;AAC3B,IAAAA,SAAQ,oBAAoB;AAC5B,IAAAA,SAAQ,SAASI;AACjB,IAAAJ,SAAQ,iCAAiC;AACzC,IAAAA,SAAQ,oBAAoB;AAC5B,IAAAA,SAAQ,qBAAqB;AAC7B,IAAAA,SAAQ,qBAAqB;AAC7B,IAAAA,SAAQ,iBAAiB;AACzB,IAAAA,SAAQ,gBAAgB;AACxB,IAAAA,SAAQ,UAAU;AAClB,IAAAA,SAAQ,yBAAyBS;AACjC,IAAAT,SAAQ,+BAA+B;AACvC,IAAAA,SAAQ,wBAAwB;AAChC,IAAAA,SAAQ,4BAA4B;AACpC,IAAAA,SAAQ,cAAcO;AACtB,IAAAP,SAAQ,UAAU;AAClB,IAAAA,SAAQ,iCAAiC;AACzC,IAAAA,SAAQ,8BAA8B;AACtC,IAAAA,SAAQ,QAAQ;AAAA;AAAA;;;ACpiGhB;AAAA,+DAAAwB,UAAA;AAAA;AAEA,QAAI,cAAc;AAGlB,aAAS,eAAe;AAEpB,aAAO,OAAO,gCAAgC,cAAc,8BAA8B;AAAA,IAC9F;AACA,aAAS,4BAA4B,MAAM,SAAS;AAChD,YAAM,UAAU,aAAa;AAC7B,aAAO,YAAY,cAAc,eAAe,cAAc,KAAK,CAAC,eAAa;AAC7E,YAAI,WAAW,WAAW,QAAQ,OAAO,SAAS;AAC9C,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,QAAQ,SAAS,QAAQ,CAAC,WAAW,QAAQ,WAAW,CAAC,SAAS;AAC7E,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,QAAQ,SAAS,QAAQ,WAAW,WAAW,QAAQ,YAAY,SAAS;AACvF,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAEA,IAAAA,SAAQ,8BAA8B;AAAA;AAAA;;;ACzBtC,IAAAC,qBAAA;AAAA,+DAAAC,UAAA;AAEA,QAAI,cAAc;AAClB,QAAI,aAAa;AACjB,QAAI,QAAQ;AAEZ,aAAS,eAAe,SAAS;AAE7B,YAAM,8BAA8B,YAAY,+BAA+B,KAAK,YAAY;AAChG,aAAO,IAAI,4BAA4B,OAAO;AAAA,IAClD;AACA,QAAI,qBAAqB;AAGrB,aAAS,KAAK,SAAS;AAEvB,YAAM,WAAW,MAAM,4BAA4B,QAAQ,MAAM,QAAQ,OAAO;AAChF,UAAI,CAAC,UAAU;AACX,6BAAqB,eAAe,OAAO;AAC3C,oBAAY,4BAA4B,kBAAkB;AAC1D,eAAO;AAAA,MACX,OAAO;AAEH,iBAAS,YAAY,OAAO;AAC5B,YAAI,CAAC,oBAAoB;AACrB,+BAAqB;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,aAAS,cAAc,MAAM;AACzB,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AACrH,YAAM,cAAc,mBAAmB;AAEvC,aAAO,YAAY,MAAM,oBAAoB,IAAI;AAAA,IACrD;AACA,aAAS,aAAa,MAAM;AACxB,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AAErH,YAAM,aAAa,mBAAmB;AACtC,aAAO,WAAW,MAAM,oBAAoB,IAAI;AAAA,IACpD;AACA,aAAS,iBAAiB,MAAM;AAC5B,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AACrH,YAAM,iBAAiB,mBAAmB;AAE1C,aAAO,eAAe,MAAM,oBAAoB,IAAI;AAAA,IACxD;AACA,aAAS,iBAAiB,MAAM;AAC5B,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AAErH,aAAO,mBAAmB,cAAc,MAAM,oBAAoB,IAAI;AAAA,IAC1E;AACA,aAAS,mBAAmB,MAAM;AAC9B,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AAErH,aAAO,mBAAmB,gBAAgB,MAAM,oBAAoB,IAAI;AAAA,IAC5E;AACA,aAAS,mBAAmB,MAAM;AAC9B,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AAErH,aAAO,mBAAmB,gBAAgB,MAAM,oBAAoB,IAAI;AAAA,IAC5E;AACA,aAAS,cAAc;AACnB,aAAO;AAAA,IACX;AACA,aAAS,kBAAkB,MAAM;AAC7B,kBAAY,OAAO,oBAAoB,WAAW,iBAAiB,WAAW,aAAa,WAAW,cAAc,CAAC;AAErH,aAAO,mBAAmB,eAAe,MAAM,oBAAoB,IAAI;AAAA,IAC3E;AAEA,gBAAY,+BAA+B,YAAY,gBAAgB;AAEvE,IAAAA,SAAQ,SAAS,YAAY;AAC7B,IAAAA,SAAQ,mBAAmB,YAAY;AACvC,IAAAA,SAAQ,iBAAiB,YAAY;AACrC,IAAAA,SAAQ,gBAAgB,YAAY;AACpC,IAAAA,SAAQ,aAAa,YAAY;AACjC,IAAAA,SAAQ,iBAAiB,YAAY;AACrC,IAAAA,SAAQ,wBAAwB,YAAY;AAC5C,IAAAA,SAAQ,iBAAiB;AACzB,IAAAA,SAAQ,cAAc;AACtB,IAAAA,SAAQ,OAAO;AACf,IAAAA,SAAQ,aAAa;AACrB,IAAAA,SAAQ,YAAY;AACpB,IAAAA,SAAQ,gBAAgB;AACxB,IAAAA,SAAQ,gBAAgB;AACxB,IAAAA,SAAQ,kBAAkB;AAC1B,IAAAA,SAAQ,kBAAkB;AAC1B,IAAAA,SAAQ,iBAAiB;AAAA;AAAA;", "names": ["exports", "require_polyfills_cjs", "exports", "error", "module", "url", "res", "path", "script", "attrs", "loaderHook", "init", "f", "cb", "scriptContext", "vm", "require_index_cjs", "exports", "errorDescMap", "require_index_cjs", "exports", "polyfills", "LOG_CATEGORY", "logger", "assert", "error", "warn", "safeWrapper", "res", "isStaticResourcesEqual", "f", "preRelease", "mainVersion", "gtlt", "comparator", "cb", "loaderHook", "url", "attrs", "module", "id", "data", "index", "globalSnapshotRes", "exports", "require_index_cjs", "exports"]}