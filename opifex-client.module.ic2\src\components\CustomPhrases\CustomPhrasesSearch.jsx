import { useContext, useEffect, useState } from "react";
import { Button, Flex, Form, Input, Modal, Table } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";

const CustomPhrasesSearch = ({
  isOpen,
  onClose,
  customPhrasesList,
  selectCustomPhrases,
  onSelectCustomPhrases,
}) => {
  const [customPhrases, setCustomPhrases] = useState(
    Object.values(customPhrasesList)
  );
  const [selectPhrases, setSelectPhrases] = useState([]);
  const settings = useContext(SettingsContext);
  const [form] = Form.useForm();
  const { isMobile, inputStyle } = useInputStyle();

  /**
   * @type {import("antd").TableProps<DataType>['columns']}
   */
  const columns = [
    {
      title: "Keywords",
      dataIndex: "key",
      key: "key",
      align: "center",
      width: 200,
      fixed: !isMobile ? "left" : false,
      showSorterTooltip: {
        target: "full-header",
      },
      sorter: (a, b) => {
        if (a.key === b.key) {
          return 0;
        } else if (a.key > b.key) {
          return 1;
        } else {
          return -1;
        }
      },
      render: (cp) => cp,
    },
    ...(settings?.basicSettings?.availableLanguages
      ? settings.basicSettings.availableLanguages.map((lang) => ({
          title: lang,
          align: "center",
          width: 200,
          dataIndex: "",
          key: "",
          render: (_, record) => record.value[lang],
        }))
      : []),
  ];

  const searchCustomPhrases = (searchTerm) => {
    if (!searchTerm) {
      setCustomPhrases(Object.values(customPhrasesList));
    } else {
      setCustomPhrases(
        Object.values(customPhrasesList).filter((cp) =>
          [cp.key, Object.values(cp.value).join(" ")]
            .join(" ")
            .toLocaleLowerCase()
            .includes(searchTerm.toLocaleLowerCase().trim())
        )
      );
    }
  };

  const handleOk = () => {
    if (onSelectCustomPhrases) {
      onSelectCustomPhrases(
        Object.values(customPhrasesList).filter((cp) =>
          selectPhrases.includes(cp.key)
        )
      );
    }
    handleReset();
  };

  const handleCancel = () => {
    handleReset();
  };

  const handleReset = () => {
    setCustomPhrases(Object.values(customPhrasesList));
    form.resetFields();
    onClose();
  };

  useEffect(() => {
    setCustomPhrases(Object.values(customPhrasesList));
  }, [customPhrasesList]);

  useEffect(() => {
    setSelectPhrases(selectCustomPhrases?.map((i) => i.key));
  }, [selectCustomPhrases]);

  const rowSelection = {
    selectedRowKeys: selectPhrases,
    onSelect: (record, select) => {
      if (select) {
        setSelectPhrases((selectPhrases) => [...selectPhrases, record.key]);
      } else {
        setSelectPhrases((selectPhrases) =>
          selectPhrases.filter((key) => key !== record.key)
        );
      }
    },
    hideSelectAll: true,
  };
  return (
    <Modal
      open={isOpen}
      title={"Add custom phrases"}
      onCancel={handleCancel}
      onOk={handleOk}
      width={"max-content"}
      style={{ maxWidth: "90vw" }}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ searchTerm: "" }}
        onFinish={({ searchTerm }) => searchCustomPhrases(searchTerm)}
        className="mt-5"
      >
        <Flex
          vertical={isMobile}
          justify="flex-start"
          align={isMobile ? "flex-start" : "flex-end"}
          style={{ columnGap: 16 }}
        >
          <Form.Item
            style={inputStyle}
            name={"searchTerm"}
            label={"Search phrases"}
          >
            <Input style={inputStyle} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              <SearchOutlined style={{ fontSize: 20 }} />
            </Button>
          </Form.Item>
        </Flex>
      </Form>
      <Table
        size={isMobile ? "small" : "middle"}
        columns={columns}
        rowSelection={rowSelection}
        dataSource={customPhrases}
        bordered
        style={{ width: "max-content" }}
        scroll={{ x: "max-content", y: 400 }}
        tableLayout="auto"
        showSorterTooltip={{
          target: "sorter-icon",
        }}
      />
    </Modal>
  );
};

export default CustomPhrasesSearch;
