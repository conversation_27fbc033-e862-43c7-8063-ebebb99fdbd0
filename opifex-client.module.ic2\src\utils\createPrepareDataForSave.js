import { convertFormDataToCssSelector } from "./transformDataToCss";

/**
 *
 * @param {() => ({xml: Record<string, any>, css: Record<string, import("react").CSSProperties>})} onSubmit
 */
export function createPrepareDataForSave(onSubmit) {
  return (config) => {
    const data = onSubmit(config);
    const cssApiPayload = convertFormDataToCssSelector(data.css);
    const styleUri = data.xml?.styleUri ?? "";
    if (data?.xml?.styleUri) delete data.xml.styleUri;
    return {
      styleUri,
      jsonSettings: JSON.stringify(data.xml),
      isMinifyCss: false,
      settingsCss: cssApiPayload,
    };
  };
}
