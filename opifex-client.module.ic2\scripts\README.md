Set execute permissions in Git so don't need chmod in CI

```bash
# Set execute permissions
chmod +x ./scripts/update-versions-semantic.sh
chmod +x ./scripts/package-client-semantic.sh

# Tell Git to track the permission changes
git update-index --chmod=+x ./scripts/update-versions-semantic.sh
git update-index --chmod=+x ./scripts/package-client-semantic.sh

# Commit and push
git add ./scripts/
git commit -m "Make scripts executable"
git push
```
