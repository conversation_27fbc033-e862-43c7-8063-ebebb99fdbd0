{"version": 3, "sources": ["../../dayjs/plugin/weekday.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_weekday=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.weekday=function(e){var t=this.$locale().weekStart||0,i=this.$W,n=(i<t?i+7:i)-t;return this.$utils().u(e)?n:this.subtract(n,\"day\").add(e,\"day\")}}}));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,uBAAqB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE;AAAC,UAAE,UAAU,UAAQ,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK,QAAQ,EAAE,aAAW,GAAE,IAAE,KAAK,IAAG,KAAG,IAAEA,KAAE,IAAE,IAAE,KAAGA;AAAE,iBAAO,KAAK,OAAO,EAAE,EAAED,EAAC,IAAE,IAAE,KAAK,SAAS,GAAE,KAAK,EAAE,IAAIA,IAAE,KAAK;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "t"]}