import { useState, useEffect, useContext, Fragment, useCallback } from "react";
import { Button, Flex, Form, Table, Tooltip } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import CustomInputTableCell from "../common/CustomInputTableCell";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";
import {
  createUniqueKeyGenerator,
  formatLanguageForwardingToString,
  parseLanguageForwardingString,
} from "../../utils/common";
import { useMemo } from "react";

const generateUniqueKey = createUniqueKeyGenerator("LF");

const LanguageForwarding = ({
  onChange,
  form,
  sources,
  fieldPrefix = ["PressReleases"],
}) => {
  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const [languageForwardingDatas, setLanguageForwardingDatas] = useState([]);
  const languageForwardingSourceOptions = Object.values(sources)?.map(
    (source) => ({
      label: source?.name,
      value: source?.id?.toString(),
    })
  );
  const languageOptions = Object.values(settings?.availableLanguages || {});

  const editLanguageForwardingForm = Form.useWatch(
    [...fieldPrefix, "EditLanguageForwarding"],
    { form, preserve: true }
  );

  /**
   * Effect to handle language forwarding updates for press releases.
   * Processes the editLanguageForwardingForm data to:
   * 1. Filter out empty entries
   * 2. Map each entry to a structured object with Source, SourceLanguage, and DestinationLanguage
   * 3. Format the data into a string representation
   * 4. Update the form with the new language forwarding configuration
   *
   * @effect
   * @dependencies {Object} editLanguageForwardingForm - Form data containing language forwarding configurations
   * @dependencies {Function} form.setFieldValue - Antd form setter function
   * @dependencies {Function} onChange - Optional callback function to update the parent form
   */
  useEffect(() => {
    const newLanguageForwarding = formatLanguageForwardingToString(
      Object.values(editLanguageForwardingForm || {})
        ?.filter((item) => !!item)
        ?.map((item) => ({
          Source: item?.Source || "",
          SourceLanguage: item?.SourceLanguage || "",
          DestinationLanguage: item?.DestinationLanguage || "",
        }))
    );
    form.setFieldValue(
      [...fieldPrefix, "LanguageForwarding"],
      newLanguageForwarding
    );
    if (onChange) {
      onChange();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editLanguageForwardingForm]);

  /**
   * Effect to initialize language forwarding data for press releases.
   * Handles two scenarios:
   * 1. Initial setup: When EditLanguageForwardingKeys is not present
   *    - Parses LanguageForwarding string into structured data
   *    - Filters sources against available sources
   *    - Generates unique keys for each forwarding entry
   *    - Updates form with initial forwarding data and keys
   * 2. Existing setup: When EditLanguageForwardingKeys exists
   *    - Maps existing forwarding keys to their corresponding values
   *
   * @effect
   * @dependencies {Object} settings.initState.PressReleases - Initial press release settings
   * @dependencies {Object} sources - Available sources configuration
   * @dependencies {Function} form.setFieldValue - Antd form setter function
   * @dependencies {Function} setLanguageForwardingDatas - State setter for language forwarding data
   * @dependencies {Function} onChange - Optional callback function to update the parent form
   */
  useEffect(() => {
    // Get initial data based on fieldPrefix
    const isInstrumentConfig = fieldPrefix.length > 1;
    let initialData = "";

    if (isInstrumentConfig) {
      // For instrument config, get from Instruments.Instrument array
      const instrumentId = fieldPrefix[1];
      const instrumentData = settings?.initState?.Instruments?.Instrument?.find(
        (inst) => inst.Id?.toString() === instrumentId?.toString()
      );
      initialData = instrumentData?.PressRelease?.LanguageForwarding || "";
    } else {
      // For global config
      initialData =
        settings?.initState?.PressReleases?.LanguageForwarding || "";
    }

    if (!form.getFieldValue([...fieldPrefix, "EditLanguageForwardingKeys"])) {
      const editLangForward = parseLanguageForwardingString(initialData)
        ?.filter((item) => Object.keys(sources || {}).includes(item.Source))
        ?.reduce(
          (editLangForwardObject, item) => ({
            ...editLangForwardObject,
            [generateUniqueKey()]: item,
          }),
          {}
        );
      const langForwardingKeys = Object.keys(editLangForward || {});
      const langForwardDatas = langForwardingKeys?.map((itemKey) => ({
        key: itemKey,
        value: editLangForward[itemKey],
      }));

      form.setFieldValue(
        [...fieldPrefix, "EditLanguageForwardingKeys"],
        langForwardingKeys
      );
      form.setFieldValue(
        [...fieldPrefix, "EditLanguageForwarding"],
        editLangForward
      );
      setLanguageForwardingDatas(langForwardDatas || []);

      if (onChange) {
        onChange();
      }
    } else {
      const existingKeys = form.getFieldValue([
        ...fieldPrefix,
        "EditLanguageForwardingKeys",
      ]);
      const existingData = form.getFieldValue([
        ...fieldPrefix,
        "EditLanguageForwarding",
      ]);
      setLanguageForwardingDatas(
        existingKeys?.map((itemKey) => ({
          key: itemKey,
          value: existingData?.[itemKey],
        })) || []
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sources]);

  /**
   * Handles adding a new language forwarding to the form.
   * 1. Generates a unique key for the new forwarding entry
   * 2. Adds the new forwarding entry to the languageForwardingDatas state
   * 3. Updates the form with the new key and forwarding entry
   * 4. Calls the onChange callback function to update the parent form
   *
   * @returns {void}
   */
  const handleAddLanguageForwarding = () => {
    const newKey = generateUniqueKey();

    const newLanguageForwardingDatas = [
      ...languageForwardingDatas,
      { key: newKey },
    ];
    setLanguageForwardingDatas(newLanguageForwardingDatas);
    const currentKeys =
      form.getFieldValue([...fieldPrefix, "EditLanguageForwardingKeys"]) || [];
    form.setFieldValue(
      [...fieldPrefix, "EditLanguageForwardingKeys"],
      [...currentKeys, newKey]
    );

    if (onChange) {
      onChange();
    }
  };

  /**
   * Handles deleting a language forwarding from the form.
   * 1. Retrieves the key of the forwarding to be deleted
   * 2. Filters out the specified forwarding from the languageForwardingDatas state
   * 3. Updates the form with the remaining forwarding keys
   * 4. Calls the onChange callback function to update the parent form
   *
   * @param {number} index - The index of the forwarding to be deleted
   * @returns {void}
   */
  const handleDeleteLanguageForwarding = useCallback((index) => {
    const keyToDelete = languageForwardingDatas[index]?.key;

    setLanguageForwardingDatas((languageForwardingDatas) =>
      languageForwardingDatas.filter((_, i) => i !== index)
    );
    const currentKeys =
      form.getFieldValue([...fieldPrefix, "EditLanguageForwardingKeys"]) || [];
    form.setFieldValue(
      [...fieldPrefix, "EditLanguageForwardingKeys"],
      currentKeys.filter((key) => key !== keyToDelete)
    );
    form.setFieldValue(
      [...fieldPrefix, "EditLanguageForwarding", keyToDelete],
      undefined
    );

    if (onChange) {
      onChange();
    }
  }, [languageForwardingDatas, form, fieldPrefix, onChange]);

  /**
   * Defines the columns for the language forwarding table.
   * 1. Source column: Displays a dropdown for selecting a source
   * 2. Source Language column: Displays a dropdown for selecting a source language
   * 3. Destination Language column: Displays a dropdown for selecting a destination language
   * 4. Action column: Displays a delete button for removing the forwarding
   *
   * @type {import("antd").TableProps<DataType>['columns']}
   */
  const languageForwardingColumns = useMemo(
    () => [
      {
        title: "Source",
        align: "left",
        minWidth: 300,
        key: "Source",
        render: (_, record) => (
          <CustomInputTableCell
            type={"select"}
            options={languageForwardingSourceOptions}
            form={form}
            name={[
              ...fieldPrefix,
              "EditLanguageForwarding",
              `${record.key}`,
              "Source",
            ]}
            rules={[{ required: true, message: "Source is required" }]}
            defaultValue={record.value?.["Source"]}
            // isDirectEdit={true}
          />
        ),
      },
      {
        title: "Source Language",
        align: "left",
        minWidth: 300,
        key: "ForwardedLanguage",
        render: (_, record) => (
          <CustomInputTableCell
            type={"select"}
            options={languageOptions}
            form={form}
            name={[
              ...fieldPrefix,
              "EditLanguageForwarding",
              `${record.key}`,
              "ForwardedLanguage",
            ]}
            rules={[
              { required: true, message: "Forwarded Language is required" },
            ]}
            defaultValue={record.value?.["ForwardedLanguage"]}
            // isDirectEdit={true}
          />
        ),
      },
      {
        title: "Destination Language",
        align: "left",
        minWidth: 300,
        key: "DisplayLanguage",
        render: (_, record) => (
          <CustomInputTableCell
            type={"select"}
            options={languageOptions}
            form={form}
            name={[
              ...fieldPrefix,
              "EditLanguageForwarding",
              `${record.key}`,
              "DisplayLanguage",
            ]}
            rules={[
              { required: true, message: "Display Language is required" },
            ]}
            defaultValue={record.value?.["DisplayLanguage"]}
            // isDirectEdit={true}
          />
        ),
      },
      {
        title: "Action",
        align: "center",
        width: 100,
        fixed: !isMobile ? "right" : false,
        key: "Action",
        render: (_, record, index) => (
          <Tooltip title={`Delete this language forwarding`}>
            <Button
              type="primary"
              danger
              onClick={() => handleDeleteLanguageForwarding(index)}
              icon={<DeleteOutlined />}
            />
          </Tooltip>
        ),
      },
    ],
    [languageForwardingSourceOptions, languageOptions, form, fieldPrefix, handleDeleteLanguageForwarding, isMobile]
  );

  return (
    <Fragment>
      <Flex justify="flex-start" align="center" className="mb-5">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddLanguageForwarding}
        >
          Add language forwarding
        </Button>
      </Flex>

      <Table
        key={`language-forwarding-${fieldPrefix.join("-")}`}
        size={isMobile ? "small" : "default"}
        columns={languageForwardingColumns}
        dataSource={languageForwardingDatas}
        bordered
        pagination={false}
        style={{ width: "100%", marginTop: 16, marginBottom: 24 }}
        scroll={{ x: "max-content", y: 400 }}
        tableLayout="auto"
        rowKey={`language-forwarding-${fieldPrefix.join("-")}`}
      />
    </Fragment>
  );
};

export default LanguageForwarding;
