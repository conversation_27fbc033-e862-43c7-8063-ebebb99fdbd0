#!/bin/sh
# scripts/create-release-branch.sh
# This script uses semantic-release to determine the next version, then creates the branch

# This will:
# 1. Run semantic-release in dry-run mode
# 2. Determine the next version (e.g., 1.3.0)
# 3. Create branch: release/v1.3.0
# 4. Push the branch

set -e

npm install @semantic-release/changelog@6.0.3 @semantic-release/commit-analyzer@10.0.4 @semantic-release/git@10.0.1 @semantic-release/exec@6.0.3 @semantic-release/gitlab@12.1.0 @semantic-release/release-notes-generator@11.0.7

echo "🔍 Analyzing commits to determine next version..."

# Run semantic-release in dry-run mode to get the next version
NEXT_VERSION=$(npx semantic-release --dry-run --no-ci | grep "The next release version is" | sed 's/.*The next release version is //')

if [ -z "$NEXT_VERSION" ]; then
    echo "❌ No release needed or unable to determine next version"
    echo "   Check if there are releasable commits since the last release"
    exit 1
fi

echo "📦 Next version will be: $NEXT_VERSION"

# Check if release branch already exists
RELEASE_BRANCH="release/v$NEXT_VERSION"
if git show-ref --verify --quiet refs/heads/$RELEASE_BRANCH; then
    echo "❌ Release branch $RELEASE_BRANCH already exists"
    exit 1
fi

# Create the release branch
echo "🌿 Creating release branch: $RELEASE_BRANCH"
git checkout develop
git pull origin develop
git checkout -b $RELEASE_BRANCH

# Push the release branch
git push origin $RELEASE_BRANCH

echo "✅ Release branch created: $RELEASE_BRANCH"
echo "🚀 Push commits to this branch to trigger RC releases"
echo "📋 When ready, merge this branch to master for final release"

# Optional: Open the branch in GitLab/GitHub for MR creation
if command -v gh &> /dev/null; then
    echo "🔗 Creating merge request..."
    gh pr create --title "Release v$NEXT_VERSION" --body "Release candidate for version $NEXT_VERSION" --base master --head $RELEASE_BRANCH
fi
