import api from "./api";
import { API } from "../constant/service";

/**
 *
 * @param {Record<string, any>} params
 * @returns {Promise<{
 *  data: {Record<string, any>}
 * }>}
 */

export const getCssVersionsByCompanyCode = (params) =>
  api.GET(API.GET_CSS_VERSIONS_BY_COMPANY_CODE, params);

export const getCustomePhraseApi = (params) =>
  api.POST(API.GET_CUSTOME_PHRASES, params);

export const getInstruments = (params) => api.GET(API.GET_INSTRUMENT, params);
