import { convertDayjsFormatToDotnetFormat } from "@euroland/opifix-utils";

export function getLSTXmlSettings(basicSettings, generalTools, state) {
  const { currencyCodes, InstrumentConfigs,ChartColors, ...props } = state;
  const { mainFont, brandingColor } = basicSettings;
  const { 
    heading = {},
    hyperlinkNormal = {},
    hyperlinkActive = {},
    tableOddRowColor = {},
    tableEvenRowColor = {},
    tab,
    tabActive,
  } = generalTools;
  const textColor = mainFont?.color;
  const fontFamily = mainFont?.fontFamily;
  const fontSize = mainFont?.fontSize && `${mainFont?.fontSize}px`;
  const primaryColor = brandingColor?.primaryColor ?? '';

  const extractMultiLangField = (data, fieldName) => {
    const result = {};
    for (const [id, item] of Object.entries(data)) {
      if (item[fieldName] && typeof item[fieldName] === "object") {
        result[`_${id}`] = item[fieldName];
      }
    }
    return result;
  };

  const enabledIds = InstrumentConfigs
    ? Object?.entries(InstrumentConfigs)
        .filter(([_, value]) => value.EnabledDividendOption)
        .map(([key]) => key)
    : undefined;

  const extractConfigField = (configs, field) => {
    if (!configs) return undefined;

    return Object.entries(configs)
      .filter(([_, value]) => value[field])
      .reduce((acc, [key, value]) => {
        acc[`_${key}`] = value[field];
        console.log("value[field]", value[field]);
        return acc;
      }, {});
  };

  const updatedFormats =
    basicSettings?.Format &&
    Object.fromEntries(
      Object.entries(basicSettings.Format).map(([key, value]) => [
        key,
        {
          PercentDigits: value?.PercentDigits,
          DecimalSeparator: value?.DecimalSeparator,
          ThousandsSeparator: value?.ThousandsSeparator,
          DecimalDigits: value?.DecimalDigits,
          NegativeNumberFormat: value?.NegativeNumberFormat,
          LongDate: convertDayjsFormatToDotnetFormat(value?.LongDate),
          ShortDate: convertDayjsFormatToDotnetFormat(value?.ShortDate),
        },
      ])
    );
console.log('state',state)
  const xml = {
    ...props,
    MainTextColor:  props['MainTextColor'] || textColor,
    MainTextFontSize:  props['MainTextFontSize'] || fontSize,
    MainTextFont:  props['MainTextFont'] || fontFamily,
    MainHeadingFontSize:  props['MainHeadingFontSize'] || heading?.fontSize,
    MainHeadingColor:  props['MainHeadingColor'] || heading?.color,
    MainHeadingFont:  props['MainHeadingFont'] || heading?.fontFamily,
    LinkColor:  props['LinkColor'] || hyperlinkNormal?.color,
    LinkHoverColor:  props['LinkHoverColor'] || hyperlinkActive?.color,
    TableOddRowColor:  props['TableOddRowColor'] || tableOddRowColor?.backgroundColor,
    TableEvenRowColor:  props['TableEvenRowColor'] || tableEvenRowColor?.backgroundColor,
    TabActiveColor:  props['TabActiveColor'] || tabActive?.backgroundColor,
    TabDeactiveColor:  props['TabDeactiveColor'] || tab?.backgroundColor,
    Format: updatedFormats,
    TimeZone: basicSettings.timezone ?? "",
    EnabledFieldTradeHistories: state?.EnabledFieldTradeHistories ?? "",
    RefreshTimeOut: basicSettings?.dataRefreshRate ?? 60,
    InstrumentIds: basicSettings?.instrumentIds?.join(","),
    DefaultCulture: basicSettings?.availableLanguages?.join(","),
    Currencies: currencyCodes?.join(","),
    ChartColors: typeof ChartColors === "string" ? ChartColors : ChartColors?.filter(item => !!item)?.join(";") || primaryColor,
    EnabledDividendOption:
      enabledIds?.join(",") ?? basicSettings?.instrumentIds?.join(","),
    LimitInvestmentStartDate: extractConfigField(
      InstrumentConfigs,
      "LimitInvestmentStartDate"
    ),
    LimitStartingData: extractConfigField(
      InstrumentConfigs,
      "LimitStartingData"
    ),
  };
  if (basicSettings?.instrument) {
    xml["CustomInstrumentName"] = extractMultiLangField(
      basicSettings.instrument,
      "shareName"
    );
    xml["CustomMarketName"] = extractMultiLangField(
      basicSettings.instrument,
      "marketName"
    );
  }
  if (state?.EditCustomPhrases) {
    xml["CustomPhrases"] = structuredClone(state.EditCustomPhrases);
  }

  if (state?.StyleURI) {
    xml["StyleURI"] = state.StyleURI;
  }
  delete xml.InstrumentConfigs;
  delete xml.AllowedParentDomains;
  delete xml.Template;
  delete xml.Instruments;
  delete xml.RefreshTimeOut;
  delete xml.HideChart;
  delete xml.GoogleAnalyticsEnabled;
  delete xml.EnabledFieldTradeHistories;
  delete xml.EnabledColorBlindMode;
  delete xml.EnableExcelDownload;
  return xml;
}
