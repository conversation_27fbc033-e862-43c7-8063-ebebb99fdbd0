import { ColorPicker, Form, Input, InputNumber } from "antd";
import { useEffect, useRef, useState } from "react";

const CustomInputTableCell = ({
  form,
  name,
  defaultValue,
  type = "string",
  disabled,
  ...props
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    if (isEditing) {
      inputRef.current?.focus();
    }
  }, [isEditing]);

  const toggleEdit = () => {
    setIsEditing(!isEditing);
  };

  if (isEditing && !disabled) {
    switch (type) {
      case "string":
        return (
          <Form.Item name={name} noStyle initialValue={defaultValue}>
            <Input
              {...props}
              style={{ width: "100%" }}
              ref={inputRef}
              onPressEnter={toggleEdit}
              onBlur={toggleEdit}
            />
          </Form.Item>
        );
      case "number":
        return (
          <Form.Item name={name} noStyle initialValue={defaultValue}>
            <InputNumber
              {...props}
              style={{ width: "100%" }}
              ref={inputRef}
              onPressEnter={toggleEdit}
              onBlur={toggleEdit}
            />
          </Form.Item>
        );
      case "color":
        return (
          <Form.Item
            name={name}
            noStyle
            initialValue={defaultValue}
            getValueFromEvent={(value) => value.toHexString()}
          >
            <ColorPicker
              {...props}
              style={{ width: "100%" }}
              showText
              allowClear
              className="flex justify-start"
            />
          </Form.Item>
        );
      default:
        break;
    }
  } else {
    return (
      <div className="custom-table-cell" onClick={toggleEdit}>
        {form.getFieldValue(name) ?? defaultValue}
      </div>
    );
  }
};

export default CustomInputTableCell;
