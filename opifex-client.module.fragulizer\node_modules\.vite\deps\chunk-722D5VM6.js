import {
  require_fragulizer_mf_v_runtimeInit_mf_v
} from "./chunk-FM5QRY6S.js";
import {
  require_index_cjs
} from "./chunk-Q6BHGTTH.js";
import {
  __commonJS
} from "./chunk-ZKAWKZG5.js";

// node_modules/__mf__virtual/fragulizer__loadShare__antd__loadShare__.js
var require_fragulizer_loadShare_antd_loadShare = __commonJS({
  "node_modules/__mf__virtual/fragulizer__loadShare__antd__loadShare__.js"(exports, module) {
    var { loadShare } = require_index_cjs();
    var { initPromise } = require_fragulizer_mf_v_runtimeInit_mf_v();
    var res = initPromise.then((_) => loadShare("antd", {
      customShareInfo: { shareConfig: {
        singleton: true,
        strictVersion: false,
        requiredVersion: "^5.24.9"
      } }
    }));
    var exportModule = (
      /*mf top-level-await placeholder replacement mf*/
      res.then((factory) => factory())
    );
    module.exports = exportModule;
  }
});

export {
  require_fragulizer_loadShare_antd_loadShare
};
//# sourceMappingURL=chunk-722D5VM6.js.map
