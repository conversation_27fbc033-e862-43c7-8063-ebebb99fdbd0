{"version": 3, "sources": ["../../@dnd-kit/utilities/src/hooks/useCombinedRefs.ts", "../../@dnd-kit/utilities/src/execution-context/canUseDOM.ts", "../../@dnd-kit/utilities/src/type-guards/isWindow.ts", "../../@dnd-kit/utilities/src/type-guards/isNode.ts", "../../@dnd-kit/utilities/src/execution-context/getWindow.ts", "../../@dnd-kit/utilities/src/type-guards/isDocument.ts", "../../@dnd-kit/utilities/src/type-guards/isHTMLElement.ts", "../../@dnd-kit/utilities/src/type-guards/isSVGElement.ts", "../../@dnd-kit/utilities/src/execution-context/getOwnerDocument.ts", "../../@dnd-kit/utilities/src/hooks/useIsomorphicLayoutEffect.ts", "../../@dnd-kit/utilities/src/hooks/useEvent.ts", "../../@dnd-kit/utilities/src/hooks/useInterval.ts", "../../@dnd-kit/utilities/src/hooks/useLatestValue.ts", "../../@dnd-kit/utilities/src/hooks/useLazyMemo.ts", "../../@dnd-kit/utilities/src/hooks/useNodeRef.ts", "../../@dnd-kit/utilities/src/hooks/usePrevious.ts", "../../@dnd-kit/utilities/src/hooks/useUniqueId.ts", "../../@dnd-kit/utilities/src/adjustment.ts", "../../@dnd-kit/utilities/src/event/hasViewportRelativeCoordinates.ts", "../../@dnd-kit/utilities/src/event/isKeyboardEvent.ts", "../../@dnd-kit/utilities/src/event/isTouchEvent.ts", "../../@dnd-kit/utilities/src/coordinates/getEventCoordinates.ts", "../../@dnd-kit/utilities/src/css.ts", "../../@dnd-kit/utilities/src/focus/findFirstFocusableNode.ts", "../../@dnd-kit/accessibility/src/components/HiddenText/HiddenText.tsx", "../../@dnd-kit/accessibility/src/components/LiveRegion/LiveRegion.tsx", "../../@dnd-kit/accessibility/src/hooks/useAnnouncement.ts", "../../@dnd-kit/core/src/components/DndMonitor/context.ts", "../../@dnd-kit/core/src/components/DndMonitor/useDndMonitor.ts", "../../@dnd-kit/core/src/components/DndMonitor/useDndMonitorProvider.tsx", "../../@dnd-kit/core/src/components/Accessibility/defaults.ts", "../../@dnd-kit/core/src/components/Accessibility/Accessibility.tsx", "../../@dnd-kit/core/src/store/actions.ts", "../../@dnd-kit/core/src/utilities/other/noop.ts", "../../@dnd-kit/core/src/sensors/useSensor.ts", "../../@dnd-kit/core/src/sensors/useSensors.ts", "../../@dnd-kit/core/src/utilities/coordinates/constants.ts", "../../@dnd-kit/core/src/utilities/coordinates/distanceBetweenPoints.ts", "../../@dnd-kit/core/src/utilities/coordinates/getRelativeTransformOrigin.ts", "../../@dnd-kit/core/src/utilities/algorithms/helpers.ts", "../../@dnd-kit/core/src/utilities/algorithms/closestCenter.ts", "../../@dnd-kit/core/src/utilities/algorithms/closestCorners.ts", "../../@dnd-kit/core/src/utilities/algorithms/rectIntersection.ts", "../../@dnd-kit/core/src/utilities/algorithms/pointerWithin.ts", "../../@dnd-kit/core/src/utilities/rect/adjustScale.ts", "../../@dnd-kit/core/src/utilities/rect/getRectDelta.ts", "../../@dnd-kit/core/src/utilities/rect/rectAdjustment.ts", "../../@dnd-kit/core/src/utilities/transform/parseTransform.ts", "../../@dnd-kit/core/src/utilities/transform/inverseTransform.ts", "../../@dnd-kit/core/src/utilities/rect/getRect.ts", "../../@dnd-kit/core/src/utilities/rect/getWindowClientRect.ts", "../../@dnd-kit/core/src/utilities/scroll/isFixed.ts", "../../@dnd-kit/core/src/utilities/scroll/isScrollable.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollableAncestors.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollableElement.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollCoordinates.ts", "../../@dnd-kit/core/src/types/direction.ts", "../../@dnd-kit/core/src/utilities/scroll/documentScrollingElement.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollPosition.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollDirectionAndSpeed.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollElementRect.ts", "../../@dnd-kit/core/src/utilities/scroll/getScrollOffsets.ts", "../../@dnd-kit/core/src/utilities/scroll/scrollIntoViewIfNeeded.ts", "../../@dnd-kit/core/src/utilities/rect/Rect.ts", "../../@dnd-kit/core/src/sensors/utilities/Listeners.ts", "../../@dnd-kit/core/src/sensors/utilities/getEventListenerTarget.ts", "../../@dnd-kit/core/src/sensors/utilities/hasExceededDistance.ts", "../../@dnd-kit/core/src/sensors/events.ts", "../../@dnd-kit/core/src/sensors/keyboard/types.ts", "../../@dnd-kit/core/src/sensors/keyboard/defaults.ts", "../../@dnd-kit/core/src/sensors/keyboard/KeyboardSensor.ts", "../../@dnd-kit/core/src/sensors/pointer/AbstractPointerSensor.ts", "../../@dnd-kit/core/src/sensors/pointer/PointerSensor.ts", "../../@dnd-kit/core/src/sensors/mouse/MouseSensor.ts", "../../@dnd-kit/core/src/sensors/touch/TouchSensor.ts", "../../@dnd-kit/core/src/hooks/utilities/useAutoScroller.ts", "../../@dnd-kit/core/src/hooks/utilities/useCachedNode.ts", "../../@dnd-kit/core/src/hooks/utilities/useCombineActivators.ts", "../../@dnd-kit/core/src/hooks/utilities/useDroppableMeasuring.ts", "../../@dnd-kit/core/src/hooks/utilities/useInitialValue.ts", "../../@dnd-kit/core/src/hooks/utilities/useInitialRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useMutationObserver.ts", "../../@dnd-kit/core/src/hooks/utilities/useResizeObserver.ts", "../../@dnd-kit/core/src/hooks/utilities/useRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useRectDelta.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollableAncestors.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollOffsets.ts", "../../@dnd-kit/core/src/hooks/utilities/useScrollOffsetsDelta.ts", "../../@dnd-kit/core/src/hooks/utilities/useSensorSetup.ts", "../../@dnd-kit/core/src/hooks/utilities/useSyntheticListeners.ts", "../../@dnd-kit/core/src/hooks/utilities/useWindowRect.ts", "../../@dnd-kit/core/src/hooks/utilities/useRects.ts", "../../@dnd-kit/core/src/utilities/nodes/getMeasurableNode.ts", "../../@dnd-kit/core/src/hooks/utilities/useDragOverlayMeasuring.ts", "../../@dnd-kit/core/src/components/DndContext/defaults.ts", "../../@dnd-kit/core/src/store/constructors.ts", "../../@dnd-kit/core/src/store/context.ts", "../../@dnd-kit/core/src/store/reducer.ts", "../../@dnd-kit/core/src/components/Accessibility/components/RestoreFocus.tsx", "../../@dnd-kit/core/src/modifiers/applyModifiers.ts", "../../@dnd-kit/core/src/components/DndContext/hooks/useMeasuringConfiguration.ts", "../../@dnd-kit/core/src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "../../@dnd-kit/core/src/components/DndContext/DndContext.tsx", "../../@dnd-kit/core/src/hooks/useDraggable.ts", "../../@dnd-kit/core/src/hooks/useDndContext.ts", "../../@dnd-kit/core/src/hooks/useDroppable.ts", "../../@dnd-kit/core/src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "../../@dnd-kit/core/src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "../../@dnd-kit/core/src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "../../@dnd-kit/core/src/components/DragOverlay/hooks/useDropAnimation.ts", "../../@dnd-kit/core/src/components/DragOverlay/hooks/useKey.ts", "../../@dnd-kit/core/src/components/DragOverlay/DragOverlay.tsx", "../../@dnd-kit/sortable/src/utilities/arrayMove.ts", "../../@dnd-kit/sortable/src/utilities/arraySwap.ts", "../../@dnd-kit/sortable/src/utilities/getSortedRects.ts", "../../@dnd-kit/sortable/src/utilities/isValidIndex.ts", "../../@dnd-kit/sortable/src/utilities/itemsEqual.ts", "../../@dnd-kit/sortable/src/utilities/normalizeDisabled.ts", "../../@dnd-kit/sortable/src/strategies/horizontalListSorting.ts", "../../@dnd-kit/sortable/src/strategies/rectSorting.ts", "../../@dnd-kit/sortable/src/strategies/rectSwapping.ts", "../../@dnd-kit/sortable/src/strategies/verticalListSorting.ts", "../../@dnd-kit/sortable/src/components/SortableContext.tsx", "../../@dnd-kit/sortable/src/hooks/defaults.ts", "../../@dnd-kit/sortable/src/hooks/utilities/useDerivedTransform.ts", "../../@dnd-kit/sortable/src/hooks/useSortable.ts", "../../@dnd-kit/sortable/src/types/type-guard.ts", "../../@dnd-kit/sortable/src/sensors/keyboard/sortableKeyboardCoordinates.ts", "../../node_modules/@babel/runtime/helpers/esm/extends.js", "../../node_modules/@ant-design/icons-svg/es/asn/CloseOutlined.js", "../../node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js", "../../node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js", "../../node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js", "../../node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js", "../../node_modules/@babel/runtime/helpers/esm/nonIterableRest.js", "../../node_modules/@babel/runtime/helpers/esm/slicedToArray.js", "../../node_modules/@babel/runtime/helpers/esm/typeof.js", "../../node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "../../node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "../../node_modules/@babel/runtime/helpers/esm/defineProperty.js", "../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../../@euroland/react-antd-draggable-select/dist/_virtual/_commonjsHelpers.es.js", "../../@euroland/react-antd-draggable-select/dist/_virtual/index.es.js", "../../node_modules/classnames/index.js", "../../node_modules/@ant-design/colors/es/presets.js", "../../node_modules/@ant-design/icons/es/components/Context.js", "../../node_modules/@babel/runtime/helpers/esm/objectSpread2.js", "../../node_modules/@ant-design/fast-color/es/FastColor.js", "../../node_modules/@ant-design/colors/es/generate.js", "../../node_modules/rc-util/es/Dom/canUseDom.js", "../../node_modules/rc-util/es/Dom/contains.js", "../../node_modules/rc-util/es/Dom/dynamicCSS.js", "../../node_modules/rc-util/es/Dom/shadow.js", "../../node_modules/rc-util/es/warning.js", "../../node_modules/@ant-design/icons/es/utils.js", "../../node_modules/@ant-design/icons/es/components/IconBase.js", "../../node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js", "../../node_modules/@ant-design/icons/es/components/AntdIcon.js", "../../node_modules/@ant-design/icons/es/icons/CloseOutlined.js", "../../@euroland/react-antd-draggable-select/src/libs/ReactAntdDraggableSelect.tsx"], "sourcesContent": ["import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n", "import React from 'react';\n\ninterface Props {\n  id: string;\n  value: string;\n}\n\nconst hiddenStyles: React.CSSProperties = {\n  display: 'none',\n};\n\nexport function HiddenText({id, value}: Props) {\n  return (\n    <div id={id} style={hiddenStyles}>\n      {value}\n    </div>\n  );\n}\n", "import React from 'react';\n\nexport interface Props {\n  id: string;\n  announcement: string;\n  ariaLiveType?: \"polite\" | \"assertive\" | \"off\";\n}\n\nexport function LiveRegion({id, announcement, ariaLiveType = \"assertive\"}: Props) {\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap',\n  };\n  \n  return (\n    <div\n      id={id}\n      style={visuallyHidden}\n      role=\"status\"\n      aria-live={ariaLiveType}\n      aria-atomic\n    >\n      {announcement}\n    </div>\n  );\n}\n", "import {useCallback, useState} from 'react';\n\nexport function useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback((value: string | undefined) => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n\n  return {announce, announcement} as const;\n}\n", "import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n", "/**\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\n */\nexport function arrayMove<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n  newArray.splice(\n    to < 0 ? newArray.length + to : to,\n    0,\n    newArray.splice(from, 1)[0]\n  );\n\n  return newArray;\n}\n", "/**\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\n */\nexport function arraySwap<T>(array: T[], from: number, to: number): T[] {\n  const newArray = array.slice();\n\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n\n  return newArray;\n}\n", "import type {\n  ClientRect,\n  UniqueIdentifier,\n  UseDndContextReturnValue,\n} from '@dnd-kit/core';\n\nexport function getSortedRects(\n  items: UniqueIdentifier[],\n  rects: UseDndContextReturnValue['droppableRects']\n) {\n  return items.reduce<ClientRect[]>((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n", "export function isValidIndex(index: number | null): index is number {\n  return index !== null && index >= 0;\n}\n", "import type {UniqueIdentifier} from '@dnd-kit/core';\n\nexport function itemsEqual(a: UniqueIdentifier[], b: UniqueIdentifier[]) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import type {Disabled} from '../types';\n\nexport function normalizeDisabled(disabled: boolean | Disabled): Disabled {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled,\n    };\n  }\n\n  return disabled;\n}\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const horizontalListSortingStrategy: SortingStrategy = ({\n  rects,\n  activeNodeRect: fallbackActiveRect,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x:\n        activeIndex < overIndex\n          ? newIndexRect.left +\n            newIndexRect.width -\n            (activeNodeRect.left + activeNodeRect.width)\n          : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(rects: ClientRect[], index: number, activeIndex: number) {\n  const currentRect: ClientRect | undefined = rects[index];\n  const previousRect: ClientRect | undefined = rects[index - 1];\n  const nextRect: ClientRect | undefined = rects[index + 1];\n\n  if (!currentRect || (!previousRect && !nextRect)) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.left - (previousRect.left + previousRect.width)\n      : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect\n    ? nextRect.left - (currentRect.left + currentRect.width)\n    : currentRect.left - (previousRect.left + previousRect.width);\n}\n", "import {arrayMove} from '../utilities';\nimport type {SortingStrategy} from '../types';\n\nexport const rectSortingStrategy: SortingStrategy = ({\n  rects,\n  activeIndex,\n  overIndex,\n  index,\n}) => {\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {SortingStrategy} from '../types';\n\nexport const rectSwappingStrategy: SortingStrategy = ({\n  activeIndex,\n  index,\n  rects,\n  overIndex,\n}) => {\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {SortingStrategy} from '../types';\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport const verticalListSortingStrategy: SortingStrategy = ({\n  activeIndex,\n  activeNodeRect: fallbackActiveRect,\n  index,\n  rects,\n  overIndex,\n}) => {\n  const activeNodeRect = rects[activeIndex] ?? fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y:\n        activeIndex < overIndex\n          ? overIndexRect.top +\n            overIndexRect.height -\n            (activeNodeRect.top + activeNodeRect.height)\n          : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale,\n    };\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale,\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale,\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale,\n  };\n};\n\nfunction getItemGap(\n  clientRects: ClientRect[],\n  index: number,\n  activeIndex: number\n) {\n  const currentRect: ClientRect | undefined = clientRects[index];\n  const previousRect: ClientRect | undefined = clientRects[index - 1];\n  const nextRect: ClientRect | undefined = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect\n      ? currentRect.top - (previousRect.top + previousRect.height)\n      : nextRect\n      ? nextRect.top - (currentRect.top + currentRect.height)\n      : 0;\n  }\n\n  return nextRect\n    ? nextRect.top - (currentRect.top + currentRect.height)\n    : previousRect\n    ? currentRect.top - (previousRect.top + previousRect.height)\n    : 0;\n}\n", "import React, {useEffect, useMemo, useRef} from 'react';\nimport {useDndContext, ClientRect, UniqueIdentifier} from '@dnd-kit/core';\nimport {useIsomorphicLayoutEffect, useUniqueId} from '@dnd-kit/utilities';\n\nimport type {Disabled, SortingStrategy} from '../types';\nimport {getSortedRects, itemsEqual, normalizeDisabled} from '../utilities';\nimport {rectSortingStrategy} from '../strategies';\n\nexport interface Props {\n  children: React.ReactNode;\n  items: (UniqueIdentifier | {id: UniqueIdentifier})[];\n  strategy?: SortingStrategy;\n  id?: string;\n  disabled?: boolean | Disabled;\n}\n\nconst ID_PREFIX = 'Sortable';\n\ninterface ContextDescriptor {\n  activeIndex: number;\n  containerId: string;\n  disabled: Disabled;\n  disableTransforms: boolean;\n  items: UniqueIdentifier[];\n  overIndex: number;\n  useDragOverlay: boolean;\n  sortedRects: ClientRect[];\n  strategy: SortingStrategy;\n}\n\nexport const Context = React.createContext<ContextDescriptor>({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false,\n  },\n});\n\nexport function SortableContext({\n  children,\n  id,\n  items: userDefinedItems,\n  strategy = rectSortingStrategy,\n  disabled: disabledProp = false,\n}: Props) {\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo<UniqueIdentifier[]>(\n    () =>\n      userDefinedItems.map((item) =>\n        typeof item === 'object' && 'id' in item ? item.id : item\n      ),\n    [userDefinedItems]\n  );\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms =\n    (overIndex !== -1 && activeIndex === -1) || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n\n  const contextValue = useMemo(\n    (): ContextDescriptor => ({\n      activeIndex,\n      containerId,\n      disabled,\n      disableTransforms,\n      items,\n      overIndex,\n      useDragOverlay,\n      sortedRects: getSortedRects(items, droppableRects),\n      strategy,\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      activeIndex,\n      containerId,\n      disabled.draggable,\n      disabled.droppable,\n      disableTransforms,\n      items,\n      overIndex,\n      droppableRects,\n      useDragOverlay,\n      strategy,\n    ]\n  );\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>;\n}\n", "import {CSS} from '@dnd-kit/utilities';\n\nimport {arrayMove} from '../utilities';\n\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\n\nexport const defaultNewIndexGetter: NewIndexGetter = ({\n  id,\n  items,\n  activeIndex,\n  overIndex,\n}) => arrayMove(items, activeIndex, overIndex).indexOf(id);\n\nexport const defaultAnimateLayoutChanges: AnimateLayoutChanges = ({\n  containerId,\n  isSorting,\n  wasDragging,\n  index,\n  items,\n  newIndex,\n  previousItems,\n  previousContainerId,\n  transition,\n}) => {\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\n\nexport const defaultTransition: SortableTransition = {\n  duration: 200,\n  easing: 'ease',\n};\n\nexport const transitionProperty = 'transform';\n\nexport const disabledTransition = CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear',\n});\n\nexport const defaultAttributes = {\n  roleDescription: 'sortable',\n};\n", "import {useEffect, useRef, useState} from 'react';\nimport {getClientRect, ClientRect} from '@dnd-kit/core';\nimport {Transform, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  rect: React.MutableRefObject<ClientRect | null>;\n  disabled: boolean;\n  index: number;\n  node: React.MutableRefObject<HTMLElement | null>;\n}\n\n/*\n * When the index of an item changes while sorting,\n * we need to temporarily disable the transforms\n */\nexport function useDerivedTransform({disabled, index, node, rect}: Arguments) {\n  const [derivedTransform, setDerivedtransform] = useState<Transform | null>(\n    null\n  );\n  const previousIndex = useRef(index);\n\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true,\n        });\n\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height,\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n\n  return derivedTransform;\n}\n", "import {useContext, useEffect, useMemo, useRef} from 'react';\nimport {\n  useDraggable,\n  useDroppable,\n  UseDraggableArguments,\n  UseDroppableArguments,\n} from '@dnd-kit/core';\nimport type {Data} from '@dnd-kit/core';\nimport {CSS, isKeyboardEvent, useCombinedRefs} from '@dnd-kit/utilities';\n\nimport {Context} from '../components';\nimport type {Disabled, SortableData, SortingStrategy} from '../types';\nimport {isValidIndex} from '../utilities';\nimport {\n  defaultAnimateLayoutChanges,\n  defaultAttributes,\n  defaultNewIndexGetter,\n  defaultTransition,\n  disabledTransition,\n  transitionProperty,\n} from './defaults';\nimport type {\n  AnimateLayoutChanges,\n  NewIndexGetter,\n  SortableTransition,\n} from './types';\nimport {useDerivedTransform} from './utilities';\n\nexport interface Arguments\n  extends Omit<UseDraggableArguments, 'disabled'>,\n    Pick<UseDroppableArguments, 'resizeObserverConfig'> {\n  animateLayoutChanges?: AnimateLayoutChanges;\n  disabled?: boolean | Disabled;\n  getNewIndex?: NewIndexGetter;\n  strategy?: SortingStrategy;\n  transition?: SortableTransition | null;\n}\n\nexport function useSortable({\n  animateLayoutChanges = defaultAnimateLayoutChanges,\n  attributes: userDefinedAttributes,\n  disabled: localDisabled,\n  data: customData,\n  getNewIndex = defaultNewIndexGetter,\n  id,\n  strategy: localStrategy,\n  resizeObserverConfig,\n  transition = defaultTransition,\n}: Arguments) {\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy,\n  } = useContext(Context);\n  const disabled: Disabled = normalizeLocalDisabled(\n    localDisabled,\n    globalDisabled\n  );\n  const index = items.indexOf(id);\n  const data = useMemo<SortableData & Data>(\n    () => ({sortable: {containerId, index, items}, ...customData}),\n    [containerId, customData, index, items]\n  );\n  const itemsAfterCurrentSortable = useMemo(\n    () => items.slice(items.indexOf(id)),\n    [items, id]\n  );\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef,\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig,\n    },\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform,\n  } = useDraggable({\n    id,\n    data,\n    attributes: {\n      ...defaultAttributes,\n      ...userDefinedAttributes,\n    },\n    disabled: disabled.draggable,\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem =\n    isSorting &&\n    !disableTransforms &&\n    isValidIndex(activeIndex) &&\n    isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement =\n    shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy ?? globalStrategy;\n  const finalTransform = displaceItem\n    ? dragSourceDisplacement ??\n      strategy({\n        rects: sortedRects,\n        activeNodeRect,\n        activeIndex,\n        overIndex,\n        index,\n      })\n    : null;\n  const newIndex =\n    isValidIndex(activeIndex) && isValidIndex(overIndex)\n      ? getNewIndex({id, items, activeIndex, overIndex})\n      : index;\n  const activeId = active?.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId,\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null,\n  });\n\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect,\n  });\n\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform ?? finalTransform,\n    transition: getTransition(),\n  };\n\n  function getTransition() {\n    if (\n      // Temporarily disable transitions for a single frame to set up derived transforms\n      derivedTransform ||\n      // Or to prevent items jumping to back to their \"new\" position when items change\n      (itemsHaveChanged && previous.current.newIndex === index)\n    ) {\n      return disabledTransition;\n    }\n\n    if (\n      (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent)) ||\n      !transition\n    ) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({\n        ...transition,\n        property: transitionProperty,\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(\n  localDisabled: Arguments['disabled'],\n  globalDisabled: Disabled\n) {\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false,\n    };\n  }\n\n  return {\n    draggable: localDisabled?.draggable ?? globalDisabled.draggable,\n    droppable: localDisabled?.droppable ?? globalDisabled.droppable,\n  };\n}\n", "import type {\n  Active,\n  Data,\n  DroppableContainer,\n  DraggableNode,\n  Over,\n} from '@dnd-kit/core';\n\nimport type {SortableData} from './data';\n\nexport function hasSortableData<\n  T extends Active | Over | DraggableNode | DroppableContainer\n>(\n  entry: T | null | undefined\n): entry is T & {data: {current: Data<SortableData>}} {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (\n    data &&\n    'sortable' in data &&\n    typeof data.sortable === 'object' &&\n    'containerId' in data.sortable &&\n    'items' in data.sortable &&\n    'index' in data.sortable\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import {\n  closestCorners,\n  getScrollableAncestors,\n  getFirstCollision,\n  KeyboardCode,\n  DroppableContainer,\n  KeyboardCoordinateGetter,\n} from '@dnd-kit/core';\nimport {subtract} from '@dnd-kit/utilities';\n\nimport {hasSortableData} from '../../types';\n\nconst directions: string[] = [\n  KeyboardCode.Down,\n  KeyboardCode.Right,\n  KeyboardCode.Up,\n  KeyboardCode.Left,\n];\n\nexport const sortableKeyboardCoordinates: KeyboardCoordinateGetter = (\n  event,\n  {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n    },\n  }\n) => {\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers: DroppableContainer[] = [];\n\n    droppableContainers.getEnabled().forEach((entry) => {\n      if (!entry || entry?.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n          break;\n      }\n    });\n\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null,\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === over?.id && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable?.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some(\n          (element, index) => scrollableAncestors[index] !== element\n        );\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset =\n          hasDifferentScrollAncestors || !hasSameContainer\n            ? {\n                x: 0,\n                y: 0,\n              }\n            : {\n                x: isAfterActive ? collisionRect.width - newRect.width : 0,\n                y: isAfterActive ? collisionRect.height - newRect.height : 0,\n              };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top,\n        };\n\n        const newCoordinates =\n          offset.x && offset.y\n            ? rectCoordinates\n            : subtract(rectCoordinates, offset);\n\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return (\n    a.data.current.sortable.containerId === b.data.current.sortable.containerId\n  );\n}\n\nfunction isAfter(a: DroppableContainer, b: DroppableContainer) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "// This icon file is generated automatically.\nvar CloseOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z\" } }] }, \"name\": \"close\", \"theme\": \"outlined\" };\nexport default CloseOutlined;\n", "function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };", "function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nexport { _iterableToArrayLimit as default };", "function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };", "import arrayWithHoles from \"./arrayWithHoles.js\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableRest from \"./nonIterableRest.js\";\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nexport { _slicedToArray as default };", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "function e(t) {\n  return t && t.__esModule && Object.prototype.hasOwnProperty.call(t, \"default\") ? t.default : t;\n}\nexport {\n  e as getDefaultExportFromCjs\n};\n//# sourceMappingURL=_commonjsHelpers.es.js.map\n", "var s = { exports: {} };\nexport {\n  s as __module\n};\n//# sourceMappingURL=index.es.js.map\n", "/*!\n  Copyright (c) 2018 <PERSON>.\n  Licensed under the MIT License (MIT), see\n  http://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames() {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg)) {\n\t\t\t\tif (arg.length) {\n\t\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\t\tif (inner) {\n\t\t\t\t\t\tclasses.push(inner);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tif (arg.toString === Object.prototype.toString) {\n\t\t\t\t\tfor (var key in arg) {\n\t\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tclasses.push(arg.toString());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "// Generated by script. Do NOT modify!\n\nexport var presetPrimaryColors = {\n  \"red\": \"#F5222D\",\n  \"volcano\": \"#FA541C\",\n  \"orange\": \"#FA8C16\",\n  \"gold\": \"#FAAD14\",\n  \"yellow\": \"#FADB14\",\n  \"lime\": \"#A0D911\",\n  \"green\": \"#52C41A\",\n  \"cyan\": \"#13C2C2\",\n  \"blue\": \"#1677FF\",\n  \"geekblue\": \"#2F54EB\",\n  \"purple\": \"#722ED1\",\n  \"magenta\": \"#EB2F96\",\n  \"grey\": \"#666666\"\n};\nexport var red = [\"#fff1f0\", \"#ffccc7\", \"#ffa39e\", \"#ff7875\", \"#ff4d4f\", \"#f5222d\", \"#cf1322\", \"#a8071a\", \"#820014\", \"#5c0011\"];\nred.primary = red[5];\nexport var volcano = [\"#fff2e8\", \"#ffd8bf\", \"#ffbb96\", \"#ff9c6e\", \"#ff7a45\", \"#fa541c\", \"#d4380d\", \"#ad2102\", \"#871400\", \"#610b00\"];\nvolcano.primary = volcano[5];\nexport var orange = [\"#fff7e6\", \"#ffe7ba\", \"#ffd591\", \"#ffc069\", \"#ffa940\", \"#fa8c16\", \"#d46b08\", \"#ad4e00\", \"#873800\", \"#612500\"];\norange.primary = orange[5];\nexport var gold = [\"#fffbe6\", \"#fff1b8\", \"#ffe58f\", \"#ffd666\", \"#ffc53d\", \"#faad14\", \"#d48806\", \"#ad6800\", \"#874d00\", \"#613400\"];\ngold.primary = gold[5];\nexport var yellow = [\"#feffe6\", \"#ffffb8\", \"#fffb8f\", \"#fff566\", \"#ffec3d\", \"#fadb14\", \"#d4b106\", \"#ad8b00\", \"#876800\", \"#614700\"];\nyellow.primary = yellow[5];\nexport var lime = [\"#fcffe6\", \"#f4ffb8\", \"#eaff8f\", \"#d3f261\", \"#bae637\", \"#a0d911\", \"#7cb305\", \"#5b8c00\", \"#3f6600\", \"#254000\"];\nlime.primary = lime[5];\nexport var green = [\"#f6ffed\", \"#d9f7be\", \"#b7eb8f\", \"#95de64\", \"#73d13d\", \"#52c41a\", \"#389e0d\", \"#237804\", \"#135200\", \"#092b00\"];\ngreen.primary = green[5];\nexport var cyan = [\"#e6fffb\", \"#b5f5ec\", \"#87e8de\", \"#5cdbd3\", \"#36cfc9\", \"#13c2c2\", \"#08979c\", \"#006d75\", \"#00474f\", \"#002329\"];\ncyan.primary = cyan[5];\nexport var blue = [\"#e6f4ff\", \"#bae0ff\", \"#91caff\", \"#69b1ff\", \"#4096ff\", \"#1677ff\", \"#0958d9\", \"#003eb3\", \"#002c8c\", \"#001d66\"];\nblue.primary = blue[5];\nexport var geekblue = [\"#f0f5ff\", \"#d6e4ff\", \"#adc6ff\", \"#85a5ff\", \"#597ef7\", \"#2f54eb\", \"#1d39c4\", \"#10239e\", \"#061178\", \"#030852\"];\ngeekblue.primary = geekblue[5];\nexport var purple = [\"#f9f0ff\", \"#efdbff\", \"#d3adf7\", \"#b37feb\", \"#9254de\", \"#722ed1\", \"#531dab\", \"#391085\", \"#22075e\", \"#120338\"];\npurple.primary = purple[5];\nexport var magenta = [\"#fff0f6\", \"#ffd6e7\", \"#ffadd2\", \"#ff85c0\", \"#f759ab\", \"#eb2f96\", \"#c41d7f\", \"#9e1068\", \"#780650\", \"#520339\"];\nmagenta.primary = magenta[5];\nexport var grey = [\"#a6a6a6\", \"#999999\", \"#8c8c8c\", \"#808080\", \"#737373\", \"#666666\", \"#404040\", \"#1a1a1a\", \"#000000\", \"#000000\"];\ngrey.primary = grey[5];\nexport var gray = grey;\nexport var presetPalettes = {\n  red: red,\n  volcano: volcano,\n  orange: orange,\n  gold: gold,\n  yellow: yellow,\n  lime: lime,\n  green: green,\n  cyan: cyan,\n  blue: blue,\n  geekblue: geekblue,\n  purple: purple,\n  magenta: magenta,\n  grey: grey\n};\nexport var redDark = [\"#2a1215\", \"#431418\", \"#58181c\", \"#791a1f\", \"#a61d24\", \"#d32029\", \"#e84749\", \"#f37370\", \"#f89f9a\", \"#fac8c3\"];\nredDark.primary = redDark[5];\nexport var volcanoDark = [\"#2b1611\", \"#441d12\", \"#592716\", \"#7c3118\", \"#aa3e19\", \"#d84a1b\", \"#e87040\", \"#f3956a\", \"#f8b692\", \"#fad4bc\"];\nvolcanoDark.primary = volcanoDark[5];\nexport var orangeDark = [\"#2b1d11\", \"#442a11\", \"#593815\", \"#7c4a15\", \"#aa6215\", \"#d87a16\", \"#e89a3c\", \"#f3b765\", \"#f8cf8d\", \"#fae3b7\"];\norangeDark.primary = orangeDark[5];\nexport var goldDark = [\"#2b2111\", \"#443111\", \"#594214\", \"#7c5914\", \"#aa7714\", \"#d89614\", \"#e8b339\", \"#f3cc62\", \"#f8df8b\", \"#faedb5\"];\ngoldDark.primary = goldDark[5];\nexport var yellowDark = [\"#2b2611\", \"#443b11\", \"#595014\", \"#7c6e14\", \"#aa9514\", \"#d8bd14\", \"#e8d639\", \"#f3ea62\", \"#f8f48b\", \"#fafab5\"];\nyellowDark.primary = yellowDark[5];\nexport var limeDark = [\"#1f2611\", \"#2e3c10\", \"#3e4f13\", \"#536d13\", \"#6f9412\", \"#8bbb11\", \"#a9d134\", \"#c9e75d\", \"#e4f88b\", \"#f0fab5\"];\nlimeDark.primary = limeDark[5];\nexport var greenDark = [\"#162312\", \"#1d3712\", \"#274916\", \"#306317\", \"#3c8618\", \"#49aa19\", \"#6abe39\", \"#8fd460\", \"#b2e58b\", \"#d5f2bb\"];\ngreenDark.primary = greenDark[5];\nexport var cyanDark = [\"#112123\", \"#113536\", \"#144848\", \"#146262\", \"#138585\", \"#13a8a8\", \"#33bcb7\", \"#58d1c9\", \"#84e2d8\", \"#b2f1e8\"];\ncyanDark.primary = cyanDark[5];\nexport var blueDark = [\"#111a2c\", \"#112545\", \"#15325b\", \"#15417e\", \"#1554ad\", \"#1668dc\", \"#3c89e8\", \"#65a9f3\", \"#8dc5f8\", \"#b7dcfa\"];\nblueDark.primary = blueDark[5];\nexport var geekblueDark = [\"#131629\", \"#161d40\", \"#1c2755\", \"#203175\", \"#263ea0\", \"#2b4acb\", \"#5273e0\", \"#7f9ef3\", \"#a8c1f8\", \"#d2e0fa\"];\ngeekblueDark.primary = geekblueDark[5];\nexport var purpleDark = [\"#1a1325\", \"#24163a\", \"#301c4d\", \"#3e2069\", \"#51258f\", \"#642ab5\", \"#854eca\", \"#ab7ae0\", \"#cda8f0\", \"#ebd7fa\"];\npurpleDark.primary = purpleDark[5];\nexport var magentaDark = [\"#291321\", \"#40162f\", \"#551c3b\", \"#75204f\", \"#a02669\", \"#cb2b83\", \"#e0529c\", \"#f37fb7\", \"#f8a8cc\", \"#fad2e3\"];\nmagentaDark.primary = magentaDark[5];\nexport var greyDark = [\"#151515\", \"#1f1f1f\", \"#2d2d2d\", \"#393939\", \"#494949\", \"#5a5a5a\", \"#6a6a6a\", \"#7b7b7b\", \"#888888\", \"#969696\"];\ngreyDark.primary = greyDark[5];\nexport var presetDarkPalettes = {\n  red: redDark,\n  volcano: volcanoDark,\n  orange: orangeDark,\n  gold: goldDark,\n  yellow: yellowDark,\n  lime: limeDark,\n  green: greenDark,\n  cyan: cyanDark,\n  blue: blueDark,\n  geekblue: geekblueDark,\n  purple: purpleDark,\n  magenta: magentaDark,\n  grey: greyDark\n};", "import { createContext } from 'react';\nvar IconContext = /*#__PURE__*/createContext({});\nexport default IconContext;", "import defineProperty from \"./defineProperty.js\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nexport { _objectSpread2 as default };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nconst round = Math.round;\n\n/**\n * Support format, alpha unit will check the % mark:\n * - rgba(102, 204, 255, .5)      -> [102, 204, 255, 0.5]\n * - rgb(102 204 255 / .5)        -> [102, 204, 255, 0.5]\n * - rgb(100%, 50%, 0% / 50%)     -> [255, 128, 0, 0.5]\n * - hsl(270, 60, 40, .5)         -> [270, 60, 40, 0.5]\n * - hsl(270deg 60% 40% / 50%)   -> [270, 60, 40, 0.5]\n *\n * When `base` is provided, the percentage value will be divided by `base`.\n */\nfunction splitColorStr(str, parseNum) {\n  const match = str\n  // Remove str before `(`\n  .replace(/^[^(]*\\((.*)/, '$1')\n  // Remove str after `)`\n  .replace(/\\).*/, '').match(/\\d*\\.?\\d+%?/g) || [];\n  const numList = match.map(item => parseFloat(item));\n  for (let i = 0; i < 3; i += 1) {\n    numList[i] = parseNum(numList[i] || 0, match[i] || '', i);\n  }\n\n  // For alpha. 50% should be 0.5\n  if (match[3]) {\n    numList[3] = match[3].includes('%') ? numList[3] / 100 : numList[3];\n  } else {\n    // By default, alpha is 1\n    numList[3] = 1;\n  }\n  return numList;\n}\nconst parseHSVorHSL = (num, _, index) => index === 0 ? num : num / 100;\n\n/** round and limit number to integer between 0-255 */\nfunction limitRange(value, max) {\n  const mergedMax = max || 255;\n  if (value > mergedMax) {\n    return mergedMax;\n  }\n  if (value < 0) {\n    return 0;\n  }\n  return value;\n}\nexport class FastColor {\n  constructor(input) {\n    /**\n     * All FastColor objects are valid. So isValid is always true. This property is kept to be compatible with TinyColor.\n     */\n    _defineProperty(this, \"isValid\", true);\n    /**\n     * Red, R in RGB\n     */\n    _defineProperty(this, \"r\", 0);\n    /**\n     * Green, G in RGB\n     */\n    _defineProperty(this, \"g\", 0);\n    /**\n     * Blue, B in RGB\n     */\n    _defineProperty(this, \"b\", 0);\n    /**\n     * Alpha/Opacity, A in RGBA/HSLA\n     */\n    _defineProperty(this, \"a\", 1);\n    // HSV privates\n    _defineProperty(this, \"_h\", void 0);\n    _defineProperty(this, \"_s\", void 0);\n    _defineProperty(this, \"_l\", void 0);\n    _defineProperty(this, \"_v\", void 0);\n    // intermediate variables to calculate HSL/HSV\n    _defineProperty(this, \"_max\", void 0);\n    _defineProperty(this, \"_min\", void 0);\n    _defineProperty(this, \"_brightness\", void 0);\n    /**\n     * Always check 3 char in the object to determine the format.\n     * We not use function in check to save bundle size.\n     * e.g. 'rgb' -> { r: 0, g: 0, b: 0 }.\n     */\n    function matchFormat(str) {\n      return str[0] in input && str[1] in input && str[2] in input;\n    }\n    if (!input) {\n      // Do nothing since already initialized\n    } else if (typeof input === 'string') {\n      const trimStr = input.trim();\n      function matchPrefix(prefix) {\n        return trimStr.startsWith(prefix);\n      }\n      if (/^#?[A-F\\d]{3,8}$/i.test(trimStr)) {\n        this.fromHexString(trimStr);\n      } else if (matchPrefix('rgb')) {\n        this.fromRgbString(trimStr);\n      } else if (matchPrefix('hsl')) {\n        this.fromHslString(trimStr);\n      } else if (matchPrefix('hsv') || matchPrefix('hsb')) {\n        this.fromHsvString(trimStr);\n      }\n    } else if (input instanceof FastColor) {\n      this.r = input.r;\n      this.g = input.g;\n      this.b = input.b;\n      this.a = input.a;\n      this._h = input._h;\n      this._s = input._s;\n      this._l = input._l;\n      this._v = input._v;\n    } else if (matchFormat('rgb')) {\n      this.r = limitRange(input.r);\n      this.g = limitRange(input.g);\n      this.b = limitRange(input.b);\n      this.a = typeof input.a === 'number' ? limitRange(input.a, 1) : 1;\n    } else if (matchFormat('hsl')) {\n      this.fromHsl(input);\n    } else if (matchFormat('hsv')) {\n      this.fromHsv(input);\n    } else {\n      throw new Error('@ant-design/fast-color: unsupported input ' + JSON.stringify(input));\n    }\n  }\n\n  // ======================= Setter =======================\n\n  setR(value) {\n    return this._sc('r', value);\n  }\n  setG(value) {\n    return this._sc('g', value);\n  }\n  setB(value) {\n    return this._sc('b', value);\n  }\n  setA(value) {\n    return this._sc('a', value, 1);\n  }\n  setHue(value) {\n    const hsv = this.toHsv();\n    hsv.h = value;\n    return this._c(hsv);\n  }\n\n  // ======================= Getter =======================\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   * @see http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n   */\n  getLuminance() {\n    function adjustGamma(raw) {\n      const val = raw / 255;\n      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);\n    }\n    const R = adjustGamma(this.r);\n    const G = adjustGamma(this.g);\n    const B = adjustGamma(this.b);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  }\n  getHue() {\n    if (typeof this._h === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._h = 0;\n      } else {\n        this._h = round(60 * (this.r === this.getMax() ? (this.g - this.b) / delta + (this.g < this.b ? 6 : 0) : this.g === this.getMax() ? (this.b - this.r) / delta + 2 : (this.r - this.g) / delta + 4));\n      }\n    }\n    return this._h;\n  }\n  getSaturation() {\n    if (typeof this._s === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._s = 0;\n      } else {\n        this._s = delta / this.getMax();\n      }\n    }\n    return this._s;\n  }\n  getLightness() {\n    if (typeof this._l === 'undefined') {\n      this._l = (this.getMax() + this.getMin()) / 510;\n    }\n    return this._l;\n  }\n  getValue() {\n    if (typeof this._v === 'undefined') {\n      this._v = this.getMax() / 255;\n    }\n    return this._v;\n  }\n\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   * Note: this is not the b of HSB\n   * @see http://www.w3.org/TR/AERT#color-contrast\n   */\n  getBrightness() {\n    if (typeof this._brightness === 'undefined') {\n      this._brightness = (this.r * 299 + this.g * 587 + this.b * 114) / 1000;\n    }\n    return this._brightness;\n  }\n\n  // ======================== Func ========================\n\n  darken(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() - amount / 100;\n    if (l < 0) {\n      l = 0;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n  lighten(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() + amount / 100;\n    if (l > 1) {\n      l = 1;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  mix(input, amount = 50) {\n    const color = this._c(input);\n    const p = amount / 100;\n    const calc = key => (color[key] - this[key]) * p + this[key];\n    const rgba = {\n      r: round(calc('r')),\n      g: round(calc('g')),\n      b: round(calc('b')),\n      a: round(calc('a') * 100) / 100\n    };\n    return this._c(rgba);\n  }\n\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   */\n  tint(amount = 10) {\n    return this.mix({\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 1\n    }, amount);\n  }\n\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   */\n  shade(amount = 10) {\n    return this.mix({\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    }, amount);\n  }\n  onBackground(background) {\n    const bg = this._c(background);\n    const alpha = this.a + bg.a * (1 - this.a);\n    const calc = key => {\n      return round((this[key] * this.a + bg[key] * bg.a * (1 - this.a)) / alpha);\n    };\n    return this._c({\n      r: calc('r'),\n      g: calc('g'),\n      b: calc('b'),\n      a: alpha\n    });\n  }\n\n  // ======================= Status =======================\n  isDark() {\n    return this.getBrightness() < 128;\n  }\n  isLight() {\n    return this.getBrightness() >= 128;\n  }\n\n  // ======================== MISC ========================\n  equals(other) {\n    return this.r === other.r && this.g === other.g && this.b === other.b && this.a === other.a;\n  }\n  clone() {\n    return this._c(this);\n  }\n\n  // ======================= Format =======================\n  toHexString() {\n    let hex = '#';\n    const rHex = (this.r || 0).toString(16);\n    hex += rHex.length === 2 ? rHex : '0' + rHex;\n    const gHex = (this.g || 0).toString(16);\n    hex += gHex.length === 2 ? gHex : '0' + gHex;\n    const bHex = (this.b || 0).toString(16);\n    hex += bHex.length === 2 ? bHex : '0' + bHex;\n    if (typeof this.a === 'number' && this.a >= 0 && this.a < 1) {\n      const aHex = round(this.a * 255).toString(16);\n      hex += aHex.length === 2 ? aHex : '0' + aHex;\n    }\n    return hex;\n  }\n\n  /** CSS support color pattern */\n  toHsl() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      l: this.getLightness(),\n      a: this.a\n    };\n  }\n\n  /** CSS support color pattern */\n  toHslString() {\n    const h = this.getHue();\n    const s = round(this.getSaturation() * 100);\n    const l = round(this.getLightness() * 100);\n    return this.a !== 1 ? `hsla(${h},${s}%,${l}%,${this.a})` : `hsl(${h},${s}%,${l}%)`;\n  }\n\n  /** Same as toHsb */\n  toHsv() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      v: this.getValue(),\n      a: this.a\n    };\n  }\n  toRgb() {\n    return {\n      r: this.r,\n      g: this.g,\n      b: this.b,\n      a: this.a\n    };\n  }\n  toRgbString() {\n    return this.a !== 1 ? `rgba(${this.r},${this.g},${this.b},${this.a})` : `rgb(${this.r},${this.g},${this.b})`;\n  }\n  toString() {\n    return this.toRgbString();\n  }\n\n  // ====================== Privates ======================\n  /** Return a new FastColor object with one channel changed */\n  _sc(rgb, value, max) {\n    const clone = this.clone();\n    clone[rgb] = limitRange(value, max);\n    return clone;\n  }\n  _c(input) {\n    return new this.constructor(input);\n  }\n  getMax() {\n    if (typeof this._max === 'undefined') {\n      this._max = Math.max(this.r, this.g, this.b);\n    }\n    return this._max;\n  }\n  getMin() {\n    if (typeof this._min === 'undefined') {\n      this._min = Math.min(this.r, this.g, this.b);\n    }\n    return this._min;\n  }\n  fromHexString(trimStr) {\n    const withoutPrefix = trimStr.replace('#', '');\n    function connectNum(index1, index2) {\n      return parseInt(withoutPrefix[index1] + withoutPrefix[index2 || index1], 16);\n    }\n    if (withoutPrefix.length < 6) {\n      // #rgb or #rgba\n      this.r = connectNum(0);\n      this.g = connectNum(1);\n      this.b = connectNum(2);\n      this.a = withoutPrefix[3] ? connectNum(3) / 255 : 1;\n    } else {\n      // #rrggbb or #rrggbbaa\n      this.r = connectNum(0, 1);\n      this.g = connectNum(2, 3);\n      this.b = connectNum(4, 5);\n      this.a = withoutPrefix[6] ? connectNum(6, 7) / 255 : 1;\n    }\n  }\n  fromHsl({\n    h,\n    s,\n    l,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._l = l;\n    this.a = typeof a === 'number' ? a : 1;\n    if (s <= 0) {\n      const rgb = round(l * 255);\n      this.r = rgb;\n      this.g = rgb;\n      this.b = rgb;\n    }\n    let r = 0,\n      g = 0,\n      b = 0;\n    const huePrime = h / 60;\n    const chroma = (1 - Math.abs(2 * l - 1)) * s;\n    const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n    if (huePrime >= 0 && huePrime < 1) {\n      r = chroma;\n      g = secondComponent;\n    } else if (huePrime >= 1 && huePrime < 2) {\n      r = secondComponent;\n      g = chroma;\n    } else if (huePrime >= 2 && huePrime < 3) {\n      g = chroma;\n      b = secondComponent;\n    } else if (huePrime >= 3 && huePrime < 4) {\n      g = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 4 && huePrime < 5) {\n      r = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 5 && huePrime < 6) {\n      r = chroma;\n      b = secondComponent;\n    }\n    const lightnessModification = l - chroma / 2;\n    this.r = round((r + lightnessModification) * 255);\n    this.g = round((g + lightnessModification) * 255);\n    this.b = round((b + lightnessModification) * 255);\n  }\n  fromHsv({\n    h,\n    s,\n    v,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._v = v;\n    this.a = typeof a === 'number' ? a : 1;\n    const vv = round(v * 255);\n    this.r = vv;\n    this.g = vv;\n    this.b = vv;\n    if (s <= 0) {\n      return;\n    }\n    const hh = h / 60;\n    const i = Math.floor(hh);\n    const ff = hh - i;\n    const p = round(v * (1.0 - s) * 255);\n    const q = round(v * (1.0 - s * ff) * 255);\n    const t = round(v * (1.0 - s * (1.0 - ff)) * 255);\n    switch (i) {\n      case 0:\n        this.g = t;\n        this.b = p;\n        break;\n      case 1:\n        this.r = q;\n        this.b = p;\n        break;\n      case 2:\n        this.r = p;\n        this.b = t;\n        break;\n      case 3:\n        this.r = p;\n        this.g = q;\n        break;\n      case 4:\n        this.r = t;\n        this.g = p;\n        break;\n      case 5:\n      default:\n        this.g = p;\n        this.b = q;\n        break;\n    }\n  }\n  fromHsvString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsv({\n      h: cells[0],\n      s: cells[1],\n      v: cells[2],\n      a: cells[3]\n    });\n  }\n  fromHslString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsl({\n      h: cells[0],\n      s: cells[1],\n      l: cells[2],\n      a: cells[3]\n    });\n  }\n  fromRgbString(trimStr) {\n    const cells = splitColorStr(trimStr, (num, txt) =>\n    // Convert percentage to number. e.g. 50% -> 128\n    txt.includes('%') ? round(num / 100 * 255) : num);\n    this.r = cells[0];\n    this.g = cells[1];\n    this.b = cells[2];\n    this.a = cells[3];\n  }\n}", "import { FastColor } from '@ant-design/fast-color';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  amount: 15\n}, {\n  index: 6,\n  amount: 25\n}, {\n  index: 5,\n  amount: 30\n}, {\n  index: 5,\n  amount: 45\n}, {\n  index: 5,\n  amount: 65\n}, {\n  index: 5,\n  amount: 85\n}, {\n  index: 4,\n  amount: 90\n}, {\n  index: 3,\n  amount: 95\n}, {\n  index: 2,\n  amount: 97\n}, {\n  index: 1,\n  amount: 98\n}];\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Math.round(saturation * 100) / 100;\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  // Clamp value between 0 and 1\n  value = Math.max(0, Math.min(1, value));\n  return Math.round(value * 100) / 100;\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = new FastColor(color);\n  var hsv = pColor.toHsv();\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var c = new FastColor({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    });\n    patterns.push(c);\n  }\n  patterns.push(pColor);\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _c = new FastColor({\n      h: getHue(hsv, _i),\n      s: getSaturation(hsv, _i),\n      v: getValue(hsv, _i)\n    });\n    patterns.push(_c);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref) {\n      var index = _ref.index,\n        amount = _ref.amount;\n      return new FastColor(opts.backgroundColor || '#141414').mix(patterns[index], amount).toHexString();\n    });\n  }\n  return patterns.map(function (c) {\n    return c.toHexString();\n  });\n}", "export default function canUseDom() {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}", "export default function contains(root, n) {\n  if (!root) {\n    return false;\n  }\n\n  // Use native if support\n  if (root.contains) {\n    return root.contains(n);\n  }\n\n  // `document.contains` not support with IE11\n  var node = n;\n  while (node) {\n    if (node === root) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport canUseDom from \"./canUseDom\";\nimport contains from \"./contains\";\nvar APPEND_ORDER = 'data-rc-order';\nvar APPEND_PRIORITY = 'data-rc-priority';\nvar MARK_KEY = \"rc-util-key\";\nvar containerCache = new Map();\nfunction getMark() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    mark = _ref.mark;\n  if (mark) {\n    return mark.startsWith('data-') ? mark : \"data-\".concat(mark);\n  }\n  return MARK_KEY;\n}\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n  var head = document.querySelector('head');\n  return head || document.body;\n}\nfunction getOrder(prepend) {\n  if (prepend === 'queue') {\n    return 'prependQueue';\n  }\n  return prepend ? 'prepend' : 'append';\n}\n\n/**\n * Find style which inject by rc-util\n */\nfunction findStyles(container) {\n  return Array.from((containerCache.get(container) || container).children).filter(function (node) {\n    return node.tagName === 'STYLE';\n  });\n}\nexport function injectCSS(css) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!canUseDom()) {\n    return null;\n  }\n  var csp = option.csp,\n    prepend = option.prepend,\n    _option$priority = option.priority,\n    priority = _option$priority === void 0 ? 0 : _option$priority;\n  var mergedOrder = getOrder(prepend);\n  var isPrependQueue = mergedOrder === 'prependQueue';\n  var styleNode = document.createElement('style');\n  styleNode.setAttribute(APPEND_ORDER, mergedOrder);\n  if (isPrependQueue && priority) {\n    styleNode.setAttribute(APPEND_PRIORITY, \"\".concat(priority));\n  }\n  if (csp !== null && csp !== void 0 && csp.nonce) {\n    styleNode.nonce = csp === null || csp === void 0 ? void 0 : csp.nonce;\n  }\n  styleNode.innerHTML = css;\n  var container = getContainer(option);\n  var firstChild = container.firstChild;\n  if (prepend) {\n    // If is queue `prepend`, it will prepend first style and then append rest style\n    if (isPrependQueue) {\n      var existStyle = (option.styles || findStyles(container)).filter(function (node) {\n        // Ignore style which not injected by rc-util with prepend\n        if (!['prepend', 'prependQueue'].includes(node.getAttribute(APPEND_ORDER))) {\n          return false;\n        }\n\n        // Ignore style which priority less then new style\n        var nodePriority = Number(node.getAttribute(APPEND_PRIORITY) || 0);\n        return priority >= nodePriority;\n      });\n      if (existStyle.length) {\n        container.insertBefore(styleNode, existStyle[existStyle.length - 1].nextSibling);\n        return styleNode;\n      }\n    }\n\n    // Use `insertBefore` as `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n  return styleNode;\n}\nfunction findExistNode(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var container = getContainer(option);\n  return (option.styles || findStyles(container)).find(function (node) {\n    return node.getAttribute(getMark(option)) === key;\n  });\n}\nexport function removeCSS(key) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var container = getContainer(option);\n    container.removeChild(existNode);\n  }\n}\n\n/**\n * qiankun will inject `appendChild` to insert into other\n */\nfunction syncRealContainer(container, option) {\n  var cachedRealContainer = containerCache.get(container);\n\n  // Find real container when not cached or cached container removed\n  if (!cachedRealContainer || !contains(document, cachedRealContainer)) {\n    var placeholderStyle = injectCSS('', option);\n    var parentNode = placeholderStyle.parentNode;\n    containerCache.set(container, parentNode);\n    container.removeChild(placeholderStyle);\n  }\n}\n\n/**\n * manually clear container cache to avoid global cache in unit testes\n */\nexport function clearContainerCache() {\n  containerCache.clear();\n}\nexport function updateCSS(css, key) {\n  var originOption = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var container = getContainer(originOption);\n  var styles = findStyles(container);\n  var option = _objectSpread(_objectSpread({}, originOption), {}, {\n    styles: styles\n  });\n\n  // Sync real parent\n  syncRealContainer(container, option);\n  var existNode = findExistNode(key, option);\n  if (existNode) {\n    var _option$csp, _option$csp2;\n    if ((_option$csp = option.csp) !== null && _option$csp !== void 0 && _option$csp.nonce && existNode.nonce !== ((_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce)) {\n      var _option$csp3;\n      existNode.nonce = (_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce;\n    }\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n    return existNode;\n  }\n  var newNode = injectCSS(css, option);\n  newNode.setAttribute(getMark(option), key);\n  return newNode;\n}", "function getRoot(ele) {\n  var _ele$getRootNode;\n  return ele === null || ele === void 0 || (_ele$getRootNode = ele.getRootNode) === null || _ele$getRootNode === void 0 ? void 0 : _ele$getRootNode.call(ele);\n}\n\n/**\n * Check if is in shadowRoot\n */\nexport function inShadow(ele) {\n  return getRoot(ele) instanceof ShadowRoot;\n}\n\n/**\n * Return shadowRoot if possible\n */\nexport function getShadowRoot(ele) {\n  return inShadow(ele) ? getRoot(ele) : null;\n}", "/* eslint-disable no-console */\nvar warned = {};\nvar preWarningFns = [];\n\n/**\n * Pre warning enable you to parse content before console.error.\n * Modify to null will prevent warning.\n */\nexport var preMessage = function preMessage(fn) {\n  preWarningFns.push(fn);\n};\n\n/**\n * Warning if condition not match.\n * @param valid Condition\n * @param message Warning message\n * @example\n * ```js\n * warning(false, 'some error'); // print some error\n * warning(true, 'some error'); // print nothing\n * warning(1 === 2, 'some error'); // print some error\n * ```\n */\nexport function warning(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'warning');\n    }, message);\n    if (finalMessage) {\n      console.error(\"Warning: \".concat(finalMessage));\n    }\n  }\n}\n\n/** @see Similar to {@link warning} */\nexport function note(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'note');\n    }, message);\n    if (finalMessage) {\n      console.warn(\"Note: \".concat(finalMessage));\n    }\n  }\n}\nexport function resetWarned() {\n  warned = {};\n}\nexport function call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nexport function warningOnce(valid, message) {\n  call(warning, valid, message);\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nexport function noteOnce(valid, message) {\n  call(note, valid, message);\n}\nwarningOnce.preMessage = preMessage;\nwarningOnce.resetWarned = resetWarned;\nwarningOnce.noteOnce = noteOnce;\nexport default warningOnce;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport warn from \"rc-util/es/warning\";\nimport React, { useContext, useEffect } from 'react';\nimport IconContext from \"./components/Context\";\nfunction camelCase(input) {\n  return input.replace(/-(.)/g, function (match, g) {\n    return g.toUpperCase();\n  });\n}\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        delete acc[key];\n        acc[camelCase(key)] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-flex;\\n  align-items: center;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles(eleRef) {\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp,\n    prefixCls = _useContext.prefixCls,\n    layer = _useContext.layer;\n  var mergedStyleStr = iconStyles;\n  if (prefixCls) {\n    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);\n  }\n  if (layer) {\n    mergedStyleStr = \"@layer \".concat(layer, \" {\\n\").concat(mergedStyleStr, \"\\n}\");\n  }\n  useEffect(function () {\n    var ele = eleRef.current;\n    var shadowRoot = getShadowRoot(ele);\n    updateCSS(mergedStyleStr, '@ant-design-icons', {\n      prepend: !layer,\n      csp: csp,\n      attachTo: shadowRoot\n    });\n  }, []);\n};", "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"icon\", \"className\", \"onClick\", \"style\", \"primaryColor\", \"secondaryColor\"];\nimport * as React from 'react';\nimport { generate, getSecondaryColor, isIconDefinition, warning, useInsertStyles } from \"../utils\";\nvar twoToneColorPalette = {\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n};\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n    secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\nfunction getTwoToneColors() {\n  return _objectSpread({}, twoToneColorPalette);\n}\nvar IconBase = function IconBase(props) {\n  var icon = props.icon,\n    className = props.className,\n    onClick = props.onClick,\n    style = props.style,\n    primaryColor = props.primaryColor,\n    secondaryColor = props.secondaryColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var svgRef = React.useRef();\n  var colors = twoToneColorPalette;\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)\n    };\n  }\n  useInsertStyles(svgRef);\n  warning(isIconDefinition(icon), \"icon should be icon definiton, but got \".concat(icon));\n  if (!isIconDefinition(icon)) {\n    return null;\n  }\n  var target = icon;\n  if (target && typeof target.icon === 'function') {\n    target = _objectSpread(_objectSpread({}, target), {}, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n  return generate(target.icon, \"svg-\".concat(target.name), _objectSpread(_objectSpread({\n    className: className,\n    onClick: onClick,\n    style: style,\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  }, restProps), {}, {\n    ref: svgRef\n  }));\n};\nIconBase.displayName = 'IconReact';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nexport default IconBase;", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport ReactIcon from \"./IconBase\";\nimport { normalizeTwoToneColors } from \"../utils\";\nexport function setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return ReactIcon.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\nexport function getTwoToneColor() {\n  var colors = ReactIcon.getTwoToneColors();\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n  return [colors.primaryColor, colors.secondaryColor];\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"icon\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"twoToneColor\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { blue } from '@ant-design/colors';\nimport Context from \"./Context\";\nimport ReactIcon from \"./IconBase\";\nimport { getTwoToneColor, setTwoToneColor } from \"./twoTonePrimaryColor\";\nimport { normalizeTwoToneColors } from \"../utils\";\n// Initial setting\n// should move it to antd main repo?\nsetTwoToneColor(blue.primary);\n\n// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34757#issuecomment-488848720\n\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    icon = props.icon,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    twoToneColor = props.twoToneColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-\").concat(icon.name), !!icon.name), \"\".concat(prefixCls, \"-spin\"), !!spin || icon.name === 'loading'), className);\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"img\",\n    \"aria-label\": icon.name\n  }, restProps, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), /*#__PURE__*/React.createElement(ReactIcon, {\n    icon: icon,\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor,\n    style: svgStyle\n  }));\n});\nIcon.displayName = 'AntdIcon';\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloseOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloseOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CloseOutlined = function CloseOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CloseOutlinedSvg\n  }));\n};\n\n/**![close](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CloseOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloseOutlined';\n}\nexport default RefIcon;", "import React, { useState, useEffect, useCallback } from 'react';\r\nimport { Select } from 'antd';\r\nimport { DndContext, DragEndEvent } from '@dnd-kit/core';\r\nimport {\r\n  horizontalListSortingStrategy,\r\n  rectSortingStrategy,\r\n  SortableContext,\r\n  useSortable,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { CloseOutlined } from '@ant-design/icons';\r\n\r\nconst { Option } = Select;\r\n\r\ntype OptionType = {\r\n  value: string;\r\n  label: string;\r\n};\r\n\r\ntype SortingStrategy = 'rect' | 'vertical' | 'horizontal';\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nconst sortingStrategies: Record<SortingStrategy, any> = {\r\n  rect: rectSortingStrategy,\r\n  vertical: verticalListSortingStrategy,\r\n  horizontal: horizontalListSortingStrategy,\r\n};\r\n\r\ntype DraggableSelectProps = {\r\n  defaultValue?: string[];\r\n  value?: string[];\r\n  options?: OptionType[];\r\n  onChange?: (selectedItems: string[], items: OptionType[]) => void;\r\n  style?: React.CSSProperties;\r\n  showSearch?: boolean;\r\n  caseSensitiveSearch?: boolean;\r\n  disabled?: boolean;\r\n  sortingStrategy?: SortingStrategy;\r\n  allowClear?: boolean;\r\n};\r\n\r\nconst DraggableSelect: React.FC<DraggableSelectProps> = ({\r\n  defaultValue,\r\n  value,\r\n  options = [],\r\n  onChange,\r\n  style = { width: '100%' },\r\n  showSearch = false,\r\n  caseSensitiveSearch = false,\r\n  disabled = false,\r\n  sortingStrategy = 'rect',\r\n  allowClear = false,\r\n}) => {\r\n  const [selectedItems, setSelectedItems] = useState<string[]>(\r\n    value || defaultValue || []\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (value !== undefined) {\r\n      setSelectedItems(value);\r\n    }\r\n  }, [value]);\r\n\r\n  const handleDragEnd = useCallback(\r\n    (event: DragEndEvent) => {\r\n      const { active, over } = event;\r\n      if (!over || active.id === over.id) return;\r\n\r\n      const newSelectedItems = selectedItems.slice();\r\n      const oldIndex = newSelectedItems.indexOf(active.id as string);\r\n      const newIndex = newSelectedItems.indexOf(over.id as string);\r\n\r\n      if (oldIndex !== -1 && newIndex !== -1) {\r\n        const reorderedSelected = [...newSelectedItems];\r\n        reorderedSelected.splice(oldIndex, 1);\r\n        reorderedSelected.splice(newIndex, 0, active.id as string);\r\n\r\n        setSelectedItems(reorderedSelected);\r\n        onChange?.(reorderedSelected, options);\r\n      }\r\n    },\r\n    [selectedItems, onChange, options]\r\n  );\r\n\r\n  const handleSelectionChange = (newSelected: string[]) => {\r\n    setSelectedItems(newSelected);\r\n    onChange?.(newSelected, options);\r\n  };\r\n\r\n  const handleFilterOption = (input: string, option?: { children: string }) => {\r\n    if (!showSearch || !option) return false;\r\n\r\n    const search = caseSensitiveSearch ? input : input.toLowerCase();\r\n    const label = caseSensitiveSearch\r\n      ? option.children\r\n      : option.children.toLowerCase();\r\n\r\n    return label.includes(search);\r\n  };\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  const TagItem = ({ label, value, closable, onClose }: any) => {\r\n    const {\r\n      attributes,\r\n      listeners,\r\n      setNodeRef,\r\n      transform,\r\n      transition,\r\n      isDragging,\r\n    } = useSortable({\r\n      id: value,\r\n      disabled,\r\n    });\r\n\r\n    const tagStyle: React.CSSProperties = {\r\n      opacity: isDragging ? 0.5 : 1,\r\n      transform: transform\r\n        ? `translate3d(${transform.x}px, ${transform.y}px, 0)`\r\n        : undefined,\r\n      transition,\r\n      cursor: disabled ? 'not-allowed' : 'move',\r\n      marginRight: 3,\r\n      display: 'inline-block',\r\n    };\r\n\r\n    return (\r\n      <div\r\n        ref={setNodeRef}\r\n        style={tagStyle}\r\n        onMouseDown={(e) => e.stopPropagation()}\r\n        title={label}\r\n      >\r\n        <span className=\"ant-select-selection-item\">\r\n          <span\r\n            className=\"ant-select-selection-item-content\"\r\n            {...attributes}\r\n            {...listeners}\r\n          >\r\n            {label}\r\n          </span>\r\n          {closable && (\r\n            <span\r\n              className=\"ant-select-selection-item-remove\"\r\n              onClick={onClose}\r\n              data-testid=\"remove-item\"\r\n            >\r\n              <CloseOutlined />\r\n            </span>\r\n          )}\r\n        </span>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <DndContext onDragEnd={handleDragEnd}>\r\n      <SortableContext\r\n        items={selectedItems}\r\n        strategy={sortingStrategies[sortingStrategy]}\r\n      >\r\n        <Select\r\n          mode=\"multiple\"\r\n          style={style}\r\n          value={selectedItems}\r\n          onChange={handleSelectionChange}\r\n          showSearch={showSearch}\r\n          disabled={disabled}\r\n          filterOption={handleFilterOption}\r\n          optionFilterProp=\"children\"\r\n          tagRender={(props) => <TagItem {...props} />}\r\n          allowClear={allowClear}\r\n        >\r\n          {options.map((item) => (\r\n            <Option key={item.value} value={item.value}>\r\n              {item.label}\r\n            </Option>\r\n          ))}\r\n        </Select>\r\n      </SortableContext>\r\n    </DndContext>\r\n  );\r\n};\r\n\r\nexport default DraggableSelect;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAEgBA,kBAAAA;oCACXC,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,SAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAEH,aAAOC;IACL,MAAOC,UAAD;AACJF,WAAKG,QAASC,SAAQA,IAAIF,IAAD,CAAzB;;;IAGFF;EALY;AAOf;ACXD,IAAaK,YACX,OAAOC,WAAW,eAClB,OAAOA,OAAOC,aAAa,eAC3B,OAAOD,OAAOC,SAASC,kBAAkB;SCJ3BC,SAASC,SAAAA;AACvB,QAAMC,gBAAgBC,OAAOC,UAAUC,SAASC,KAAKL,OAA/B;AACtB,SACEC,kBAAkB;EAElBA,kBAAkB;AAErB;SCPeK,OAAOd,MAAAA;AACrB,SAAO,cAAcA;AACtB;SCCee,UAAUC,QAAAA;;AACxB,MAAI,CAACA,QAAQ;AACX,WAAOZ;;AAGT,MAAIG,SAASS,MAAD,GAAU;AACpB,WAAOA;;AAGT,MAAI,CAACF,OAAOE,MAAD,GAAU;AACnB,WAAOZ;;AAGT,UAAA,yBAAA,yBAAOY,OAAOC,kBAAd,OAAA,SAAO,uBAAsBC,gBAA7B,OAAA,wBAA4Cd;AAC7C;SCfee,WAAWnB,MAAAA;AACzB,QAAM;IAACoB;MAAYL,UAAUf,IAAD;AAE5B,SAAOA,gBAAgBoB;AACxB;SCFeC,cAAcrB,MAAAA;AAC5B,MAAIO,SAASP,IAAD,GAAQ;AAClB,WAAO;;AAGT,SAAOA,gBAAgBe,UAAUf,IAAD,EAAOsB;AACxC;SCReC,aAAavB,MAAAA;AAC3B,SAAOA,gBAAgBe,UAAUf,IAAD,EAAOwB;AACxC;SCIeC,iBAAiBT,QAAAA;AAC/B,MAAI,CAACA,QAAQ;AACX,WAAOX;;AAGT,MAAIE,SAASS,MAAD,GAAU;AACpB,WAAOA,OAAOX;;AAGhB,MAAI,CAACS,OAAOE,MAAD,GAAU;AACnB,WAAOX;;AAGT,MAAIc,WAAWH,MAAD,GAAU;AACtB,WAAOA;;AAGT,MAAIK,cAAcL,MAAD,KAAYO,aAAaP,MAAD,GAAU;AACjD,WAAOA,OAAOC;;AAGhB,SAAOZ;AACR;ACtBD,IAAaqB,4BAA4BvB,YACrCwB,+BACAC;SCNYC,SAA6BC,SAAAA;AAC3C,QAAMC,iBAAaC,qBAAsBF,OAAhB;AAEzBJ,4BAA0B,MAAA;AACxBK,eAAWE,UAAUH;GADE;AAIzB,aAAOI,0BAAY,WAAA;sCAAaC,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,WAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAC9B,WAAOJ,WAAWE,WAAlB,OAAA,SAAOF,WAAWE,QAAU,GAAGE,IAAxB;KACN,CAAA,CAFe;AAGnB;SCZeC,cAAAA;AACd,QAAMC,kBAAcL,qBAAsB,IAAhB;AAE1B,QAAMM,UAAMJ,0BAAY,CAACK,UAAoBC,aAArB;AACtBH,gBAAYJ,UAAUQ,YAAYF,UAAUC,QAAX;KAChC,CAAA,CAFoB;AAIvB,QAAME,YAAQR,0BAAY,MAAA;AACxB,QAAIG,YAAYJ,YAAY,MAAM;AAChCU,oBAAcN,YAAYJ,OAAb;AACbI,kBAAYJ,UAAU;;KAEvB,CAAA,CALsB;AAOzB,SAAO,CAACK,KAAKI,KAAN;AACR;SCZeE,eACdC,OACAC,cAAAA;MAAAA,iBAAAA,QAAAA;AAAAA,mBAA+B,CAACD,KAAD;;AAE/B,QAAME,eAAWf,qBAAUa,KAAJ;AAEvBnB,4BAA0B,MAAA;AACxB,QAAIqB,SAASd,YAAYY,OAAO;AAC9BE,eAASd,UAAUY;;KAEpBC,YAJsB;AAMzB,SAAOC;AACR;SChBeC,YACdC,UACAH,cAAAA;AAEA,QAAMC,eAAWf,qBAAM;AAEvB,aAAOjC;IACL,MAAA;AACE,YAAMmD,WAAWD,SAASF,SAASd,OAAV;AACzBc,eAASd,UAAUiB;AAEnB,aAAOA;;;IAGT,CAAC,GAAGJ,YAAJ;EARY;AAUf;SCdeK,WACdC,UAAAA;AAKA,QAAMC,kBAAkBxB,SAASuB,QAAD;AAChC,QAAMpD,WAAOgC,qBAA2B,IAArB;AACnB,QAAMsB,iBAAapB;IAChB1B,aAAD;AACE,UAAIA,YAAYR,KAAKiC,SAAS;AAC5BoB,2BAAe,OAAf,SAAAA,gBAAkB7C,SAASR,KAAKiC,OAAjB;;AAGjBjC,WAAKiC,UAAUzB;;;IAGjB,CAAA;EAT4B;AAY9B,SAAO,CAACR,MAAMsD,UAAP;AACR;SCvBeC,YAAeV,OAAAA;AAC7B,QAAM3C,UAAM8B,qBAAM;AAElBJ,8BAAU,MAAA;AACR1B,QAAI+B,UAAUY;KACb,CAACA,KAAD,CAFM;AAIT,SAAO3C,IAAI+B;AACZ;ACRD,IAAIuB,MAA8B,CAAA;AAElC,SAAgBC,YAAYC,QAAgBb,OAAAA;AAC1C,aAAO9C,sBAAQ,MAAA;AACb,QAAI8C,OAAO;AACT,aAAOA;;AAGT,UAAMc,KAAKH,IAAIE,MAAD,KAAY,OAAO,IAAIF,IAAIE,MAAD,IAAW;AACnDF,QAAIE,MAAD,IAAWC;AAEd,WAAUD,SAAV,MAAoBC;KACnB,CAACD,QAAQb,KAAT,CATW;AAUf;ACfD,SAASe,mBAAmBC,UAA5B;AACE,SAAO,SACLC,QADK;sCAEFC,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,kBAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAEH,WAAOA,YAAYC,OACjB,CAACC,aAAaC,eAAd;AACE,YAAMC,UAAUzD,OAAOyD,QAAQD,UAAf;AAEhB,iBAAW,CAACE,MAAKC,eAAN,KAA0BF,SAAS;AAC5C,cAAMtB,QAAQoB,YAAYG,IAAD;AAEzB,YAAIvB,SAAS,MAAM;AACjBoB,sBAAYG,IAAD,IAASvB,QAAQgB,WAAWQ;;;AAI3C,aAAOJ;OAET;MACE,GAAGH;KAfA;;AAmBV;AAED,IAAaQ,MAAMV,mBAAmB,CAAD;AACrC,IAAaW,WAAWX,mBAAmB,EAAD;SC3B1BY,+BACdC,OAAAA;AAEA,SAAO,aAAaA,SAAS,aAAaA;AAC3C;SCFeC,gBACdD,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAM;IAACE;MAAiB5D,UAAU0D,MAAMzD,MAAP;AAEjC,SAAO2D,iBAAiBF,iBAAiBE;AAC1C;SCVeC,aACdH,OAAAA;AAEA,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,QAAM;IAACI;MAAc9D,UAAU0D,MAAMzD,MAAP;AAE9B,SAAO6D,cAAcJ,iBAAiBI;AACvC;ACND,SAAgBC,oBAAoBL,OAAAA;AAClC,MAAIG,aAAaH,KAAD,GAAS;AACvB,QAAIA,MAAMM,WAAWN,MAAMM,QAAQC,QAAQ;AACzC,YAAM;QAACC,SAASC;QAAGC,SAASC;UAAKX,MAAMM,QAAQ,CAAd;AAEjC,aAAO;QACLG,GAAAA;QACAE,GAAAA;;eAEOX,MAAMY,kBAAkBZ,MAAMY,eAAeL,QAAQ;AAC9D,YAAM;QAACC,SAASC;QAAGC,SAASC;UAAKX,MAAMY,eAAe,CAArB;AAEjC,aAAO;QACLH,GAAAA;QACAE,GAAAA;;;;AAKN,MAAIZ,+BAA+BC,KAAD,GAAS;AACzC,WAAO;MACLS,GAAGT,MAAMQ;MACTG,GAAGX,MAAMU;;;AAIb,SAAO;AACR;ICpBYG,MAAM5E,OAAO6E,OAAO;EAC/BC,WAAW;IACT5E,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,YAAM;QAACP,GAAAA;QAAGE,GAAAA;UAAKK;AAEf,aAAA,kBAAsBP,KAAIQ,KAAKC,MAAMT,EAAX,IAAgB,KAA1C,UACEE,KAAIM,KAAKC,MAAMP,EAAX,IAAgB,KADtB;;;EAKJQ,OAAO;IACLhF,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,YAAM;QAACI;QAAQC;UAAUL;AAEzB,aAAA,YAAiBI,SAAjB,cAAmCC,SAAnC;;;EAGJC,WAAW;IACTnF,SAAS6E,WAAD;AACN,UAAI,CAACA,WAAW;AACd;;AAGF,aAAO,CACLH,IAAIE,UAAU5E,SAAS6E,SAAvB,GACAH,IAAIM,MAAMhF,SAAS6E,SAAnB,CAFK,EAGLO,KAAK,GAHA;;;EAMXC,YAAY;IACVrF,SAAQ,MAAA;UAAC;QAACsF;QAAU1D;QAAU2D;;AAC5B,aAAUD,WAAV,MAAsB1D,WAAtB,QAAoC2D;;;AAvCT,CAAd;ACbnB,IAAMC,WACJ;AAEF,SAAgBC,uBACd7F,SAAAA;AAEA,MAAIA,QAAQ8F,QAAQF,QAAhB,GAA2B;AAC7B,WAAO5F;;AAGT,SAAOA,QAAQ+F,cAAcH,QAAtB;AACR;;;;ACJD,IAAMI,eAAoC;EACxCC,SAAS;AAD+B;SAI1BC,WAAAA,MAAAA;MAAW;IAACC;IAAIC;;AAC9B,SACEC,cAAAA,QAAAA,cAAA,OAAA;IAAKF;IAAQG,OAAON;KACjBI,KADH;AAIH;SCTeG,WAAAA,MAAAA;MAAW;IAACJ;IAAIK;IAAcC,eAAe;;AAE3D,QAAMC,iBAAsC;IAC1CC,UAAU;IACVC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVC,MAAM;IACNC,UAAU;IACVC,YAAY;;AAGd,SACEjB,cAAAA,QAAAA,cAAA,OAAA;IACEF;IACAG,OAAOI;IACPa,MAAK;iBACMd;;KAGVD,YAPH;AAUH;SClCegB,kBAAAA;AACd,QAAM,CAAChB,cAAciB,eAAf,QAAkCC,wBAAS,EAAD;AAChD,QAAMC,eAAWC,2BAAaxB,WAAD;AAC3B,QAAIA,SAAS,MAAM;AACjBqB,sBAAgBrB,KAAD;;KAEhB,CAAA,CAJyB;AAM5B,SAAO;IAACuB;IAAUnB;;AACnB;;;ACPM,IAAMqB,wBAAoBC,6BAAuC,IAA1B;SCC9BC,cAAcC,UAAAA;AAC5B,QAAMC,uBAAmBC,0BAAWL,iBAAD;AAEnCM,+BAAU,MAAA;AACR,QAAI,CAACF,kBAAkB;AACrB,YAAM,IAAIG,MACR,8DADI;;AAKR,UAAMC,cAAcJ,iBAAiBD,QAAD;AAEpC,WAAOK;KACN,CAACL,UAAUC,gBAAX,CAVM;AAWV;SCfeK,wBAAAA;AACd,QAAM,CAACC,SAAD,QAAcC,wBAAS,MAAM,oBAAIC,IAAJ,CAAP;AAE5B,QAAMR,uBAAmBS,2BACtBV,cAAD;AACEO,cAAUI,IAAIX,QAAd;AACA,WAAO,MAAMO,UAAUK,OAAOZ,QAAjB;KAEf,CAACO,SAAD,CALkC;AAQpC,QAAMM,eAAWH,2BACf,UAAA;QAAC;MAACI;MAAMC;;AACNR,cAAUS,QAAShB,cAAD;AAAA,UAAA;AAAA,cAAA,iBAAcA,SAASc,IAAD,MAAtB,OAAA,SAAc,eAAA,KAAAd,UAAiBe,KAAT;KAAxC;KAEF,CAACR,SAAD,CAJ0B;AAO5B,SAAO,CAACM,UAAUZ,gBAAX;AACR;ICrBYgB,kCAA4D;EACvEC,WAAS;AAD8D;AAQzE,IAAaC,uBAAsC;EACjDC,YAAW,MAAA;QAAC;MAACC;;AACX,WAAA,8BAAmCA,OAAOC,KAA1C;;EAEFC,WAAU,OAAA;QAAC;MAACF;MAAQG;;AAClB,QAAIA,MAAM;AACR,aAAA,oBAAyBH,OAAOC,KAAhC,oCAAoEE,KAAKF,KAAzE;;AAGF,WAAA,oBAAyBD,OAAOC,KAAhC;;EAEFG,UAAS,OAAA;QAAC;MAACJ;MAAQG;;AACjB,QAAIA,MAAM;AACR,aAAA,oBAAyBH,OAAOC,KAAhC,sCAAsEE,KAAKF;;AAG7E,WAAA,oBAAyBD,OAAOC,KAAhC;;EAEFI,aAAY,OAAA;QAAC;MAACL;;AACZ,WAAA,4CAAiDA,OAAOC,KAAxD;;AAnB+C;SCUnCK,cAAAA,MAAAA;MAAc;IAC5BC,gBAAgBT;IAChBU;IACAC;IACAC,2BAA2Bd;;AAE3B,QAAM;IAACe;IAAUC;MAAgBC,gBAAe;AAChD,QAAMC,eAAeC,YAAW,eAAA;AAChC,QAAM,CAACC,SAASC,UAAV,QAAwB9B,wBAAS,KAAD;AAEtCL,+BAAU,MAAA;AACRmC,eAAW,IAAD;KACT,CAAA,CAFM;AAITvC,oBACEwC,uBACE,OAAO;IACLnB,YAAW,OAAA;UAAC;QAACC;;AACXW,eAASJ,cAAcR,YAAY;QAACC;OAA3B,CAAD;;IAEVmB,WAAU,OAAA;UAAC;QAACnB;QAAQG;;AAClB,UAAII,cAAcY,YAAY;AAC5BR,iBAASJ,cAAcY,WAAW;UAACnB;UAAQG;SAAlC,CAAD;;;IAGZD,WAAU,OAAA;UAAC;QAACF;QAAQG;;AAClBQ,eAASJ,cAAcL,WAAW;QAACF;QAAQG;OAAlC,CAAD;;IAEVC,UAAS,OAAA;UAAC;QAACJ;QAAQG;;AACjBQ,eAASJ,cAAcH,UAAU;QAACJ;QAAQG;OAAjC,CAAD;;IAEVE,aAAY,OAAA;UAAC;QAACL;QAAQG;;AACpBQ,eAASJ,cAAcF,aAAa;QAACL;QAAQG;OAApC,CAAD;;MAGZ,CAACQ,UAAUJ,aAAX,CApBK,CADI;AAyBb,MAAI,CAACS,SAAS;AACZ,WAAO;;AAGT,QAAMI,SACJC,cAAAA,QAAAA,cAAA,cAAAA,QAAA,UAAA,MACEA,cAAAA,QAAAA,cAACC,YAAD;IACErB,IAAIQ;IACJc,OAAOb,yBAAyBb;GAFlC,GAIAwB,cAAAA,QAAAA,cAACG,YAAD;IAAYvB,IAAIa;IAAcF;GAA9B,CALF;AASF,SAAOJ,gBAAYiB,+BAAaL,QAAQZ,SAAT,IAAsBY;AACtD;ACvED,IAAYM;CAAZ,SAAYA,SAAAA;AACVA,EAAAA,QAAAA,WAAAA,IAAA;AACAA,EAAAA,QAAAA,UAAAA,IAAA;AACAA,EAAAA,QAAAA,SAAAA,IAAA;AACAA,EAAAA,QAAAA,YAAAA,IAAA;AACAA,EAAAA,QAAAA,UAAAA,IAAA;AACAA,EAAAA,QAAAA,mBAAAA,IAAA;AACAA,EAAAA,QAAAA,sBAAAA,IAAA;AACAA,EAAAA,QAAAA,qBAAAA,IAAA;AACD,GATWA,WAAAA,SAAM,CAAA,EAAlB;SCHgBC,OAAAA;AAAAA;IGEHC,qBAAkCC,OAAOC,OAAO;EAC3DC,GAAG;EACHC,GAAG;AAFwD,CAAd;SEC/BC,2BACdC,OACAC,MAAAA;AAEA,QAAMC,mBAAmBC,oBAAoBH,KAAD;AAE5C,MAAI,CAACE,kBAAkB;AACrB,WAAO;;AAGT,QAAME,kBAAkB;IACtBC,IAAKH,iBAAiBG,IAAIJ,KAAKK,QAAQL,KAAKM,QAAS;IACrDC,IAAKN,iBAAiBM,IAAIP,KAAKQ,OAAOR,KAAKS,SAAU;;AAGvD,SAAUN,gBAAgBC,IAA1B,OAAgCD,gBAAgBI,IAAhD;AACD;ACDD,SAAgBG,mBAAAA,OAAAA,OAAAA;MACd;IAACC,MAAM;MAACC,OAAOC;;;MACf;IAACF,MAAM;MAACC,OAAOE;;;AAEf,SAAOA,KAAID;AACZ;AAsCD,SAAgBE,kBACdC,YACAC,UAAAA;AAEA,MAAI,CAACD,cAAcA,WAAWE,WAAW,GAAG;AAC1C,WAAO;;AAGT,QAAM,CAACC,cAAD,IAAmBH;AAEzB,SAAOC,WAAWE,eAAeF,QAAD,IAAaE;AAC9C;AGhED,SAAgBC,qBACdC,OACAC,QAAAA;AAEA,QAAMC,MAAMC,KAAKC,IAAIH,OAAOC,KAAKF,MAAME,GAA3B;AACZ,QAAMG,OAAOF,KAAKC,IAAIH,OAAOI,MAAML,MAAMK,IAA5B;AACb,QAAMC,QAAQH,KAAKI,IAAIN,OAAOI,OAAOJ,OAAOO,OAAOR,MAAMK,OAAOL,MAAMQ,KAAxD;AACd,QAAMC,SAASN,KAAKI,IAAIN,OAAOC,MAAMD,OAAOS,QAAQV,MAAME,MAAMF,MAAMU,MAAvD;AACf,QAAMF,QAAQF,QAAQD;AACtB,QAAMK,SAASD,SAASP;AAExB,MAAIG,OAAOC,SAASJ,MAAMO,QAAQ;AAChC,UAAME,aAAaV,OAAOO,QAAQP,OAAOS;AACzC,UAAME,YAAYZ,MAAMQ,QAAQR,MAAMU;AACtC,UAAMG,mBAAmBL,QAAQE;AACjC,UAAMI,oBACJD,oBAAoBF,aAAaC,YAAYC;AAE/C,WAAOE,OAAOD,kBAAkBE,QAAQ,CAA1B,CAAD;;AAIf,SAAO;AACR;AAMD,IAAaC,mBAAuC,UAAA;MAAC;IACnDC;IACAC;IACAC;;AAEA,QAAMC,aAAoC,CAAA;AAE1C,aAAWC,sBAAsBF,qBAAqB;AACpD,UAAM;MAACG;QAAMD;AACb,UAAME,OAAOL,eAAeM,IAAIF,EAAnB;AAEb,QAAIC,MAAM;AACR,YAAMV,oBAAoBf,qBAAqByB,MAAMN,aAAP;AAE9C,UAAIJ,oBAAoB,GAAG;AACzBO,mBAAWK,KAAK;UACdH;UACAI,MAAM;YAACL;YAAoBM,OAAOd;;SAFpC;;;;AAQN,SAAOO,WAAWQ,KAAKC,kBAAhB;AACR;SE1DeC,YACdC,WACAC,OACAC,OAAAA;AAEA,SAAO;IACL,GAAGF;IACHG,QAAQF,SAASC,QAAQD,MAAMG,QAAQF,MAAME,QAAQ;IACrDC,QAAQJ,SAASC,QAAQD,MAAMK,SAASJ,MAAMI,SAAS;;AAE1D;SCVeC,aACdN,OACAC,OAAAA;AAEA,SAAOD,SAASC,QACZ;IACEM,GAAGP,MAAMQ,OAAOP,MAAMO;IACtBC,GAAGT,MAAMU,MAAMT,MAAMS;MAEvBC;AACL;SCXeC,uBAAuBC,UAAAA;AACrC,SAAO,SAASC,iBACdC,MADK;sCAEFC,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,kBAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAEH,WAAOA,YAAYC,OACjB,CAACC,KAAKC,gBAAgB;MACpB,GAAGD;MACHR,KAAKQ,IAAIR,MAAMG,WAAWM,WAAWV;MACrCW,QAAQF,IAAIE,SAASP,WAAWM,WAAWV;MAC3CD,MAAMU,IAAIV,OAAOK,WAAWM,WAAWZ;MACvCc,OAAOH,IAAIG,QAAQR,WAAWM,WAAWZ;QAE3C;MAAC,GAAGQ;KARC;;AAWV;AAEM,IAAMO,kBAAkBV,uBAAuB,CAAD;SClBrCW,eAAexB,WAAAA;AAC7B,MAAIA,UAAUyB,WAAW,WAArB,GAAmC;AACrC,UAAMC,iBAAiB1B,UAAU2B,MAAM,GAAG,EAAnB,EAAuBC,MAAM,IAA7B;AAEvB,WAAO;MACLpB,GAAG,CAACkB,eAAe,EAAD;MAClBhB,GAAG,CAACgB,eAAe,EAAD;MAClBvB,QAAQ,CAACuB,eAAe,CAAD;MACvBrB,QAAQ,CAACqB,eAAe,CAAD;;aAEhB1B,UAAUyB,WAAW,SAArB,GAAiC;AAC1C,UAAMC,iBAAiB1B,UAAU2B,MAAM,GAAG,EAAnB,EAAuBC,MAAM,IAA7B;AAEvB,WAAO;MACLpB,GAAG,CAACkB,eAAe,CAAD;MAClBhB,GAAG,CAACgB,eAAe,CAAD;MAClBvB,QAAQ,CAACuB,eAAe,CAAD;MACvBrB,QAAQ,CAACqB,eAAe,CAAD;;;AAI3B,SAAO;AACR;SCpBeG,iBACdb,MACAhB,WACA8B,iBAAAA;AAEA,QAAMC,kBAAkBP,eAAexB,SAAD;AAEtC,MAAI,CAAC+B,iBAAiB;AACpB,WAAOf;;AAGT,QAAM;IAACb;IAAQE;IAAQG,GAAGwB;IAAYtB,GAAGuB;MAAcF;AAEvD,QAAMvB,KAAIQ,KAAKP,OAAOuB,cAAc,IAAI7B,UAAU+B,WAAWJ,eAAD;AAC5D,QAAMpB,KACJM,KAAKL,MACLsB,cACC,IAAI5B,UACH6B,WAAWJ,gBAAgBH,MAAMG,gBAAgBK,QAAQ,GAAxB,IAA+B,CAArD,CAAD;AACd,QAAMC,KAAIjC,SAASa,KAAKZ,QAAQD,SAASa,KAAKZ;AAC9C,QAAMiC,KAAIhC,SAASW,KAAKV,SAASD,SAASW,KAAKV;AAE/C,SAAO;IACLF,OAAOgC;IACP9B,QAAQ+B;IACR1B,KAAKD;IACLY,OAAOd,KAAI4B;IACXf,QAAQX,KAAI2B;IACZ5B,MAAMD;;AAET;ACzBD,IAAM8B,iBAA0B;EAACC,iBAAiB;AAAlB;AAKhC,SAAgBC,cACdC,SACAC,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAAmBJ;;AAEnB,MAAItB,OAAmByB,QAAQE,sBAAR;AAEvB,MAAID,QAAQH,iBAAiB;AAC3B,UAAM;MAACvC;MAAW8B;QAChBc,UAAUH,OAAD,EAAUI,iBAAiBJ,OAApC;AAEF,QAAIzC,WAAW;AACbgB,aAAOa,iBAAiBb,MAAMhB,WAAW8B,eAAlB;;;AAI3B,QAAM;IAACnB;IAAKF;IAAML;IAAOE;IAAQe;IAAQC;MAASN;AAElD,SAAO;IACLL;IACAF;IACAL;IACAE;IACAe;IACAC;;AAEH;AAUD,SAAgBwB,+BAA+BL,SAAAA;AAC7C,SAAOD,cAAcC,SAAS;IAACF,iBAAiB;GAA5B;AACrB;SCjDeQ,oBAAoBN,SAAAA;AAClC,QAAMrC,QAAQqC,QAAQO;AACtB,QAAM1C,SAASmC,QAAQQ;AAEvB,SAAO;IACLtC,KAAK;IACLF,MAAM;IACNa,OAAOlB;IACPiB,QAAQf;IACRF;IACAE;;AAEH;SCZe4C,QACdC,MACAC,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAAqCR,UAAUO,IAAD,EAAON,iBAAiBM,IAAjC;;AAErC,SAAOC,cAAcC,aAAa;AACnC;SCLeC,aACdb,SACAW,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAAqCR,UAAUH,OAAD,EAAUI,iBACtDJ,OADmC;;AAIrC,QAAMc,gBAAgB;AACtB,QAAMC,cAAa,CAAC,YAAY,aAAa,WAA1B;AAEnB,SAAOA,YAAWC,KAAMC,cAAD;AACrB,UAAMC,QAAQP,cAAcM,QAAD;AAE3B,WAAO,OAAOC,UAAU,WAAWJ,cAAcK,KAAKD,KAAnB,IAA4B;GAH1D;AAKR;SCNeE,uBACdpB,SACAqB,OAAAA;AAEA,QAAMC,gBAA2B,CAAA;AAEjC,WAASC,wBAAwBb,MAAjC;AACE,QAAIW,SAAS,QAAQC,cAAcE,UAAUH,OAAO;AAClD,aAAOC;;AAGT,QAAI,CAACZ,MAAM;AACT,aAAOY;;AAGT,QACEG,WAAWf,IAAD,KACVA,KAAKgB,oBAAoB,QACzB,CAACJ,cAAcK,SAASjB,KAAKgB,gBAA5B,GACD;AACAJ,oBAAcM,KAAKlB,KAAKgB,gBAAxB;AAEA,aAAOJ;;AAGT,QAAI,CAACO,cAAcnB,IAAD,KAAUoB,aAAapB,IAAD,GAAQ;AAC9C,aAAOY;;AAGT,QAAIA,cAAcK,SAASjB,IAAvB,GAA8B;AAChC,aAAOY;;AAGT,UAAMX,gBAAgBR,UAAUH,OAAD,EAAUI,iBAAiBM,IAApC;AAEtB,QAAIA,SAASV,SAAS;AACpB,UAAIa,aAAaH,MAAMC,aAAP,GAAuB;AACrCW,sBAAcM,KAAKlB,IAAnB;;;AAIJ,QAAID,QAAQC,MAAMC,aAAP,GAAuB;AAChC,aAAOW;;AAGT,WAAOC,wBAAwBb,KAAKqB,UAAN;;AAGhC,MAAI,CAAC/B,SAAS;AACZ,WAAOsB;;AAGT,SAAOC,wBAAwBvB,OAAD;AAC/B;AAED,SAAgBgC,2BAA2BtB,MAAAA;AACzC,QAAM,CAACuB,uBAAD,IAA4Bb,uBAAuBV,MAAM,CAAP;AAExD,SAAOuB,2BAAP,OAAOA,0BAA2B;AACnC;SC5DeC,qBAAqBlC,SAAAA;AACnC,MAAI,CAACmC,aAAa,CAACnC,SAAS;AAC1B,WAAO;;AAGT,MAAIoC,SAASpC,OAAD,GAAW;AACrB,WAAOA;;AAGT,MAAI,CAACqC,OAAOrC,OAAD,GAAW;AACpB,WAAO;;AAGT,MACEyB,WAAWzB,OAAD,KACVA,YAAYsC,iBAAiBtC,OAAD,EAAU0B,kBACtC;AACA,WAAOa;;AAGT,MAAIV,cAAc7B,OAAD,GAAW;AAC1B,WAAOA;;AAGT,SAAO;AACR;SC9BewC,qBAAqBxC,SAAAA;AACnC,MAAIoC,SAASpC,OAAD,GAAW;AACrB,WAAOA,QAAQyC;;AAGjB,SAAOzC,QAAQ0C;AAChB;AAED,SAAgBC,qBAAqB3C,SAAAA;AACnC,MAAIoC,SAASpC,OAAD,GAAW;AACrB,WAAOA,QAAQ4C;;AAGjB,SAAO5C,QAAQ6C;AAChB;AAED,SAAgBC,qBACd9C,SAAAA;AAEA,SAAO;IACLjC,GAAGyE,qBAAqBxC,OAAD;IACvB/B,GAAG0E,qBAAqB3C,OAAD;;AAE1B;AC3BD,IAAY+C;CAAZ,SAAYA,YAAAA;AACVA,EAAAA,WAAAA,WAAAA,SAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,UAAAA,IAAAA,EAAAA,IAAA;AACD,GAHWA,cAAAA,YAAS,CAAA,EAArB;SCEgBC,2BAA2BhD,SAAAA;AACzC,MAAI,CAACmC,aAAa,CAACnC,SAAS;AAC1B,WAAO;;AAGT,SAAOA,YAAYiD,SAASvB;AAC7B;SCNewB,kBAAkBC,oBAAAA;AAChC,QAAMC,YAAY;IAChBrF,GAAG;IACHE,GAAG;;AAEL,QAAMoF,aAAaL,2BAA2BG,kBAAD,IACzC;IACEtF,QAAQ0E,OAAO/B;IACf7C,OAAO4E,OAAOhC;MAEhB;IACE1C,QAAQsF,mBAAmBG;IAC3B3F,OAAOwF,mBAAmBI;;AAEhC,QAAMC,YAAY;IAChBzF,GAAGoF,mBAAmBM,cAAcJ,WAAW1F;IAC/CM,GAAGkF,mBAAmBO,eAAeL,WAAWxF;;AAGlD,QAAM8F,QAAQR,mBAAmBN,aAAaO,UAAUnF;AACxD,QAAM2F,SAAST,mBAAmBT,cAAcU,UAAUrF;AAC1D,QAAM8F,WAAWV,mBAAmBN,aAAaW,UAAUvF;AAC3D,QAAM6F,UAAUX,mBAAmBT,cAAcc,UAAUzF;AAE3D,SAAO;IACL4F;IACAC;IACAC;IACAC;IACAN;IACAJ;;AAEH;AC5BD,IAAMW,mBAAmB;EACvBhG,GAAG;EACHE,GAAG;AAFoB;AAKzB,SAAgB+F,2BACdC,iBACAC,qBAAAA,MAEAC,cACAC,qBAAAA;MAFA;IAAClG;IAAKF;IAAMa;IAAOD;;MACnBuF,iBAAAA,QAAAA;AAAAA,mBAAe;;MACfC,wBAAAA,QAAAA;AAAAA,0BAAsBL;;AAEtB,QAAM;IAACJ;IAAOE;IAAUD;IAAQE;MAAWZ,kBAAkBe,eAAD;AAE5D,QAAMI,YAAY;IAChBtG,GAAG;IACHE,GAAG;;AAEL,QAAMqG,QAAQ;IACZvG,GAAG;IACHE,GAAG;;AAEL,QAAMsG,YAAY;IAChB1G,QAAQqG,oBAAoBrG,SAASuG,oBAAoBnG;IACzDN,OAAOuG,oBAAoBvG,QAAQyG,oBAAoBrG;;AAGzD,MAAI,CAAC4F,SAASzF,OAAOgG,oBAAoBhG,MAAMqG,UAAU1G,QAAQ;AAE/DwG,cAAUpG,IAAI8E,UAAUyB;AACxBF,UAAMrG,IACJkG,eACAM,KAAKC,KACFR,oBAAoBhG,MAAMqG,UAAU1G,SAASK,OAAOqG,UAAU1G,MADjE;aAIF,CAACgG,YACDjF,UAAUsF,oBAAoBtF,SAAS2F,UAAU1G,QACjD;AAEAwG,cAAUpG,IAAI8E,UAAU4B;AACxBL,UAAMrG,IACJkG,eACAM,KAAKC,KACFR,oBAAoBtF,SAAS2F,UAAU1G,SAASe,UAC/C2F,UAAU1G,MAFd;;AAMJ,MAAI,CAACiG,WAAWjF,SAASqF,oBAAoBrF,QAAQ0F,UAAU5G,OAAO;AAEpE0G,cAAUtG,IAAIgF,UAAU4B;AACxBL,UAAMvG,IACJoG,eACAM,KAAKC,KACFR,oBAAoBrF,QAAQ0F,UAAU5G,QAAQkB,SAAS0F,UAAU5G,KADpE;aAGO,CAACiG,UAAU5F,QAAQkG,oBAAoBlG,OAAOuG,UAAU5G,OAAO;AAExE0G,cAAUtG,IAAIgF,UAAUyB;AACxBF,UAAMvG,IACJoG,eACAM,KAAKC,KACFR,oBAAoBlG,OAAOuG,UAAU5G,QAAQK,QAAQuG,UAAU5G,KADlE;;AAKJ,SAAO;IACL0G;IACAC;;AAEH;SC7EeM,qBAAqB5E,SAAAA;AACnC,MAAIA,YAAYiD,SAASvB,kBAAkB;AACzC,UAAM;MAACnB;MAAYC;QAAe+B;AAElC,WAAO;MACLrE,KAAK;MACLF,MAAM;MACNa,OAAO0B;MACP3B,QAAQ4B;MACR7C,OAAO4C;MACP1C,QAAQ2C;;;AAIZ,QAAM;IAACtC;IAAKF;IAAMa;IAAOD;MAAUoB,QAAQE,sBAAR;AAEnC,SAAO;IACLhC;IACAF;IACAa;IACAD;IACAjB,OAAOqC,QAAQuD;IACf1F,QAAQmC,QAAQsD;;AAEnB;SCdeuB,iBAAiBC,qBAAAA;AAC/B,SAAOA,oBAAoBrG,OAAoB,CAACC,KAAKgC,SAAN;AAC7C,WAAOqE,IAAIrG,KAAKoE,qBAAqBpC,IAAD,CAA1B;KACTvC,kBAFI;AAGR;AAED,SAAgB6G,iBAAiBF,qBAAAA;AAC/B,SAAOA,oBAAoBrG,OAAe,CAACC,KAAKgC,SAAN;AACxC,WAAOhC,MAAM8D,qBAAqB9B,IAAD;KAChC,CAFI;AAGR;AAED,SAAgBuE,iBAAiBH,qBAAAA;AAC/B,SAAOA,oBAAoBrG,OAAe,CAACC,KAAKgC,SAAN;AACxC,WAAOhC,MAAMiE,qBAAqBjC,IAAD;KAChC,CAFI;AAGR;SCtBewE,uBACdlF,SACAmF,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAA6CpF;;AAE7C,MAAI,CAACC,SAAS;AACZ;;AAGF,QAAM;IAAC9B;IAAKF;IAAMY;IAAQC;MAASsG,QAAQnF,OAAD;AAC1C,QAAMiC,0BAA0BD,2BAA2BhC,OAAD;AAE1D,MAAI,CAACiC,yBAAyB;AAC5B;;AAGF,MACErD,UAAU,KACVC,SAAS,KACTX,OAAOqE,OAAO/B,eACdxC,QAAQuE,OAAOhC,YACf;AACAP,YAAQoF,eAAe;MACrBC,OAAO;MACPC,QAAQ;KAFV;;AAKH;ACtBD,IAAMvE,aAAa,CACjB,CAAC,KAAK,CAAC,QAAQ,OAAT,GAAmBiE,gBAAzB,GACA,CAAC,KAAK,CAAC,OAAO,QAAR,GAAmBC,gBAAzB,CAFiB;AAKnB,IAAaM,OAAb,MAAaA;EACXC,YAAYjH,MAAkByB,SAAAA;SAyBtBzB,OAAAA;SAEDZ,QAAAA;SAEAE,SAAAA;SAIAK,MAAAA;SAEAU,SAAAA;SAEAC,QAAAA;SAEAb,OAAAA;AAtCL,UAAM8G,sBAAsB1D,uBAAuBpB,OAAD;AAClD,UAAMyF,gBAAgBZ,iBAAiBC,mBAAD;AAEtC,SAAKvG,OAAO;MAAC,GAAGA;;AAChB,SAAKZ,QAAQY,KAAKZ;AAClB,SAAKE,SAASU,KAAKV;AAEnB,eAAW,CAAC6H,MAAMC,MAAMC,eAAb,KAAiC7E,YAAY;AACtD,iBAAW8E,QAAOF,MAAM;AACtBG,eAAOC,eAAe,MAAMF,MAAK;UAC/BG,KAAK,MAAA;AACH,kBAAMC,iBAAiBL,gBAAgBd,mBAAD;AACtC,kBAAMoB,sBAAsBT,cAAcC,IAAD,IAASO;AAElD,mBAAO,KAAK1H,KAAKsH,IAAV,IAAiBK;;UAE1BC,YAAY;SAPd;;;AAYJL,WAAOC,eAAe,MAAM,QAAQ;MAACI,YAAY;KAAjD;;;ICpCSC,kBAAAA;EAOXZ,YAAoBa,QAAAA;SAAAA,SAAAA;SANZC,YAIF,CAAA;SAaCC,YAAY,MAAA;AACjB,WAAKD,UAAUE,QAASC,cAAD;AAAA,YAAA;AAAA,gBAAA,eACrB,KAAKJ,WADgB,OAAA,SACrB,aAAaK,oBAAoB,GAAGD,QAApC;OADF;;AAZkB,SAAA,SAAAJ;;EAEbtB,IACL4B,WACAC,SACA3G,SAHQ;;AAKR,KAAA,gBAAA,KAAKoG,WAAL,OAAA,SAAA,cAAaQ,iBAAiBF,WAAWC,SAA0B3G,OAAnE;AACA,SAAKqG,UAAU1E,KAAK,CAAC+E,WAAWC,SAA0B3G,OAAtC,CAApB;;;SCbY6G,uBACdT,QAAAA;AAQA,QAAM;IAACU;MAAe5G,UAAUkG,MAAD;AAE/B,SAAOA,kBAAkBU,cAAcV,SAAS/D,iBAAiB+D,MAAD;AACjE;SCZeW,oBACdC,OACAC,aAAAA;AAEA,QAAMC,KAAK1C,KAAKC,IAAIuC,MAAMlJ,CAAf;AACX,QAAMqJ,KAAK3C,KAAKC,IAAIuC,MAAMhJ,CAAf;AAEX,MAAI,OAAOiJ,gBAAgB,UAAU;AACnC,WAAOzC,KAAK4C,KAAKF,MAAM,IAAIC,MAAM,CAA1B,IAA+BF;;AAGxC,MAAI,OAAOA,eAAe,OAAOA,aAAa;AAC5C,WAAOC,KAAKD,YAAYnJ,KAAKqJ,KAAKF,YAAYjJ;;AAGhD,MAAI,OAAOiJ,aAAa;AACtB,WAAOC,KAAKD,YAAYnJ;;AAG1B,MAAI,OAAOmJ,aAAa;AACtB,WAAOE,KAAKF,YAAYjJ;;AAG1B,SAAO;AACR;AC1BD,IAAYqJ;CAAZ,SAAYA,YAAAA;AACVA,EAAAA,WAAAA,OAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,IAAA;AACAA,EAAAA,WAAAA,SAAAA,IAAA;AACAA,EAAAA,WAAAA,aAAAA,IAAA;AACAA,EAAAA,WAAAA,QAAAA,IAAA;AACAA,EAAAA,WAAAA,iBAAAA,IAAA;AACAA,EAAAA,WAAAA,kBAAAA,IAAA;AACD,GARWA,cAAAA,YAAS,CAAA,EAArB;AAUA,SAAgBC,eAAeC,OAAAA;AAC7BA,QAAMD,eAAN;AACD;AAED,SAAgBE,gBAAgBD,OAAAA;AAC9BA,QAAMC,gBAAN;AACD;ICbWC;CAAZ,SAAYA,eAAAA;AACVA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,MAAAA,IAAA;AACAA,EAAAA,cAAAA,IAAAA,IAAA;AACAA,EAAAA,cAAAA,KAAAA,IAAA;AACAA,EAAAA,cAAAA,OAAAA,IAAA;AACAA,EAAAA,cAAAA,KAAAA,IAAA;AACD,GATWA,iBAAAA,eAAY,CAAA,EAAxB;ACDO,IAAMC,uBAAsC;EACjDC,OAAO,CAACF,aAAaG,OAAOH,aAAaI,KAAlC;EACPC,QAAQ,CAACL,aAAaM,GAAd;EACRC,KAAK,CAACP,aAAaG,OAAOH,aAAaI,OAAOJ,aAAaQ,GAAtD;AAH4C;AAMnD,IAAaC,kCAA4D,CACvEX,OADuE,SAAA;MAEvE;IAACY;;AAED,UAAQZ,MAAMa,MAAd;IACE,KAAKX,aAAaY;AAChB,aAAO;QACL,GAAGF;QACHrK,GAAGqK,mBAAmBrK,IAAI;;IAE9B,KAAK2J,aAAaa;AAChB,aAAO;QACL,GAAGH;QACHrK,GAAGqK,mBAAmBrK,IAAI;;IAE9B,KAAK2J,aAAac;AAChB,aAAO;QACL,GAAGJ;QACHnK,GAAGmK,mBAAmBnK,IAAI;;IAE9B,KAAKyJ,aAAae;AAChB,aAAO;QACL,GAAGL;QACHnK,GAAGmK,mBAAmBnK,IAAI;;;AAIhC,SAAOyK;AACR;ICGYC,uBAAAA;EAMXnD,YAAoBoD,OAAAA;SAAAA,QAAAA;SALbC,oBAAoB;SACnBC,uBAAAA;SACAxC,YAAAA;SACAyC,kBAAAA;AAEY,SAAA,QAAAH;AAClB,UAAM;MACJpB,OAAO;QAACnB;;QACNuC;AAEJ,SAAKA,QAAQA;AACb,SAAKtC,YAAY,IAAIF,UAAU9D,iBAAiB+D,MAAD,CAA9B;AACjB,SAAK0C,kBAAkB,IAAI3C,UAAUjG,UAAUkG,MAAD,CAAvB;AACvB,SAAK2C,gBAAgB,KAAKA,cAAcC,KAAK,IAAxB;AACrB,SAAKC,eAAe,KAAKA,aAAaD,KAAK,IAAvB;AAEpB,SAAKE,OAAL;;EAGMA,SAAM;AACZ,SAAKC,YAAL;AAEA,SAAKL,gBAAgBhE,IAAIuC,UAAU+B,QAAQ,KAAKH,YAAhD;AACA,SAAKH,gBAAgBhE,IAAIuC,UAAUgC,kBAAkB,KAAKJ,YAA1D;AAEAK,eAAW,MAAM,KAAKjD,UAAUvB,IAAIuC,UAAUkC,SAAS,KAAKR,aAA3C,CAAP;;EAGJI,cAAW;AACjB,UAAM;MAACK;MAAYC;QAAW,KAAKd;AACnC,UAAMlI,OAAO+I,WAAW/I,KAAKiJ;AAE7B,QAAIjJ,MAAM;AACRwE,6BAAuBxE,IAAD;;AAGxBgJ,YAAQvL,kBAAD;;EAGD6K,cAAcxB,OAAD;AACnB,QAAIoC,gBAAgBpC,KAAD,GAAS;AAC1B,YAAM;QAACqC;QAAQC;QAAS7J;UAAW,KAAK2I;AACxC,YAAM;QACJmB,gBAAgBpC;QAChBqC,mBAAmB7B;QACnB8B,iBAAiB;UACfhK;AACJ,YAAM;QAACoI;UAAQb;AAEf,UAAIuC,cAAc9B,IAAItG,SAAS0G,IAA3B,GAAkC;AACpC,aAAK6B,UAAU1C,KAAf;AACA;;AAGF,UAAIuC,cAAchC,OAAOpG,SAAS0G,IAA9B,GAAqC;AACvC,aAAKa,aAAa1B,KAAlB;AACA;;AAGF,YAAM;QAAC2C;UAAiBL,QAAQH;AAChC,YAAMvB,qBAAqB+B,gBACvB;QAACpM,GAAGoM,cAAcnM;QAAMC,GAAGkM,cAAcjM;UACzCC;AAEJ,UAAI,CAAC,KAAK2K,sBAAsB;AAC9B,aAAKA,uBAAuBV;;AAG9B,YAAMgC,iBAAiBJ,iBAAiBxC,OAAO;QAC7CqC;QACAC,SAASA,QAAQH;QACjBvB;OAHqC;AAMvC,UAAIgC,gBAAgB;AAClB,cAAMC,mBAAmBC,SACvBF,gBACAhC,kBAF0C;AAI5C,cAAMmC,cAAc;UAClBxM,GAAG;UACHE,GAAG;;AAEL,cAAM;UAAC6G;YAAuBgF,QAAQH;AAEtC,mBAAW1F,mBAAmBa,qBAAqB;AACjD,gBAAMT,YAAYmD,MAAMa;AACxB,gBAAM;YAAC1E;YAAOG;YAASF;YAAQC;YAAUL;YAAWJ;cAClDF,kBAAkBe,eAAD;AACnB,gBAAMuG,oBAAoB5F,qBAAqBX,eAAD;AAE9C,gBAAMwG,qBAAqB;YACzB1M,GAAG0G,KAAKiG,IACNrG,cAAcqD,aAAaY,QACvBkC,kBAAkB3L,QAAQ2L,kBAAkB7M,QAAQ,IACpD6M,kBAAkB3L,OACtB4F,KAAKkG,IACHtG,cAAcqD,aAAaY,QACvBkC,kBAAkBxM,OAClBwM,kBAAkBxM,OAAOwM,kBAAkB7M,QAAQ,GACvDyM,eAAerM,CAJjB,CAJC;YAWHE,GAAGwG,KAAKiG,IACNrG,cAAcqD,aAAac,OACvBgC,kBAAkB5L,SAAS4L,kBAAkB3M,SAAS,IACtD2M,kBAAkB5L,QACtB6F,KAAKkG,IACHtG,cAAcqD,aAAac,OACvBgC,kBAAkBtM,MAClBsM,kBAAkBtM,MAAMsM,kBAAkB3M,SAAS,GACvDuM,eAAenM,CAJjB,CAJC;;AAaL,gBAAM2M,aACHvG,cAAcqD,aAAaY,SAAS,CAACxE,WACrCO,cAAcqD,aAAaa,QAAQ,CAAC3E;AACvC,gBAAMiH,aACHxG,cAAcqD,aAAac,QAAQ,CAAC3E,YACpCQ,cAAcqD,aAAae,MAAM,CAAC9E;AAErC,cAAIiH,cAAcH,mBAAmB1M,MAAMqM,eAAerM,GAAG;AAC3D,kBAAM+M,uBACJ7G,gBAAgBvB,aAAa2H,iBAAiBtM;AAChD,kBAAMgN,4BACH1G,cAAcqD,aAAaY,SAC1BwC,wBAAwBtH,UAAUzF,KACnCsG,cAAcqD,aAAaa,QAC1BuC,wBAAwB1H,UAAUrF;AAEtC,gBAAIgN,6BAA6B,CAACV,iBAAiBpM,GAAG;AAGpDgG,8BAAgB+G,SAAS;gBACvBhN,MAAM8M;gBACNG,UAAUhB;eAFZ;AAIA;;AAGF,gBAAIc,2BAA2B;AAC7BR,0BAAYxM,IAAIkG,gBAAgBvB,aAAaoI;mBACxC;AACLP,0BAAYxM,IACVsG,cAAcqD,aAAaY,QACvBrE,gBAAgBvB,aAAac,UAAUzF,IACvCkG,gBAAgBvB,aAAaU,UAAUrF;;AAG/C,gBAAIwM,YAAYxM,GAAG;AACjBkG,8BAAgBiH,SAAS;gBACvBlN,MAAM,CAACuM,YAAYxM;gBACnBkN,UAAUhB;eAFZ;;AAKF;qBACSY,cAAcJ,mBAAmBxM,MAAMmM,eAAenM,GAAG;AAClE,kBAAM6M,uBACJ7G,gBAAgBpB,YAAYwH,iBAAiBpM;AAC/C,kBAAM8M,4BACH1G,cAAcqD,aAAac,QAC1BsC,wBAAwBtH,UAAUvF,KACnCoG,cAAcqD,aAAae,MAC1BqC,wBAAwB1H,UAAUnF;AAEtC,gBAAI8M,6BAA6B,CAACV,iBAAiBtM,GAAG;AAGpDkG,8BAAgB+G,SAAS;gBACvB9M,KAAK4M;gBACLG,UAAUhB;eAFZ;AAIA;;AAGF,gBAAIc,2BAA2B;AAC7BR,0BAAYtM,IAAIgG,gBAAgBpB,YAAYiI;mBACvC;AACLP,0BAAYtM,IACVoG,cAAcqD,aAAac,OACvBvE,gBAAgBpB,YAAYW,UAAUvF,IACtCgG,gBAAgBpB,YAAYO,UAAUnF;;AAG9C,gBAAIsM,YAAYtM,GAAG;AACjBgG,8BAAgBiH,SAAS;gBACvBhN,KAAK,CAACqM,YAAYtM;gBAClBgN,UAAUhB;eAFZ;;AAMF;;;AAIJ,aAAKkB,WACH3D,OACA4D,IACEd,SAAoBF,gBAAgB,KAAKtB,oBAAtB,GACnByB,WAFoB,CAFxB;;;;EAWEY,WAAW3D,OAAc6D,aAAf;AAChB,UAAM;MAACC;QAAU,KAAK1C;AAEtBpB,UAAMD,eAAN;AACA+D,WAAOD,WAAD;;EAGAnB,UAAU1C,OAAD;AACf,UAAM;MAAC+D;QAAS,KAAK3C;AAErBpB,UAAMD,eAAN;AACA,SAAKiE,OAAL;AACAD,UAAK;;EAGCrC,aAAa1B,OAAD;AAClB,UAAM;MAACiE;QAAY,KAAK7C;AAExBpB,UAAMD,eAAN;AACA,SAAKiE,OAAL;AACAC,aAAQ;;EAGFD,SAAM;AACZ,SAAKlF,UAAUC,UAAf;AACA,SAAKwC,gBAAgBxC,UAArB;;;AA1OSoC,eA6OJ+C,aAAgD,CACrD;EACE/E,WAAW;EACXC,SAAS,CACPY,OADO,MAAA,UAAA;QAEP;MAACuC,gBAAgBpC;MAAsBgE;;QACvC;MAAC9B;;AAED,UAAM;MAACxB;QAAQb,MAAMoE;AAErB,QAAI7B,cAAcnC,MAAMjG,SAAS0G,IAA7B,GAAoC;AACtC,YAAMwD,YAAYhC,OAAOiC,cAAcnC;AAEvC,UAAIkC,aAAarE,MAAMnB,WAAWwF,WAAW;AAC3C,eAAO;;AAGTrE,YAAMD,eAAN;AAEAoE,sBAAY,OAAZ,SAAAA,aAAe;QAACnE,OAAOA,MAAMoE;OAAjB;AAEZ,aAAO;;AAGT,WAAO;;AAvBX,CADqD;ACxOzD,SAASG,qBACPC,YADF;AAGE,SAAOC,QAAQD,cAAc,cAAcA,UAA7B;AACf;AAED,SAASE,kBACPF,YADF;AAGE,SAAOC,QAAQD,cAAc,WAAWA,UAA1B;AACf;AAaD,IAAaG,wBAAb,MAAaA;EAUX3G,YACUoD,OACAwD,SACRC,gBAAAA;;QAAAA,mBAAAA,QAAAA;AAAAA,uBAAiBvF,uBAAuB8B,MAAMpB,MAAMnB,MAAb;;SAF/BuC,QAAAA;SACAwD,SAAAA;SAXHvD,oBAAoB;SACnB5F,WAAAA;SACAqJ,YAAqB;SACrBC,qBAAAA;SACAC,YAAmC;SACnClG,YAAAA;SACAmG,oBAAAA;SACA1D,kBAAAA;AAGE,SAAA,QAAAH;AACA,SAAA,SAAAwD;AAGR,UAAM;MAAC5E;QAASoB;AAChB,UAAM;MAACvC;QAAUmB;AAEjB,SAAKoB,QAAQA;AACb,SAAKwD,SAASA;AACd,SAAKnJ,WAAWX,iBAAiB+D,MAAD;AAChC,SAAKoG,oBAAoB,IAAIrG,UAAU,KAAKnD,QAAnB;AACzB,SAAKqD,YAAY,IAAIF,UAAUiG,cAAd;AACjB,SAAKtD,kBAAkB,IAAI3C,UAAUjG,UAAUkG,MAAD,CAAvB;AACvB,SAAKkG,sBAAL,uBAA0BG,oBAAoBlF,KAAD,MAA7C,OAAA,uBAAwDrJ;AACxD,SAAKiL,cAAc,KAAKA,YAAYH,KAAK,IAAtB;AACnB,SAAKkC,aAAa,KAAKA,WAAWlC,KAAK,IAArB;AAClB,SAAKiB,YAAY,KAAKA,UAAUjB,KAAK,IAApB;AACjB,SAAKC,eAAe,KAAKA,aAAaD,KAAK,IAAvB;AACpB,SAAK0D,gBAAgB,KAAKA,cAAc1D,KAAK,IAAxB;AACrB,SAAK2D,sBAAsB,KAAKA,oBAAoB3D,KAAK,IAA9B;AAE3B,SAAKE,OAAL;;EAGMA,SAAM;AACZ,UAAM;MACJiD,QAAAA;MACAxD,OAAO;QACL3I,SAAS;UAAC4M;UAAsBC;;;QAEhC;AAEJ,SAAKxG,UAAUvB,IAAIqH,QAAOW,KAAKC,MAAM,KAAK7B,YAAY;MAAC8B,SAAS;KAAhE;AACA,SAAK3G,UAAUvB,IAAIqH,QAAOnE,IAAI+E,MAAM,KAAK9C,SAAzC;AAEA,QAAIkC,QAAOrE,QAAQ;AACjB,WAAKzB,UAAUvB,IAAIqH,QAAOrE,OAAOiF,MAAM,KAAK9D,YAA5C;;AAGF,SAAKH,gBAAgBhE,IAAIuC,UAAU+B,QAAQ,KAAKH,YAAhD;AACA,SAAKH,gBAAgBhE,IAAIuC,UAAU4F,WAAW3F,cAA9C;AACA,SAAKwB,gBAAgBhE,IAAIuC,UAAUgC,kBAAkB,KAAKJ,YAA1D;AACA,SAAKH,gBAAgBhE,IAAIuC,UAAU6F,aAAa5F,cAAhD;AACA,SAAKkF,kBAAkB1H,IAAIuC,UAAUkC,SAAS,KAAKmD,aAAnD;AAEA,QAAIE,sBAAsB;AACxB,UACEC,8BADF,QACEA,2BAA6B;QAC3BtF,OAAO,KAAKoB,MAAMpB;QAClBiC,YAAY,KAAKb,MAAMa;QACvBxJ,SAAS,KAAK2I,MAAM3I;OAHI,GAK1B;AACA,eAAO,KAAKmJ,YAAL;;AAGT,UAAI8C,kBAAkBW,oBAAD,GAAwB;AAC3C,aAAKL,YAAYjD,WACf,KAAKH,aACLyD,qBAAqBO,KAFI;AAI3B,aAAKC,cAAcR,oBAAnB;AACA;;AAGF,UAAId,qBAAqBc,oBAAD,GAAwB;AAC9C,aAAKQ,cAAcR,oBAAnB;AACA;;;AAIJ,SAAKzD,YAAL;;EAGMoC,SAAM;AACZ,SAAKlF,UAAUC,UAAf;AACA,SAAKwC,gBAAgBxC,UAArB;AAIAgD,eAAW,KAAKkD,kBAAkBlG,WAAW,EAAnC;AAEV,QAAI,KAAKiG,cAAc,MAAM;AAC3Bc,mBAAa,KAAKd,SAAN;AACZ,WAAKA,YAAY;;;EAIba,cACNrB,YACAuB,QAFmB;AAInB,UAAM;MAAC1D;MAAQ2D;QAAa,KAAK5E;AACjC4E,cAAU3D,QAAQmC,YAAY,KAAKO,oBAAoBgB,MAA9C;;EAGHnE,cAAW;AACjB,UAAM;MAACmD;QAAsB;AAC7B,UAAM;MAAC7C;QAAW,KAAKd;AAEvB,QAAI2D,oBAAoB;AACtB,WAAKD,YAAY;AAGjB,WAAKG,kBAAkB1H,IAAIuC,UAAUmG,OAAOhG,iBAAiB;QAC3DiG,SAAS;OADX;AAKA,WAAKd,oBAAL;AAGA,WAAKH,kBAAkB1H,IACrBuC,UAAUqG,iBACV,KAAKf,mBAFP;AAKAlD,cAAQ6C,kBAAD;;;EAIHpB,WAAW3D,OAAD;;AAChB,UAAM;MAAC8E;MAAWC;MAAoB3D;QAAS;AAC/C,UAAM;MACJ0C;MACArL,SAAS;QAAC4M;;QACRjE;AAEJ,QAAI,CAAC2D,oBAAoB;AACvB;;AAGF,UAAMlB,eAAW,wBAAGqB,oBAAoBlF,KAAD,MAAtB,OAAA,wBAAiCrJ;AAClD,UAAM8I,QAAQqD,SAAoBiC,oBAAoBlB,WAArB;AAGjC,QAAI,CAACiB,aAAaO,sBAAsB;AACtC,UAAId,qBAAqBc,oBAAD,GAAwB;AAC9C,YACEA,qBAAqBe,aAAa,QAClC5G,oBAAoBC,OAAO4F,qBAAqBe,SAA7B,GACnB;AACA,iBAAO,KAAK1E,aAAL;;AAGT,YAAIlC,oBAAoBC,OAAO4F,qBAAqBgB,QAA7B,GAAwC;AAC7D,iBAAO,KAAKzE,YAAL;;;AAIX,UAAI8C,kBAAkBW,oBAAD,GAAwB;AAC3C,YAAI7F,oBAAoBC,OAAO4F,qBAAqBe,SAA7B,GAAyC;AAC9D,iBAAO,KAAK1E,aAAL;;;AAIX,WAAKmE,cAAcR,sBAAsB5F,KAAzC;AACA;;AAGF,QAAIO,MAAMsG,YAAY;AACpBtG,YAAMD,eAAN;;AAGF+D,WAAOD,WAAD;;EAGAnB,YAAS;AACf,UAAM;MAAC6D;MAASxC;QAAS,KAAK3C;AAE9B,SAAK4C,OAAL;AACA,QAAI,CAAC,KAAKc,WAAW;AACnByB,cAAQ,KAAKnF,MAAMiB,MAAZ;;AAET0B,UAAK;;EAGCrC,eAAY;AAClB,UAAM;MAAC6E;MAAStC;QAAY,KAAK7C;AAEjC,SAAK4C,OAAL;AACA,QAAI,CAAC,KAAKc,WAAW;AACnByB,cAAQ,KAAKnF,MAAMiB,MAAZ;;AAET4B,aAAQ;;EAGFkB,cAAcnF,OAAD;AACnB,QAAIA,MAAMa,SAASX,aAAaM,KAAK;AACnC,WAAKkB,aAAL;;;EAII0D,sBAAmB;;AACzB,KAAA,wBAAA,KAAK3J,SAAS+K,aAAd,MAAA,OAAA,SAAA,sBAA8BC,gBAA9B;;;ACtQJ,IAAM7B,SAA+B;EACnCrE,QAAQ;IAACiF,MAAM;;EACfD,MAAM;IAACC,MAAM;;EACb/E,KAAK;IAAC+E,MAAM;;AAHuB;AAUrC,IAAakB,gBAAb,cAAmC/B,sBAAAA;EACjC3G,YAAYoD,OAAAA;AACV,UAAM;MAACpB;QAASoB;AAGhB,UAAMyD,iBAAiB/J,iBAAiBkF,MAAMnB,MAAP;AAEvC,UAAMuC,OAAOwD,QAAQC,cAArB;;;AAPS6B,cAUJxC,aAAa,CAClB;EACE/E,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAACgF,aAAapE;;QACd;MAACmE;;AAED,QAAI,CAACnE,MAAM2G,aAAa3G,MAAM4G,WAAW,GAAG;AAC1C,aAAO;;AAGTzC,oBAAY,OAAZ,SAAAA,aAAe;MAACnE;KAAJ;AAEZ,WAAO;;AAZX,CADkB;ACpBtB,IAAM4E,WAA+B;EACnCW,MAAM;IAACC,MAAM;;EACb/E,KAAK;IAAC+E,MAAM;;AAFuB;AAKrC,IAAKqB;CAAL,SAAKA,cAAAA;AACHA,EAAAA,aAAAA,aAAAA,YAAAA,IAAAA,CAAAA,IAAA;AACD,GAFIA,gBAAAA,cAAW,CAAA,EAAhB;AAQA,IAAaC,cAAb,cAAiCnC,sBAAAA;EAC/B3G,YAAYoD,OAAAA;AACV,UAAMA,OAAOwD,UAAQ9J,iBAAiBsG,MAAMpB,MAAMnB,MAAb,CAArC;;;AAFSiI,YAKJ5C,aAAa,CAClB;EACE/E,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAACgF,aAAapE;;QACd;MAACmE;;AAED,QAAInE,MAAM4G,WAAWC,YAAYE,YAAY;AAC3C,aAAO;;AAGT5C,oBAAY,OAAZ,SAAAA,aAAe;MAACnE;KAAJ;AAEZ,WAAO;;AAZX,CADkB;AClBtB,IAAM4E,WAA+B;EACnCrE,QAAQ;IAACiF,MAAM;;EACfD,MAAM;IAACC,MAAM;;EACb/E,KAAK;IAAC+E,MAAM;;AAHuB;AAUrC,IAAawB,cAAb,cAAiCrC,sBAAAA;EAC/B3G,YAAYoD,OAAAA;AACV,UAAMA,OAAOwD,QAAb;;EAuBU,OAALqC,QAAK;AAIVlM,WAAOsE,iBAAiBuF,SAAOW,KAAKC,MAAM0B,OAAM;MAC9ChB,SAAS;MACTT,SAAS;KAFX;AAKA,WAAO,SAAS0B,WAAT;AACLpM,aAAOmE,oBAAoB0F,SAAOW,KAAKC,MAAM0B,KAA7C;;AAKF,aAASA,QAAT;IAAA;;;AAxCSF,YAKJ9C,aAAa,CAClB;EACE/E,WAAW;EACXC,SAAS,CAAA,MAAA,UAAA;QACP;MAACgF,aAAapE;;QACd;MAACmE;;AAED,UAAM;MAACiD;QAAWpH;AAElB,QAAIoH,QAAQpN,SAAS,GAAG;AACtB,aAAO;;AAGTmK,oBAAY,OAAZ,SAAAA,aAAe;MAACnE;KAAJ;AAEZ,WAAO;;AAdX,CADkB;IChBVqH;CAAZ,SAAYA,sBAAAA;AACVA,EAAAA,qBAAAA,qBAAAA,SAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,qBAAAA,qBAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACD,GAHWA,wBAAAA,sBAAmB,CAAA,EAA/B;AAmCA,IAAYC;CAAZ,SAAYA,iBAAAA;AACVA,EAAAA,gBAAAA,gBAAAA,WAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,gBAAAA,gBAAAA,mBAAAA,IAAAA,CAAAA,IAAA;AACD,GAHWA,mBAAAA,iBAAc,CAAA,EAA1B;AAUA,SAAgBC,gBAAAA,MAAAA;MAAgB;IAC9B5K;IACA0H,YAAYgD,oBAAoBG;IAChCC;IACAC;IACAC;IACAC,WAAW;IACXC,QAAQP,eAAeQ;IACvBC;IACAzK;IACA0K;IACAvI;IACA1C;;AAEA,QAAMkL,eAAeC,gBAAgB;IAACzI;IAAO0I,UAAU,CAACR;GAApB;AACpC,QAAM,CAACS,uBAAuBC,uBAAxB,IAAmDC,YAAW;AACpE,QAAMC,kBAAcC,sBAAoB;IAACjS,GAAG;IAAGE,GAAG;GAAxB;AAC1B,QAAMgS,sBAAkBD,sBAAwB;IAACjS,GAAG;IAAGE,GAAG;GAA5B;AAC9B,QAAMM,WAAO2R,uBAAQ,MAAA;AACnB,YAAQrE,WAAR;MACE,KAAKgD,oBAAoBG;AACvB,eAAOO,qBACH;UACErR,KAAKqR,mBAAmBtR;UACxBW,QAAQ2Q,mBAAmBtR;UAC3BD,MAAMuR,mBAAmBxR;UACzBc,OAAO0Q,mBAAmBxR;YAE5B;MACN,KAAK8Q,oBAAoBsB;AACvB,eAAOjB;;KAEV,CAACrD,WAAWqD,cAAcK,kBAA1B,CAdiB;AAepB,QAAMa,yBAAqBJ,sBAAuB,IAAjB;AACjC,QAAMK,iBAAaC,2BAAY,MAAA;AAC7B,UAAMrM,kBAAkBmM,mBAAmBzG;AAE3C,QAAI,CAAC1F,iBAAiB;AACpB;;AAGF,UAAMvB,aAAaqN,YAAYpG,QAAQ5L,IAAIkS,gBAAgBtG,QAAQ5L;AACnE,UAAM8E,YAAYkN,YAAYpG,QAAQ1L,IAAIgS,gBAAgBtG,QAAQ1L;AAElEgG,oBAAgBiH,SAASxI,YAAYG,SAArC;KACC,CAAA,CAX2B;AAY9B,QAAM0N,gCAA4BL,uBAChC,MACEb,UAAUP,eAAeQ,YACrB,CAAC,GAAGxK,mBAAJ,EAAyB0L,QAAzB,IACA1L,qBACN,CAACuK,OAAOvK,mBAAR,CALuC;AAQzC2L;IACE,MAAA;AACE,UAAI,CAACtB,WAAW,CAACrK,oBAAoBtD,UAAU,CAACjD,MAAM;AACpDsR,gCAAuB;AACvB;;AAGF,iBAAW5L,mBAAmBsM,2BAA2B;AACvD,aAAItB,aAAS,OAAT,SAAAA,UAAYhL,eAAH,OAAwB,OAAO;AAC1C;;AAGF,cAAMyM,QAAQ5L,oBAAoBpF,QAAQuE,eAA5B;AACd,cAAMC,sBAAsBsL,wBAAwBkB,KAAD;AAEnD,YAAI,CAACxM,qBAAqB;AACxB;;AAGF,cAAM;UAACG;UAAWC;YAASN,2BACzBC,iBACAC,qBACA3F,MACA4F,cACAI,SALmD;AAQrD,mBAAWmB,QAAQ,CAAC,KAAK,GAAN,GAAqB;AACtC,cAAI,CAAC+J,aAAa/J,IAAD,EAAOrB,UAAUqB,IAAD,CAA5B,GAAkD;AACrDpB,kBAAMoB,IAAD,IAAS;AACdrB,sBAAUqB,IAAD,IAAS;;;AAItB,YAAIpB,MAAMvG,IAAI,KAAKuG,MAAMrG,IAAI,GAAG;AAC9B4R,kCAAuB;AAEvBO,6BAAmBzG,UAAU1F;AAC7B2L,gCAAsBS,YAAYjB,QAAb;AAErBW,sBAAYpG,UAAUrF;AACtB2L,0BAAgBtG,UAAUtF;AAE1B;;;AAIJ0L,kBAAYpG,UAAU;QAAC5L,GAAG;QAAGE,GAAG;;AAChCgS,sBAAgBtG,UAAU;QAAC5L,GAAG;QAAGE,GAAG;;AACpC4R,8BAAuB;;;IAGzB;MACE1L;MACAkM;MACApB;MACAY;MACAV;MACAC;;MAEAuB,KAAKC,UAAUrS,IAAf;;MAEAoS,KAAKC,UAAUnB,YAAf;MACAG;MACA9K;MACAyL;MACAf;;MAEAmB,KAAKC,UAAUrM,SAAf;IAhBF;EApDO;AAuEV;AAOD,IAAMsM,sBAAoC;EACxC9S,GAAG;IAAC,CAACgF,UAAUyB,QAAX,GAAsB;IAAO,CAACzB,UAAU4B,OAAX,GAAqB;;EACtD1G,GAAG;IAAC,CAAC8E,UAAUyB,QAAX,GAAsB;IAAO,CAACzB,UAAU4B,OAAX,GAAqB;;AAFd;AAK1C,SAAS+K,gBAAT,OAAA;MAAyB;IACvBzI;IACA0I;;AAKA,QAAMmB,gBAAgBC,YAAY9J,KAAD;AAEjC,SAAO+J,YACJC,oBAAD;AACE,QAAItB,YAAY,CAACmB,iBAAiB,CAACG,gBAAgB;AAEjD,aAAOJ;;AAGT,UAAMxM,YAAY;MAChBtG,GAAG0G,KAAKyM,KAAKjK,MAAMlJ,IAAI+S,cAAc/S,CAAlC;MACHE,GAAGwG,KAAKyM,KAAKjK,MAAMhJ,IAAI6S,cAAc7S,CAAlC;;AAIL,WAAO;MACLF,GAAG;QACD,CAACgF,UAAUyB,QAAX,GACEyM,eAAelT,EAAEgF,UAAUyB,QAA3B,KAAwCH,UAAUtG,MAAM;QAC1D,CAACgF,UAAU4B,OAAX,GACEsM,eAAelT,EAAEgF,UAAU4B,OAA3B,KAAuCN,UAAUtG,MAAM;;MAE3DE,GAAG;QACD,CAAC8E,UAAUyB,QAAX,GACEyM,eAAehT,EAAE8E,UAAUyB,QAA3B,KAAwCH,UAAUpG,MAAM;QAC1D,CAAC8E,UAAU4B,OAAX,GACEsM,eAAehT,EAAE8E,UAAU4B,OAA3B,KAAuCN,UAAUpG,MAAM;;;KAI/D,CAAC0R,UAAU1I,OAAO6J,aAAlB,CA5BgB;AA8BnB;SCjOeK,cACdC,gBACAC,IAAAA;AAEA,QAAMC,gBAAgBD,MAAM,OAAOD,eAAepL,IAAIqL,EAAnB,IAAyB3I;AAC5D,QAAMhI,OAAO4Q,gBAAgBA,cAAc5Q,KAAKiJ,UAAU;AAE1D,SAAOqH,YACJO,gBAAD;;AACE,QAAIF,MAAM,MAAM;AACd,aAAO;;AAMT,YAAA,OAAO3Q,QAAP,OAAOA,OAAQ6Q,eAAf,OAAA,OAA6B;KAE/B,CAAC7Q,MAAM2Q,EAAP,CAXgB;AAanB;SCjBeG,qBACdC,SACAC,qBAAAA;AAKA,aAAOxB,uBACL,MACEuB,QAAQhT,OAA2B,CAACkT,aAAaC,WAAd;AACjC,UAAM;MAACA,QAAQC;QAAUD;AAEzB,UAAME,mBAAmBD,OAAOnG,WAAWqG,IAAKlG,gBAAe;MAC7DlF,WAAWkF,UAAUlF;MACrBC,SAAS8K,oBAAoB7F,UAAUjF,SAASgL,MAApB;MAFL;AAKzB,WAAO,CAAC,GAAGD,aAAa,GAAGG,gBAApB;KACN,CAAA,CATH,GAUF,CAACL,SAASC,mBAAV,CAZY;AAcf;IChBWM;CAAZ,SAAYA,oBAAAA;AACVA,EAAAA,mBAAAA,mBAAAA,QAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,mBAAAA,mBAAAA,gBAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,mBAAAA,mBAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACD,GAJWA,sBAAAA,oBAAiB,CAAA,EAA7B;AAMA,IAAYC;CAAZ,SAAYA,qBAAAA;AACVA,EAAAA,oBAAAA,WAAAA,IAAA;AACD,GAFWA,uBAAAA,qBAAkB,CAAA,EAA9B;AAYA,IAAMC,eAAwB,oBAAIC,IAAJ;AAE9B,SAAgBC,sBACdC,YAAAA,MAAAA;MACA;IAACC;IAAUC;IAAcC;;AAEzB,QAAM,CAACC,OAAOC,QAAR,QAAoBC,wBAAoC,IAA5B;AAClC,QAAM;IAACC;IAAWzN;IAAS0N;MAAYL;AACvC,QAAMM,oBAAgB9C,sBAAOqC,UAAD;AAC5B,QAAM1C,WAAWoD,WAAU;AAC3B,QAAMC,cAAcC,eAAetD,QAAD;AAClC,QAAMuD,iCAA6B5C,2BACjC,SAAC6C,MAAD;QAACA,SAAAA,QAAAA;AAAAA,MAAAA,OAA0B,CAAA;;AACzB,QAAIH,YAAYrJ,SAAS;AACvB;;AAGF+I,aAAUxR,WAAD;AACP,UAAIA,UAAU,MAAM;AAClB,eAAOiS;;AAGT,aAAOjS,MAAMkS,OAAOD,KAAIE,OAAQhC,QAAO,CAACnQ,MAAMS,SAAS0P,EAAf,CAApB,CAAb;KALD;KAQV,CAAC2B,WAAD,CAd4C;AAgB9C,QAAMxG,gBAAYwD,sBAA8B,IAAxB;AACxB,QAAMsD,iBAAiBtC,YACpBuC,mBAAD;AACE,QAAI5D,YAAY,CAAC2C,UAAU;AACzB,aAAOJ;;AAGT,QACE,CAACqB,iBACDA,kBAAkBrB,gBAClBY,cAAcnJ,YAAY0I,cAC1BI,SAAS,MACT;AACA,YAAMV,MAAe,oBAAII,IAAJ;AAErB,eAASqB,aAAanB,YAAY;AAChC,YAAI,CAACmB,WAAW;AACd;;AAGF,YACEf,SACAA,MAAMjR,SAAS,KACf,CAACiR,MAAM9Q,SAAS6R,UAAUnC,EAAzB,KACDmC,UAAUjV,KAAKoL,SACf;AAEAoI,cAAI0B,IAAID,UAAUnC,IAAImC,UAAUjV,KAAKoL,OAArC;AACA;;AAGF,cAAMjJ,OAAO8S,UAAU9S,KAAKiJ;AAC5B,cAAMpL,OAAOmC,OAAO,IAAI6E,KAAKJ,QAAQzE,IAAD,GAAQA,IAAxB,IAAgC;AAEpD8S,kBAAUjV,KAAKoL,UAAUpL;AAEzB,YAAIA,MAAM;AACRwT,cAAI0B,IAAID,UAAUnC,IAAI9S,IAAtB;;;AAIJ,aAAOwT;;AAGT,WAAOwB;KAET,CAAClB,YAAYI,OAAOH,UAAU3C,UAAUxK,OAAxC,CA7CgC;AAgDlCsL,+BAAU,MAAA;AACRqC,kBAAcnJ,UAAU0I;KACvB,CAACA,UAAD,CAFM;AAIT5B;IACE,MAAA;AACE,UAAId,UAAU;AACZ;;AAGFuD,iCAA0B;;;IAG5B,CAACZ,UAAU3C,QAAX;EATO;AAYTc;IACE,MAAA;AACE,UAAIgC,SAASA,MAAMjR,SAAS,GAAG;AAC7BkR,iBAAS,IAAD;;;;IAIZ,CAAC/B,KAAKC,UAAU6B,KAAf,CAAD;EAPO;AAUThC;IACE,MAAA;AACE,UACEd,YACA,OAAOiD,cAAc,YACrBpG,UAAU7C,YAAY,MACtB;AACA;;AAGF6C,gBAAU7C,UAAUJ,WAAW,MAAA;AAC7B2J,mCAA0B;AAC1B1G,kBAAU7C,UAAU;SACnBiJ,SAH2B;;;IAMhC,CAACA,WAAWjD,UAAUuD,4BAA4B,GAAGX,YAArD;EAhBO;AAmBT,SAAO;IACLe;IACAJ;IACAQ,oBAAoBjB,SAAS;;AAG/B,WAASM,aAAT;AACE,YAAQF,UAAR;MACE,KAAKb,kBAAkB2B;AACrB,eAAO;MACT,KAAK3B,kBAAkB4B;AACrB,eAAOtB;MACT;AACE,eAAO,CAACA;;;AAGf;SCpKeuB,gBAId3S,OACA4S,WAAAA;AAEA,SAAO9C,YACJuC,mBAAD;AACE,QAAI,CAACrS,OAAO;AACV,aAAO;;AAGT,QAAIqS,eAAe;AACjB,aAAOA;;AAGT,WAAO,OAAOO,cAAc,aAAaA,UAAU5S,KAAD,IAAUA;KAE9D,CAAC4S,WAAW5S,KAAZ,CAZgB;AAcnB;SCtBe6S,eACdrT,MACAyE,SAAAA;AAEA,SAAO0O,gBAAgBnT,MAAMyE,OAAP;AACvB;ACID,SAAgB6O,oBAAAA,MAAAA;MAAoB;IAACC;IAAUtE;;AAC7C,QAAMuE,kBAAkBC,SAASF,QAAD;AAChC,QAAMG,uBAAmBlE,uBAAQ,MAAA;AAC/B,QACEP,YACA,OAAOpN,WAAW,eAClB,OAAOA,OAAO8R,qBAAqB,aACnC;AACA,aAAO3L;;AAGT,UAAM;MAAC2L;QAAoB9R;AAE3B,WAAO,IAAI8R,iBAAiBH,eAArB;KACN,CAACA,iBAAiBvE,QAAlB,CAZ6B;AAchCc,+BAAU,MAAA;AACR,WAAO,MAAM2D,oBAAN,OAAA,SAAMA,iBAAkBE,WAAlB;KACZ,CAACF,gBAAD,CAFM;AAIT,SAAOA;AACR;ACrBD,SAAgBG,kBAAAA,MAAAA;MAAkB;IAACN;IAAUtE;;AAC3C,QAAM6E,eAAeL,SAASF,QAAD;AAC7B,QAAMQ,qBAAiBvE;IACrB,MAAA;AACE,UACEP,YACA,OAAOpN,WAAW,eAClB,OAAOA,OAAOmS,mBAAmB,aACjC;AACA,eAAOhM;;AAGT,YAAM;QAACgM;UAAkBnS;AAEzB,aAAO,IAAImS,eAAeF,YAAnB;;;IAGT,CAAC7E,QAAD;EAf4B;AAkB9Bc,+BAAU,MAAA;AACR,WAAO,MAAMgE,kBAAN,OAAA,SAAMA,eAAgBH,WAAhB;KACZ,CAACG,cAAD,CAFM;AAIT,SAAOA;AACR;AC5BD,SAASE,eAAe3U,SAAxB;AACE,SAAO,IAAIuF,KAAKxF,cAAcC,OAAD,GAAWA,OAAjC;AACR;AAED,SAAgB4U,QACd5U,SACAmF,SACA0P,cAAAA;MADA1P,YAAAA,QAAAA;AAAAA,cAAgDwP;;AAGhD,QAAM,CAACpW,MAAMuW,OAAP,QAAkBnC,wBAA4B,IAApB;AAEhC,WAASoC,cAAT;AACED,YAASE,iBAAD;AACN,UAAI,CAAChV,SAAS;AACZ,eAAO;;AAGT,UAAIA,QAAQiV,gBAAgB,OAAO;AAAA,YAAA;AAGjC,gBAAA,OAAOD,eAAP,OAAOA,cAAeH,iBAAtB,OAAA,OAAsC;;AAGxC,YAAMK,UAAU/P,QAAQnF,OAAD;AAEvB,UAAI2Q,KAAKC,UAAUoE,WAAf,MAAgCrE,KAAKC,UAAUsE,OAAf,GAAyB;AAC3D,eAAOF;;AAGT,aAAOE;KAjBF;;AAqBT,QAAMd,mBAAmBJ,oBAAoB;IAC3CC,SAASkB,SAAD;AACN,UAAI,CAACnV,SAAS;AACZ;;AAGF,iBAAWoV,UAAUD,SAAS;AAC5B,cAAM;UAACE;UAAMhP;YAAU+O;AAEvB,YACEC,SAAS,eACThP,kBAAkBiP,eAClBjP,OAAOkP,SAASvV,OAAhB,GACA;AACA+U,sBAAW;AACX;;;;GAfoC;AAoB5C,QAAMN,iBAAiBF,kBAAkB;IAACN,UAAUc;GAAZ;AAExCS,4BAA0B,MAAA;AACxBT,gBAAW;AAEX,QAAI/U,SAAS;AACXyU,wBAAc,OAAd,SAAAA,eAAgBgB,QAAQzV,OAAxB;AACAoU,0BAAgB,OAAhB,SAAAA,iBAAkBqB,QAAQxS,SAASyS,MAAM;QACvCC,WAAW;QACXC,SAAS;OAFX;WAIK;AACLnB,wBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AACAF,0BAAgB,OAAhB,SAAAA,iBAAkBE,WAAlB;;KAED,CAACtU,OAAD,CAbsB;AAezB,SAAOzB;AACR;SC3EesX,aAAatX,MAAAA;AAC3B,QAAMuX,cAAcjC,gBAAgBtV,IAAD;AAEnC,SAAOT,aAAaS,MAAMuX,WAAP;AACpB;ACJD,IAAM5D,iBAA0B,CAAA;AAEhC,SAAgB6D,uBAAuBrV,MAAAA;AACrC,QAAMsV,mBAAehG,sBAAOtP,IAAD;AAE3B,QAAMuV,YAAYjF,YACfuC,mBAAD;AACE,QAAI,CAAC7S,MAAM;AACT,aAAOwR;;AAGT,QACEqB,iBACAA,kBAAkBrB,kBAClBxR,QACAsV,aAAarM,WACbjJ,KAAKqB,eAAeiU,aAAarM,QAAQ5H,YACzC;AACA,aAAOwR;;AAGT,WAAOnS,uBAAuBV,IAAD;KAE/B,CAACA,IAAD,CAlB2B;AAqB7B+P,+BAAU,MAAA;AACRuF,iBAAarM,UAAUjJ;KACtB,CAACA,IAAD,CAFM;AAIT,SAAOuV;AACR;SCvBeC,iBAAiBC,UAAAA;AAC/B,QAAM,CACJC,mBACAC,oBAFI,QAGF1D,wBAAmC,IAA3B;AACZ,QAAM2D,mBAAetG,sBAAOmG,QAAD;AAG3B,QAAMI,mBAAejG,2BAAa9I,WAAD;AAC/B,UAAM9F,mBAAmBQ,qBAAqBsF,MAAMnB,MAAP;AAE7C,QAAI,CAAC3E,kBAAkB;AACrB;;AAGF2U,yBAAsBD,CAAAA,uBAAD;AACnB,UAAI,CAACA,oBAAmB;AACtB,eAAO;;AAGTA,MAAAA,mBAAkB3C,IAChB/R,kBACAoB,qBAAqBpB,gBAAD,CAFtB;AAKA,aAAO,IAAIyQ,IAAIiE,kBAAR;KAVW;KAYnB,CAAA,CAnB6B;AAqBhC3F,+BAAU,MAAA;AACR,UAAM+F,mBAAmBF,aAAa3M;AAEtC,QAAIwM,aAAaK,kBAAkB;AACjCC,cAAQD,gBAAD;AAEP,YAAME,UAAUP,SACbpE,IAAK/R,aAAD;AACH,cAAM2W,oBAAoBzU,qBAAqBlC,OAAD;AAE9C,YAAI2W,mBAAmB;AACrBA,4BAAkB9P,iBAAiB,UAAU0P,cAAc;YACzDtJ,SAAS;WADX;AAIA,iBAAO,CACL0J,mBACA7T,qBAAqB6T,iBAAD,CAFf;;AAMT,eAAO;OAfK,EAiBbtD,OAEGuD,WAIGA,SAAS,IAvBF;AA0BhBP,2BAAqBK,QAAQlV,SAAS,IAAI2Q,IAAIuE,OAAR,IAAmB,IAArC;AAEpBJ,mBAAa3M,UAAUwM;;AAGzB,WAAO,MAAA;AACLM,cAAQN,QAAD;AACPM,cAAQD,gBAAD;;AAGT,aAASC,QAAQN,WAAjB;AACEA,MAAAA,UAAS3P,QAASxG,aAAD;AACf,cAAM2W,oBAAoBzU,qBAAqBlC,OAAD;AAE9C2W,6BAAiB,OAAjB,SAAAA,kBAAmBjQ,oBAAoB,UAAU6P,YAAjD;OAHF;;KAMD,CAACA,cAAcJ,QAAf,CAjDM;AAmDT,aAAOjG,uBAAQ,MAAA;AACb,QAAIiG,SAAS3U,QAAQ;AACnB,aAAO4U,oBACHS,MAAMC,KAAKV,kBAAkBW,OAAlB,CAAX,EAAuCtY,OACrC,CAACC,KAAK2M,gBAAgBtG,IAAIrG,KAAK2M,WAAN,GACzBlN,kBAFF,IAIA0G,iBAAiBsR,QAAD;;AAGtB,WAAOhY;KACN,CAACgY,UAAUC,iBAAX,CAXW;AAYf;SCpGeY,sBACdvR,eACA8M,cAAAA;MAAAA,iBAAAA,QAAAA;AAAAA,mBAAsB,CAAA;;AAEtB,QAAM0E,2BAAuBjH,sBAA2B,IAArB;AAEnCS;IACE,MAAA;AACEwG,2BAAqBtN,UAAU;;;IAGjC4I;EALO;AAQT9B,+BAAU,MAAA;AACR,UAAMyG,mBAAmBzR,kBAAkBtH;AAE3C,QAAI+Y,oBAAoB,CAACD,qBAAqBtN,SAAS;AACrDsN,2BAAqBtN,UAAUlE;;AAGjC,QAAI,CAACyR,oBAAoBD,qBAAqBtN,SAAS;AACrDsN,2BAAqBtN,UAAU;;KAEhC,CAAClE,aAAD,CAVM;AAYT,SAAOwR,qBAAqBtN,UACxBwN,SAAS1R,eAAewR,qBAAqBtN,OAArC,IACRxL;AACL;SC7BeiZ,eAAe3F,SAAAA;AAC7BhB;IACE,MAAA;AACE,UAAI,CAACtO,WAAW;AACd;;AAGF,YAAMkV,cAAc5F,QAAQM,IAAI,UAAA;AAAA,YAAC;UAACH;YAAF;AAAA,eAAcA,OAAOnD,SAArB,OAAA,SAAcmD,OAAOnD,MAAP;OAA1B;AAEpB,aAAO,MAAA;AACL,mBAAWE,YAAY0I,aAAa;AAClC1I,sBAAQ,OAAR,SAAAA,SAAQ;;;;;;IAMd8C,QAAQM,IAAI,WAAA;AAAA,UAAC;QAACH;UAAF;AAAA,aAAcA;KAA1B;EAhBO;AAkBV;SCXe0F,sBACdhR,WACA+K,IAAAA;AAEA,aAAOnB,uBAAQ,MAAA;AACb,WAAO5J,UAAU7H,OACf,CAACC,KAAD,SAAA;UAAM;QAACiI;QAAWC;;AAChBlI,UAAIiI,SAAD,IAAea,WAAD;AACfZ,gBAAQY,OAAO6J,EAAR;;AAGT,aAAO3S;OAET,CAAA,CARK;KAUN,CAAC4H,WAAW+K,EAAZ,CAXW;AAYf;SCzBekG,cAAcvX,SAAAA;AAC5B,aAAOkQ,uBAAQ,MAAOlQ,UAAUM,oBAAoBN,OAAD,IAAY,MAAO,CACpEA,OADoE,CAAxD;AAGf;ACED,IAAMkS,iBAAuB,CAAA;AAE7B,SAAgBsF,SACdrB,UACAhR,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAA4CpF;;AAE5C,QAAM,CAAC0X,YAAD,IAAiBtB;AACvB,QAAMuB,aAAaH,cACjBE,eAAetX,UAAUsX,YAAD,IAAiB,IADX;AAGhC,QAAM,CAACE,OAAOC,QAAR,QAAoBjF,wBAAuBT,cAAf;AAElC,WAAS2F,eAAT;AACED,aAAS,MAAA;AACP,UAAI,CAACzB,SAAS3U,QAAQ;AACpB,eAAO0Q;;AAGT,aAAOiE,SAASpE,IAAK/R,aACnBgD,2BAA2BhD,OAAD,IACrB0X,aACD,IAAInS,KAAKJ,QAAQnF,OAAD,GAAWA,OAA3B,CAHC;KALD;;AAaV,QAAMyU,iBAAiBF,kBAAkB;IAACN,UAAU4D;GAAZ;AAExCrC,4BAA0B,MAAA;AACxBf,sBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AACAuD,iBAAY;AACZ1B,aAAS3P,QAASxG,aAAYyU,kBAAb,OAAA,SAAaA,eAAgBgB,QAAQzV,OAAxB,CAA9B;KACC,CAACmW,QAAD,CAJsB;AAMzB,SAAOwB;AACR;SC3CeG,kBACdpX,MAAAA;AAEA,MAAI,CAACA,MAAM;AACT,WAAO;;AAGT,MAAIA,KAAKqX,SAASvW,SAAS,GAAG;AAC5B,WAAOd;;AAET,QAAMsX,aAAatX,KAAKqX,SAAS,CAAd;AAEnB,SAAOlW,cAAcmW,UAAD,IAAeA,aAAatX;AACjD;SCHeuX,wBAAAA,MAAAA;MAAwB;IACtC9S;;AAEA,QAAM,CAAC5G,MAAMuW,OAAP,QAAkBnC,wBAA4B,IAApB;AAChC,QAAM6B,mBAAelE,2BAClBoG,aAAD;AACE,eAAW;MAACrQ;SAAWqQ,SAAS;AAC9B,UAAI7U,cAAcwE,MAAD,GAAU;AACzByO,gBAASvW,CAAAA,UAAD;AACN,gBAAM2W,UAAU/P,QAAQkB,MAAD;AAEvB,iBAAO9H,QACH;YAAC,GAAGA;YAAMZ,OAAOuX,QAAQvX;YAAOE,QAAQqX,QAAQrX;cAChDqX;SALC;AAOP;;;KAIN,CAAC/P,OAAD,CAf8B;AAiBhC,QAAMsP,iBAAiBF,kBAAkB;IAACN,UAAUO;GAAZ;AACxC,QAAM0D,uBAAmB5H,2BACtBtQ,aAAD;AACE,UAAMU,OAAOoX,kBAAkB9X,OAAD;AAE9ByU,sBAAc,OAAd,SAAAA,eAAgBH,WAAhB;AAEA,QAAI5T,MAAM;AACR+T,wBAAc,OAAd,SAAAA,eAAgBgB,QAAQ/U,IAAxB;;AAGFoU,YAAQpU,OAAOyE,QAAQzE,IAAD,IAAS,IAAxB;KAET,CAACyE,SAASsP,cAAV,CAZkC;AAcpC,QAAM,CAAC0D,SAASC,MAAV,IAAoBC,WAAWH,gBAAD;AAEpC,aAAOhI,uBACL,OAAO;IACLiI;IACA5Z;IACA6Z;MAEF,CAAC7Z,MAAM4Z,SAASC,MAAhB,CANY;AAQf;AC9CM,IAAME,iBAAiB,CAC5B;EAAC1G,QAAQ1D;EAAejO,SAAS,CAAA;AAAjC,GACA;EAAC2R,QAAQjJ;EAAgB1I,SAAS,CAAA;AAAlC,CAF4B;AAKvB,IAAMsY,cAAuB;EAAC5O,SAAS,CAAA;AAAV;AAE7B,IAAM6O,gCAAsE;EACjFC,WAAW;IACTtT,SAAS9E;;EAEXqY,WAAW;IACTvT,SAAS9E;IACTwS,UAAUb,kBAAkB2G;IAC5B/F,WAAWX,mBAAmB2G;;EAEhCC,aAAa;IACX1T,SAASpF;;AAVsE;ICdtE+Y,uCAA+B3G,IAAAA;EAI1CnM,IAAIqL,IAAD;;AACD,WAAOA,MAAM,QAAN,aAAa,MAAMrL,IAAIqL,EAAV,MAAb,OAAA,aAA8B3I,SAAYA;;EAGnDqQ,UAAO;AACL,WAAOlC,MAAMC,KAAK,KAAKC,OAAL,CAAX;;EAGTiC,aAAU;AACR,WAAO,KAAKD,QAAL,EAAe1F,OAAO,UAAA;AAAA,UAAC;QAAC1D;UAAF;AAAA,aAAgB,CAACA;KAAvC;;EAGTsJ,WAAW5H,IAAD;;AACR,YAAA,yBAAA,YAAO,KAAKrL,IAAIqL,EAAT,MAAP,OAAA,SAAO,UAAc3Q,KAAKiJ,YAA1B,OAAA,wBAAqCjB;;;ACflC,IAAMwQ,uBAAgD;EAC3DC,gBAAgB;EAChBtP,QAAQ;EACRJ,YAAY;EACZ2P,gBAAgB;EAChBC,YAAY;EACZC,mBAAmB;EACnBlI,gBAAgB,oBAAIe,IAAJ;EAChBmB,gBAAgB,oBAAInB,IAAJ;EAChBoH,qBAAqB,IAAIT,uBAAJ;EACrBU,MAAM;EACNX,aAAa;IACXV,SAAS;MACPxO,SAAS;;IAEXpL,MAAM;IACN6Z,QAAQ1J;;EAEV5J,qBAAqB,CAAA;EACrB0K,yBAAyB,CAAA;EACzBiK,wBAAwBjB;EACxBtF,4BAA4BxE;EAC5BgJ,YAAY;EACZhE,oBAAoB;AAvBuC;AA0BtD,IAAMgG,yBAAoD;EAC/DP,gBAAgB;EAChBzN,YAAY,CAAA;EACZ7B,QAAQ;EACRuP,gBAAgB;EAChBO,mBAAmB;IACjBlB,WAAW;;EAEbmB,UAAUlL;EACV0C,gBAAgB,oBAAIe,IAAJ;EAChBqH,MAAM;EACNtG,4BAA4BxE;AAXmC;AAc1D,IAAMmL,sBAAkBC,6BAC7BJ,sBAD0C;AAIrC,IAAMK,oBAAgBD,6BAC3BZ,oBADwC;SC/C1Bc,kBAAAA;AACd,SAAO;IACLvB,WAAW;MACT5O,QAAQ;MACR0C,oBAAoB;QAACxO,GAAG;QAAGE,GAAG;;MAC9Bgc,OAAO,oBAAI9H,IAAJ;MACP+H,WAAW;QAACnc,GAAG;QAAGE,GAAG;;;IAEvBya,WAAW;MACTrG,YAAY,IAAIyG,uBAAJ;;;AAGjB;AAED,SAAgBqB,QAAQC,OAAcC,QAAAA;AACpC,UAAQA,OAAOhF,MAAf;IACE,KAAKiF,OAAOpN;AACV,aAAO;QACL,GAAGkN;QACH3B,WAAW;UACT,GAAG2B,MAAM3B;UACTlM,oBAAoB8N,OAAO9N;UAC3B1C,QAAQwQ,OAAOxQ;;;IAGrB,KAAKyQ,OAAOC;AACV,UAAIH,MAAM3B,UAAU5O,UAAU,MAAM;AAClC,eAAOuQ;;AAGT,aAAO;QACL,GAAGA;QACH3B,WAAW;UACT,GAAG2B,MAAM3B;UACTyB,WAAW;YACTnc,GAAGsc,OAAOhP,YAAYtN,IAAIqc,MAAM3B,UAAUlM,mBAAmBxO;YAC7DE,GAAGoc,OAAOhP,YAAYpN,IAAImc,MAAM3B,UAAUlM,mBAAmBtO;;;;IAIrE,KAAKqc,OAAOE;IACZ,KAAKF,OAAOG;AACV,aAAO;QACL,GAAGL;QACH3B,WAAW;UACT,GAAG2B,MAAM3B;UACT5O,QAAQ;UACR0C,oBAAoB;YAACxO,GAAG;YAAGE,GAAG;;UAC9Bic,WAAW;YAACnc,GAAG;YAAGE,GAAG;;;;IAI3B,KAAKqc,OAAOI,mBAAmB;AAC7B,YAAM;QAAC1a;UAAWqa;AAClB,YAAM;QAAChJ;UAAMrR;AACb,YAAMqS,aAAa,IAAIyG,uBAAuBsB,MAAM1B,UAAUrG,UAA3C;AACnBA,iBAAWoB,IAAIpC,IAAIrR,OAAnB;AAEA,aAAO;QACL,GAAGoa;QACH1B,WAAW;UACT,GAAG0B,MAAM1B;UACTrG;;;;IAKN,KAAKiI,OAAOK,sBAAsB;AAChC,YAAM;QAACtJ;QAAIxL,KAAAA;QAAK8J;UAAY0K;AAC5B,YAAMra,UAAUoa,MAAM1B,UAAUrG,WAAWrM,IAAIqL,EAA/B;AAEhB,UAAI,CAACrR,WAAW6F,SAAQ7F,QAAQ6F,KAAK;AACnC,eAAOuU;;AAGT,YAAM/H,aAAa,IAAIyG,uBAAuBsB,MAAM1B,UAAUrG,UAA3C;AACnBA,iBAAWoB,IAAIpC,IAAI;QACjB,GAAGrR;QACH2P;OAFF;AAKA,aAAO;QACL,GAAGyK;QACH1B,WAAW;UACT,GAAG0B,MAAM1B;UACTrG;;;;IAKN,KAAKiI,OAAOM,qBAAqB;AAC/B,YAAM;QAACvJ;QAAIxL,KAAAA;UAAOwU;AAClB,YAAMra,UAAUoa,MAAM1B,UAAUrG,WAAWrM,IAAIqL,EAA/B;AAEhB,UAAI,CAACrR,WAAW6F,SAAQ7F,QAAQ6F,KAAK;AACnC,eAAOuU;;AAGT,YAAM/H,aAAa,IAAIyG,uBAAuBsB,MAAM1B,UAAUrG,UAA3C;AACnBA,iBAAWwI,OAAOxJ,EAAlB;AAEA,aAAO;QACL,GAAG+I;QACH1B,WAAW;UACT,GAAG0B,MAAM1B;UACTrG;;;;IAKN,SAAS;AACP,aAAO+H;;;AAGZ;SCzGeU,aAAAA,MAAAA;MAAa;IAACnL;;AAC5B,QAAM;IAAC9F;IAAQsP;IAAgB/H;UAAkB2J,0BAAWlB,eAAD;AAC3D,QAAMmB,yBAAyBjK,YAAYoI,cAAD;AAC1C,QAAM8B,mBAAmBlK,YAAYlH,UAAD,OAAA,SAACA,OAAQwH,EAAT;AAGpCZ,+BAAU,MAAA;AACR,QAAId,UAAU;AACZ;;AAGF,QAAI,CAACwJ,kBAAkB6B,0BAA0BC,oBAAoB,MAAM;AACzE,UAAI,CAACrR,gBAAgBoR,sBAAD,GAA0B;AAC5C;;AAGF,UAAI/X,SAASiY,kBAAkBF,uBAAuB3U,QAAQ;AAE5D;;AAGF,YAAMiL,gBAAgBF,eAAepL,IAAIiV,gBAAnB;AAEtB,UAAI,CAAC3J,eAAe;AAClB;;AAGF,YAAM;QAACxF;QAAepL;UAAQ4Q;AAE9B,UAAI,CAACxF,cAAcnC,WAAW,CAACjJ,KAAKiJ,SAAS;AAC3C;;AAGFwR,4BAAsB,MAAA;AACpB,mBAAWnb,WAAW,CAAC8L,cAAcnC,SAASjJ,KAAKiJ,OAA7B,GAAuC;AAC3D,cAAI,CAAC3J,SAAS;AACZ;;AAGF,gBAAMob,gBAAgBC,uBAAuBrb,OAAD;AAE5C,cAAIob,eAAe;AACjBA,0BAAcE,MAAd;AACA;;;OAVe;;KAetB,CACDnC,gBACAxJ,UACAyB,gBACA6J,kBACAD,sBALC,CA1CM;AAkDT,SAAO;AACR;SClEeO,eACdC,WAAAA,MAAAA;MACA;IAACje;IAAW,GAAGke;;AAEf,SAAOD,aAAS,QAATA,UAAWha,SACdga,UAAU/c,OAAkB,CAACkT,aAAatT,aAAd;AAC1B,WAAOA,SAAS;MACdd,WAAWoU;MACX,GAAG8J;KAFU;KAIdle,SALH,IAMAA;AACL;SCVeme,0BACdlJ,QAAAA;AAEA,aAAOtC;IACL,OAAO;MACLuI,WAAW;QACT,GAAGD,8BAA8BC;QACjC,GAAGjG,UAAH,OAAA,SAAGA,OAAQiG;;MAEbC,WAAW;QACT,GAAGF,8BAA8BE;QACjC,GAAGlG,UAAH,OAAA,SAAGA,OAAQkG;;MAEbG,aAAa;QACX,GAAGL,8BAA8BK;QACjC,GAAGrG,UAAH,OAAA,SAAGA,OAAQqG;;;;IAIf,CAACrG,UAAD,OAAA,SAACA,OAAQiG,WAAWjG,UAApB,OAAA,SAAoBA,OAAQkG,WAAWlG,UAAvC,OAAA,SAAuCA,OAAQqG,WAA/C;EAhBY;AAkBf;SCXe8C,iCAAAA,MAAAA;MAAiC;IAC/ClS;IACAtE;IACA2Q;IACAtD,SAAS;;AAET,QAAMoJ,kBAAc5L,sBAAO,KAAD;AAC1B,QAAM;IAACjS,GAAAA;IAAGE,GAAAA;MAAK,OAAOuU,WAAW,YAAY;IAACzU,GAAGyU;IAAQvU,GAAGuU;MAAUA;AAEtEgD,4BAA0B,MAAA;AACxB,UAAM7F,WAAW,CAAC5R,MAAK,CAACE;AAExB,QAAI0R,YAAY,CAAClG,YAAY;AAC3BmS,kBAAYjS,UAAU;AACtB;;AAGF,QAAIiS,YAAYjS,WAAW,CAACmM,aAAa;AAGvC;;AAIF,UAAMpV,OAAO+I,cAAH,OAAA,SAAGA,WAAY/I,KAAKiJ;AAE9B,QAAI,CAACjJ,QAAQA,KAAKuU,gBAAgB,OAAO;AAGvC;;AAGF,UAAM1W,OAAO4G,QAAQzE,IAAD;AACpB,UAAMmb,YAAY/d,aAAaS,MAAMuX,WAAP;AAE9B,QAAI,CAAC/X,IAAG;AACN8d,gBAAU9d,IAAI;;AAGhB,QAAI,CAACE,IAAG;AACN4d,gBAAU5d,IAAI;;AAIhB2d,gBAAYjS,UAAU;AAEtB,QAAIlF,KAAKC,IAAImX,UAAU9d,CAAnB,IAAwB,KAAK0G,KAAKC,IAAImX,UAAU5d,CAAnB,IAAwB,GAAG;AAC1D,YAAMgE,0BAA0BD,2BAA2BtB,IAAD;AAE1D,UAAIuB,yBAAyB;AAC3BA,gCAAwBiJ,SAAS;UAC/BhN,KAAK2d,UAAU5d;UACfD,MAAM6d,UAAU9d;SAFlB;;;KAMH,CAAC0L,YAAY1L,IAAGE,IAAG6X,aAAa3Q,OAAhC,CA/CsB;AAgD1B;ACoDM,IAAM2W,6BAAyBhC,6BAAyB;EAC7D,GAAG3b;EACHT,QAAQ;EACRE,QAAQ;AAHqD,CAAZ;AAMnD,IAAKme;CAAL,SAAKA,SAAAA;AACHA,EAAAA,QAAAA,QAAAA,eAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,QAAAA,QAAAA,cAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,QAAAA,QAAAA,aAAAA,IAAAA,CAAAA,IAAA;AACD,GAJIA,WAAAA,SAAM,CAAA,EAAX;AAMA,IAAaC,iBAAaC,oBAAK,SAASD,YAAT,MAAA;;MAAoB;IACjD3K;IACA6K;IACA7L,aAAa;IACb0H;IACAtG,UAAU6G;IACV6D,qBAAqBC;IACrBC;IACAb;IACA,GAAG5S;;AAEH,QAAM0T,YAAQC,0BAAWpC,SAASzR,QAAWsR,eAArB;AACxB,QAAM,CAACI,OAAOR,QAAR,IAAoB0C;AAC1B,QAAM,CAACE,sBAAsBC,uBAAvB,IACJC,sBAAqB;AACvB,QAAM,CAACC,QAAQC,SAAT,QAAsBjK,wBAAiBoJ,OAAOc,aAAhB;AACpC,QAAMC,gBAAgBH,WAAWZ,OAAOgB;AACxC,QAAM;IACJtE,WAAW;MAAC5O,QAAQmT;MAAU/C,OAAO7I;MAAgB8I;;IACrDxB,WAAW;MAACrG,YAAYkH;;MACtBa;AACJ,QAAM1Z,OAAOsc,YAAY,OAAO5L,eAAepL,IAAIgX,QAAnB,IAA+B;AAC/D,QAAMC,kBAAcjN,sBAAkC;IACpDkN,SAAS;IACTC,YAAY;GAFY;AAI1B,QAAMtT,aAASqG,uBACb,MAAA;AAAA,QAAA;AAAA,WACE8M,YAAY,OACR;MACE3L,IAAI2L;;MAEJI,OAAI,aAAE1c,QAAF,OAAA,SAAEA,KAAM0c,SAAR,OAAA,aAAgB7E;MACpBha,MAAM0e;QAER;KACN,CAACD,UAAUtc,IAAX,CAVoB;AAYtB,QAAM2c,gBAAYrN,sBAAgC,IAA1B;AACxB,QAAM,CAACsN,cAAcC,eAAf,QAAkC5K,wBAAgC,IAAxB;AAChD,QAAM,CAACwG,gBAAgBqE,iBAAjB,QAAsC7K,wBAAuB,IAAf;AACpD,QAAM8K,cAAcxK,eAAerK,OAAO9C,OAAOiR,OAAOnO,KAAd,CAAR;AAClC,QAAM8U,yBAAyBC,YAAW,kBAAmBtM,EAAnB;AAC1C,QAAMuM,iCAA6B1N,uBACjC,MAAMqJ,oBAAoBP,WAApB,GACN,CAACO,mBAAD,CAFwC;AAI1C,QAAME,yBAAyBiC,0BAA0BW,SAAD;AACxD,QAAM;IAAC/I;IAAgBJ;IAA4BQ;MACjDtB,sBAAsBwL,4BAA4B;IAChDtL,UAAUwK;IACVvK,cAAc,CAAC2H,UAAUnc,GAAGmc,UAAUjc,CAAxB;IACduU,QAAQiH,uBAAuBf;GAHZ;AAKvB,QAAMjP,aAAa0H,cAAcC,gBAAgB4L,QAAjB;AAChC,QAAMa,4BAAwB3N,uBAC5B,MAAOiJ,iBAAiBzM,oBAAoByM,cAAD,IAAmB,MAC9D,CAACA,cAAD,CAFmC;AAIrC,QAAM2E,oBAAoBC,uBAAsB;AAChD,QAAMC,wBAAwBjK,eAC5BtK,YACAgQ,uBAAuBhB,UAAUtT,OAFS;AAK5CwW,mCAAiC;IAC/BlS,YAAYuT,YAAY,OAAO5L,eAAepL,IAAIgX,QAAnB,IAA+B;IAC9DxK,QAAQsL,kBAAkBG;IAC1BnI,aAAakI;IACb7Y,SAASsU,uBAAuBhB,UAAUtT;GAJZ;AAOhC,QAAMiU,iBAAiBxE,QACrBnL,YACAgQ,uBAAuBhB,UAAUtT,SACjC6Y,qBAH4B;AAK9B,QAAM1E,oBAAoB1E,QACxBnL,aAAaA,WAAWyU,gBAAgB,IADT;AAGjC,QAAMC,oBAAgBnO,sBAAsB;IAC1CmJ,gBAAgB;IAChBtP,QAAQ;IACRJ;IACAU,eAAe;IACfkP,YAAY;IACZ/F;IACAlC;IACAgN,cAAc;IACdC,kBAAkB;IAClB9E;IACAC,MAAM;IACN1U,qBAAqB,CAAA;IACrBwZ,yBAAyB;GAbC;AAe5B,QAAMC,WAAWhF,oBAAoBN,YAApB,wBACfkF,cAAcxU,QAAQ6P,SADP,OAAA,SACf,sBAA4BnI,EADb;AAGjB,QAAMwH,cAAcZ,wBAAwB;IAC1C9S,SAASsU,uBAAuBZ,YAAY1T;GADH;AAK3C,QAAMiZ,gBAAY,wBAAGvF,YAAYV,QAAQxO,YAAvB,OAAA,wBAAkCF;AACpD,QAAM4U,mBAAmBvB,iBAAa,oBAClCjE,YAAYta,SADsB,OAAA,oBACd6a,iBACpB;AACJ,QAAMoF,kBAAkBvS,QACtB4M,YAAYV,QAAQxO,WAAWkP,YAAYta,IADd;AAK/B,QAAMkgB,gBAAgB5I,aAAa2I,kBAAkB,OAAOpF,cAA1B;AAGlC,QAAM1B,aAAaH,cACjB6G,eAAeje,UAAUie,YAAD,IAAiB,IADX;AAKhC,QAAMtZ,sBAAsBiR,uBAC1B+G,gBAAgByB,YAAH,OAAGA,WAAY9U,aAAa,IADO;AAGlD,QAAM+F,0BAA0BgI,SAAS1S,mBAAD;AAGxC,QAAM4Z,oBAAoBnD,eAAeC,WAAW;IAClDje,WAAW;MACTQ,GAAGmc,UAAUnc,IAAI0gB,cAAc1gB;MAC/BE,GAAGic,UAAUjc,IAAIwgB,cAAcxgB;MAC/BP,QAAQ;MACRE,QAAQ;;IAEVub;IACAtP;IACAuP;IACAE;IACA+E;IACA7E,MAAM2E,cAAcxU,QAAQ6P;IAC5BmF,iBAAiB9F,YAAYta;IAC7BuG;IACA0K;IACAkI;GAhBsC;AAmBxC,QAAMnI,qBAAqBsO,wBACvB9Y,IAAI8Y,uBAAuB3D,SAAxB,IACH;AAEJ,QAAMzU,gBAAgByQ,iBAAiBpR,mBAAD;AAEtC,QAAM8Z,mBAAmB5H,sBAAsBvR,aAAD;AAE9C,QAAMoZ,wBAAwB7H,sBAAsBvR,eAAe,CACjE2T,cADiE,CAAhB;AAInD,QAAMkF,0BAA0BvZ,IAAI2Z,mBAAmBE,gBAApB;AAEnC,QAAMzU,gBAAgBkU,mBAClBvf,gBAAgBuf,kBAAkBK,iBAAnB,IACf;AAEJ,QAAMrF,aACJxP,UAAUM,gBACNgS,mBAAmB;IACjBtS;IACAM;IACAmJ;IACAiG,qBAAqBqE;IACrBrO;GALgB,IAOlB;AACN,QAAMuP,SAASC,kBAAkB1F,YAAY,IAAb;AAChC,QAAM,CAACG,MAAMwF,OAAP,QAAkBrM,wBAAsB,IAAd;AAIhC,QAAMsM,mBAAmBT,kBACrBE,oBACA3Z,IAAI2Z,mBAAmBG,qBAApB;AAEP,QAAMthB,YAAYD,YAChB2hB,mBAD2B,aAE3BzF,QAF2B,OAAA,SAE3BA,KAAMjb,SAFqB,OAAA,aAEb,MACd6a,cAH2B;AAM7B,QAAM8F,sBAAkBlP,sBAA8B,IAAxB;AAC9B,QAAMmP,wBAAoB7O;IACxB,CACE9I,OADF,UAAA;UAEE;QAACoK,QAAQC;QAAQ5R;;AAEjB,UAAIod,UAAU1T,WAAW,MAAM;AAC7B;;AAGF,YAAMF,cAAa2H,eAAepL,IAAIqX,UAAU1T,OAA7B;AAEnB,UAAI,CAACF,aAAY;AACf;;AAGF,YAAM0P,kBAAiB3R,MAAMoE;AAE7B,YAAMwT,iBAAiB,IAAIvN,OAAO;QAChChI,QAAQwT,UAAU1T;QAClBF,YAAAA;QACAjC,OAAO2R;QACPlZ;;;QAGA6J,SAASqU;QACTpQ,QAAQsD,KAAD;AACL,gBAAMC,gBAAgBF,eAAepL,IAAIqL,GAAnB;AAEtB,cAAI,CAACC,eAAe;AAClB;;AAGF,gBAAM;YAAC+N;cAAe5B,YAAY9T;AAClC,gBAAMnC,SAAwB;YAAC6J,IAAAA;;AAC/BgO,yBAAW,OAAX,SAAAA,YAAc7X,MAAH;AACXgV,+BAAqB;YAACnH,MAAM;YAAe7N,OAAAA;WAAvB;;QAEtBgG,UAAU6D,KAAIrF,YAAYO,oBAAoBgB,QAArC;AACP,gBAAM+D,gBAAgBF,eAAepL,IAAIqL,GAAnB;AAEtB,cAAI,CAACC,eAAe;AAClB;;AAGF,gBAAM;YAACgO;cAAiB7B,YAAY9T;AACpC,gBAAMnC,SAA0B;YAC9B6J,IAAAA;YACArF;YACAO;YACAgB;;AAGF+R,2BAAa,OAAb,SAAAA,cAAgB9X,MAAH;AACbgV,+BAAqB;YAACnH,MAAM;YAAiB7N,OAAAA;WAAzB;;QAEtBkC,QAAQ6C,oBAAD;AACL,gBAAM8E,MAAKgM,UAAU1T;AAErB,cAAI0H,OAAM,MAAM;AACd;;AAGF,gBAAMC,gBAAgBF,eAAepL,IAAIqL,GAAnB;AAEtB,cAAI,CAACC,eAAe;AAClB;;AAGF,gBAAM;YAACiO;cAAe9B,YAAY9T;AAClC,gBAAMnC,SAAwB;YAC5B2R,gBAAAA;YACAtP,QAAQ;cAACwH,IAAAA;cAAI+L,MAAM9L,cAAc8L;cAAM7e,MAAM0e;;;AAG/CuC,wDAAwB,MAAA;AACtBD,2BAAW,OAAX,SAAAA,YAAc/X,MAAH;AACXoV,sBAAUb,OAAO0D,YAAR;AACT7F,qBAAS;cACPvE,MAAMiF,OAAOpN;cACbX;cACA1C,QAAQwH;aAHF;AAKRmL,iCAAqB;cAACnH,MAAM;cAAe7N,OAAAA;aAAvB;AACpB+V,4BAAgB2B,gBAAgBvV,OAAjB;AACf6T,8BAAkBrE,eAAD;WAVI;;QAazB7N,OAAOD,aAAD;AACJuO,mBAAS;YACPvE,MAAMiF,OAAOC;YACblP;WAFM;;QAKVE,OAAOmU,cAAcpF,OAAOE,OAAR;QACpB/O,UAAUiU,cAAcpF,OAAOG,UAAR;OA7EF;AAgFvByE,sBAAgBvV,UAAUyV;AAE1B,eAASM,cAAcrK,MAAvB;AACE,eAAO,eAAezO,UAAf;AACL,gBAAM;YAACiD,QAAAA;YAAQwP,YAAAA;YAAYG,MAAAA;YAAM8E,yBAAAA;cAC/BH,cAAcxU;AAChB,cAAInC,SAA6B;AAEjC,cAAIqC,WAAUyU,0BAAyB;AACrC,kBAAM;cAACqB;gBAAclC,YAAY9T;AAEjCnC,YAAAA,SAAQ;cACN2R,gBAAAA;cACAtP,QAAQA;cACRwP,YAAAA;cACApS,OAAOqX;cACP9E,MAAAA;;AAGF,gBAAInE,SAASiF,OAAOE,WAAW,OAAOmF,eAAe,YAAY;AAC/D,oBAAMC,eAAe,MAAMC,QAAQC,QAAQH,WAAWnY,MAAD,CAA1B;AAE3B,kBAAIoY,cAAc;AAChBvK,uBAAOiF,OAAOG;;;;AAKpB4C,oBAAU1T,UAAU;AAEpB6V,wDAAwB,MAAA;AACtB5F,qBAAS;cAACvE;aAAF;AACRuH,sBAAUb,OAAOc,aAAR;AACTmC,oBAAQ,IAAD;AACPzB,4BAAgB,IAAD;AACfC,8BAAkB,IAAD;AACjB0B,4BAAgBvV,UAAU;AAE1B,kBAAMhD,YACJ0O,SAASiF,OAAOE,UAAU,cAAc;AAE1C,gBAAIhT,QAAO;AACT,oBAAMZ,WAAU6W,YAAY9T,QAAQhD,SAApB;AAEhBC,cAAAA,YAAO,OAAP,SAAAA,SAAUY,MAAH;AACPgV,mCAAqB;gBAACnH,MAAM1O;gBAAWa,OAAAA;eAAnB;;WAfD;;;;;IAsB7B,CAAC4J,cAAD;EArJmC;AAwJrC,QAAM2O,wCAAoCzP,2BACxC,CACE1J,SACAgL,WAFF;AAIE,WAAO,CAACpK,OAAOqC,YAAR;AACL,YAAM+B,cAAcpE,MAAMoE;AAC1B,YAAMoU,sBAAsB5O,eAAepL,IAAI6D,OAAnB;AAE5B;;QAEEwT,UAAU1T,YAAY;QAEtB,CAACqW;QAEDpU,YAAYqU,UACZrU,YAAYsU;QACZ;AACA;;AAGF,YAAMC,oBAAoB;QACxBtW,QAAQmW;;AAEV,YAAMI,iBAAiBxZ,QACrBY,OACAoK,OAAO3R,SACPkgB,iBAH4B;AAM9B,UAAIC,mBAAmB,MAAM;AAC3BxU,oBAAYqU,SAAS;UACnBI,YAAYzO,OAAOA;;AAGrByL,kBAAU1T,UAAUE;AACpBsV,0BAAkB3X,OAAOoK,MAAR;;;KAIvB,CAACR,gBAAgB+N,iBAAjB,CAxCmD;AA2CrD,QAAMzT,aAAa8F,qBACjBC,SACAsO,iCAFqC;AAKvC3I,iBAAe3F,OAAD;AAEd+D,4BAA0B,MAAA;AACxB,QAAI4D,kBAAkBuD,WAAWZ,OAAO0D,cAAc;AACpD7C,gBAAUb,OAAOgB,WAAR;;KAEV,CAAC3D,gBAAgBuD,MAAjB,CAJsB;AAMzBlM;IACE,MAAA;AACE,YAAM;QAAC6P;UAAc7C,YAAY9T;AACjC,YAAM;QAACE,QAAAA;QAAQsP,gBAAAA;QAAgBE,YAAAA;QAAYG,MAAAA;UAAQ2E,cAAcxU;AAEjE,UAAI,CAACE,WAAU,CAACsP,iBAAgB;AAC9B;;AAGF,YAAM3R,QAAuB;QAC3BqC,QAAAA;QACAsP,gBAAAA;QACAE,YAAAA;QACApS,OAAO;UACLlJ,GAAGugB,wBAAwBvgB;UAC3BE,GAAGqgB,wBAAwBrgB;;QAE7Bub,MAAAA;;AAGFgG,oDAAwB,MAAA;AACtBc,sBAAU,OAAV,SAAAA,WAAa9Y,KAAH;AACVgV,6BAAqB;UAACnH,MAAM;UAAc7N;SAAtB;OAFC;;;IAMzB,CAAC8W,wBAAwBvgB,GAAGugB,wBAAwBrgB,CAApD;EA1BO;AA6BTwS;IACE,MAAA;AACE,YAAM;QACJ5G,QAAAA;QACAsP,gBAAAA;QACAE,YAAAA;QACAE,qBAAAA;QACA+E,yBAAAA;UACEH,cAAcxU;AAElB,UACE,CAACE,WACDwT,UAAU1T,WAAW,QACrB,CAACwP,mBACD,CAACmF,0BACD;AACA;;AAGF,YAAM;QAACiC;UAAc9C,YAAY9T;AACjC,YAAM6W,gBAAgBjH,qBAAoBvT,IAAI8Y,MAAxB;AACtB,YAAMtF,QACJgH,iBAAiBA,cAAcjiB,KAAKoL,UAChC;QACE0H,IAAImP,cAAcnP;QAClB9S,MAAMiiB,cAAcjiB,KAAKoL;QACzByT,MAAMoD,cAAcpD;QACpBzN,UAAU6Q,cAAc7Q;UAE1B;AACN,YAAMnI,QAAuB;QAC3BqC,QAAAA;QACAsP,gBAAAA;QACAE,YAAAA;QACApS,OAAO;UACLlJ,GAAGugB,yBAAwBvgB;UAC3BE,GAAGqgB,yBAAwBrgB;;QAE7Bub,MAAAA;;AAGFgG,oDAAwB,MAAA;AACtBR,gBAAQxF,KAAD;AACP+G,sBAAU,OAAV,SAAAA,WAAa/Y,KAAH;AACVgV,6BAAqB;UAACnH,MAAM;UAAc7N;SAAtB;OAHC;;;IAOzB,CAACsX,MAAD;EAhDO;AAmDTtJ,4BAA0B,MAAA;AACxB2I,kBAAcxU,UAAU;MACtBwP;MACAtP;MACAJ;MACAU;MACAkP;MACA/F;MACAlC;MACAgN;MACAC;MACA9E;MACAC;MACA1U;MACAwZ;;AAGFrB,gBAAYtT,UAAU;MACpBuT,SAASmB;MACTlB,YAAYhT;;KAEb,CACDN,QACAJ,YACA4P,YACAlP,eACAiH,gBACAgN,cACAC,kBACA/K,gBACAiG,qBACAC,MACA1U,qBACAwZ,uBAZC,CArBsB;AAoCzBvP,kBAAgB;IACd,GAAG+O;IACH7W,OAAOiT;IACPhL,cAAc/E;IACdoF;IACAzK;IACA0K;GANa;AASf,QAAMiR,oBAAgBvQ,uBAAQ,MAAA;AAC5B,UAAMpG,UAAmC;MACvCD;MACAJ;MACA2P;MACAD;MACAE;MACAC;MACAT;MACAzH;MACAmI;MACAjG;MACAkG;MACAtG;MACApO;MACA0K;MACAiK;MACA/F;MACAgE;;AAGF,WAAO5N;KACN,CACDD,QACAJ,YACA2P,gBACAD,gBACAE,YACAC,mBACAT,aACAzH,gBACAmI,qBACAjG,gBACAkG,MACAtG,4BACApO,qBACA0K,yBACAiK,wBACA/F,oBACAgE,UAjBC,CAtB0B;AA0C7B,QAAMgJ,sBAAkBxQ,uBAAQ,MAAA;AAC9B,UAAMpG,UAAqC;MACzCqP;MACAzN;MACA7B;MACAuP;MACAO,mBAAmB;QACjBlB,WAAWiF;;MAEb9D;MACAxI;MACAoI;MACAtG;;AAGF,WAAOpJ;KACN,CACDqP,gBACAzN,YACA7B,QACAuP,gBACAQ,UACA8D,wBACAtM,gBACAoI,MACAtG,0BATC,CAhB4B;AA4B/B,SACEyN,cAAAA,QAAAA,cAACC,kBAAkBC,UAAnB;IAA4B3f,OAAOub;KACjCkE,cAAAA,QAAAA,cAAC9G,gBAAgBgH,UAAjB;IAA0B3f,OAAOwf;KAC/BC,cAAAA,QAAAA,cAAC5G,cAAc8G,UAAf;IAAwB3f,OAAOuf;KAC7BE,cAAAA,QAAAA,cAAC7E,uBAAuB+E,UAAxB;IAAiC3f,OAAO3D;KACrCwa,QADH,CADF,GAKA4I,cAAAA,QAAAA,cAAC7F,cAAD;IAAcnL,WAAUuM,iBAAa,OAAb,SAAAA,cAAe4E,kBAAiB;GAAxD,CANF,GAQAH,cAAAA,QAAAA,cAACI,eAAD;IAAA,GACM7E;IACJ8E,yBAAyBtD;GAF3B,CATF;AAgBF,WAASK,yBAAT;AACE,UAAMkD,kCACJ3D,gBAAY,OAAZ,SAAAA,aAAczU,uBAAsB;AACtC,UAAMqY,6BACJ,OAAO7Q,eAAe,WAClBA,WAAWlB,YAAY,QACvBkB,eAAe;AACrB,UAAMlB,UACJ2N,iBACA,CAACmE,kCACD,CAACC;AAEH,QAAI,OAAO7Q,eAAe,UAAU;AAClC,aAAO;QACL,GAAGA;QACHlB;;;AAIJ,WAAO;MAACA;;;AAEX,CAtnB6B;ACrG9B,IAAMgS,kBAAcrH,6BAAmB,IAAN;AAEjC,IAAMsH,cAAc;AAEpB,IAAMC,YAAY;AAElB,SAAgBC,aAAAA,MAAAA;MAAa;IAC3BjQ;IACA+L;IACAzN,WAAW;IACX4R;;AAEA,QAAM1b,OAAM8X,YAAY0D,SAAD;AACvB,QAAM;IACJ3V;IACAyN;IACAtP;IACAuP;IACAO;IACAvI;IACAoI;UACEuB,0BAAWlB,eAAD;AACd,QAAM;IACJ2H,OAAOJ;IACPK,kBAAkB;IAClBC,WAAW;MACTH,cAJE,OAIFA,aAAc,CAAA;AAClB,QAAMI,cAAa9X,UAAM,OAAN,SAAAA,OAAQwH,QAAOA;AAClC,QAAM9T,gBAA8Bwd,0BAClC4G,aAAa7F,yBAAyBqF,WADM;AAG9C,QAAM,CAACzgB,MAAMkhB,UAAP,IAAqBvJ,WAAU;AACrC,QAAM,CAACvM,eAAe+V,mBAAhB,IAAuCxJ,WAAU;AACvD,QAAM/R,YAAYgR,sBAAsB5L,YAAY2F,EAAb;AACvC,QAAMyQ,UAAU7O,eAAemK,IAAD;AAE9B5H;IACE,MAAA;AACEpE,qBAAeqC,IAAIpC,IAAI;QAACA;QAAIxL,KAAAA;QAAKnF;QAAMoL;QAAesR,MAAM0E;OAA5D;AAEA,aAAO,MAAA;AACL,cAAMphB,QAAO0Q,eAAepL,IAAIqL,EAAnB;AAEb,YAAI3Q,SAAQA,MAAKmF,QAAQA,MAAK;AAC5BuL,yBAAeyJ,OAAOxJ,EAAtB;;;;;IAKN,CAACD,gBAAgBC,EAAjB;EAbuB;AAgBzB,QAAM0Q,yBAA0C7R,uBAC9C,OAAO;IACLsR;IACAE;IACA,iBAAiB/R;IACjB,gBAAgBgS,cAAcH,SAASJ,cAAc,OAAO1Y;IAC5D,wBAAwB+Y;IACxB,oBAAoB9H,kBAAkBlB;MAExC,CACE9I,UACA6R,MACAE,UACAC,YACAF,iBACA9H,kBAAkBlB,SANpB,CATqD;AAmBvD,SAAO;IACL5O;IACAsP;IACAC;IACAmI,YAAYQ;IACZJ;IACArb,WAAWqJ,WAAWjH,SAAYpC;IAClC5F;IACA8Y;IACAoI;IACAC;IACAtkB;;AAEH;SCrHeykB,gBAAAA;AACd,aAAOjH,0BAAWhB,aAAD;AAClB;ACsBD,IAAMsH,cAAY;AAElB,IAAMY,8BAA8B;EAClCC,SAAS;AADyB;AAIpC,SAAgBC,aAAAA,MAAAA;MAAa;IAC3B/E;IACAzN,WAAW;IACX0B;IACA+Q;;AAEA,QAAMvc,OAAM8X,YAAY0D,WAAD;AACvB,QAAM;IAACxX;IAAQ+P;IAAUJ;IAAMtG;UAC7B6H,0BAAWlB,eAAD;AACZ,QAAMwI,eAAWrS,sBAAO;IAACL;GAAF;AACvB,QAAM2S,8BAA0BtS,sBAAO,KAAD;AACtC,QAAMzR,WAAOyR,sBAA0B,IAApB;AACnB,QAAMuS,iBAAavS,sBAA8B,IAAxB;AACzB,QAAM;IACJL,UAAU6S;IACVC;IACAP,SAASQ;MACP;IACF,GAAGT;IACH,GAAGG;;AAEL,QAAMjP,OAAMF,eAAewP,yBAAD,OAACA,wBAAyBpR,EAA1B;AAC1B,QAAMmD,mBAAelE;IACnB,MAAA;AACE,UAAI,CAACgS,wBAAwB3Y,SAAS;AAGpC2Y,gCAAwB3Y,UAAU;AAClC;;AAGF,UAAI4Y,WAAW5Y,WAAW,MAAM;AAC9B2D,qBAAaiV,WAAW5Y,OAAZ;;AAGd4Y,iBAAW5Y,UAAUJ,WAAW,MAAA;AAC9B2J,mCACE2D,MAAM8L,QAAQxP,KAAIxJ,OAAlB,IAA6BwJ,KAAIxJ,UAAU,CAACwJ,KAAIxJ,OAAL,CADnB;AAG1B4Y,mBAAW5Y,UAAU;SACpB+Y,qBAL4B;;;IAQjC,CAACA,qBAAD;EArB8B;AAuBhC,QAAMjO,iBAAiBF,kBAAkB;IACvCN,UAAUO;IACV7E,UAAU6S,0BAA0B,CAAC3Y;GAFC;AAIxC,QAAMqO,uBAAmB5H,2BACvB,CAACsS,YAAgCC,oBAAjC;AACE,QAAI,CAACpO,gBAAgB;AACnB;;AAGF,QAAIoO,iBAAiB;AACnBpO,qBAAeqO,UAAUD,eAAzB;AACAP,8BAAwB3Y,UAAU;;AAGpC,QAAIiZ,YAAY;AACdnO,qBAAegB,QAAQmN,UAAvB;;KAGJ,CAACnO,cAAD,CAfkC;AAiBpC,QAAM,CAAC0D,SAASyJ,UAAV,IAAwBvJ,WAAWH,gBAAD;AACxC,QAAM4J,UAAU7O,eAAemK,IAAD;AAE9B3M,+BAAU,MAAA;AACR,QAAI,CAACgE,kBAAkB,CAAC0D,QAAQxO,SAAS;AACvC;;AAGF8K,mBAAeH,WAAf;AACAgO,4BAAwB3Y,UAAU;AAClC8K,mBAAegB,QAAQ0C,QAAQxO,OAA/B;KACC,CAACwO,SAAS1D,cAAV,CARM;AAUThE;IACE,MAAA;AACEmJ,eAAS;QACPvE,MAAMiF,OAAOI;QACb1a,SAAS;UACPqR;UACAxL,KAAAA;UACA8J;UACAjP,MAAMyX;UACN5Z;UACA6e,MAAM0E;;OARF;AAYR,aAAO,MACLlI,SAAS;QACPvE,MAAMiF,OAAOM;QACb/U,KAAAA;QACAwL;OAHM;;;IAOZ,CAACA,EAAD;EAtBO;AAyBTZ,+BAAU,MAAA;AACR,QAAId,aAAa0S,SAAS1Y,QAAQgG,UAAU;AAC1CiK,eAAS;QACPvE,MAAMiF,OAAOK;QACbtJ;QACAxL,KAAAA;QACA8J;OAJM;AAOR0S,eAAS1Y,QAAQgG,WAAWA;;KAE7B,CAAC0B,IAAIxL,MAAK8J,UAAUiK,QAApB,CAXM;AAaT,SAAO;IACL/P;IACAtL;IACAwkB,SAAQvJ,QAAI,OAAJ,SAAAA,KAAMnI,QAAOA;IACrB3Q,MAAMyX;IACNqB;IACAoI;;AAEH;SC/IeoB,iBAAAA,MAAAA;MAAiB;IAACC;IAAWlL;;AAC3C,QAAM,CACJmL,gBACAC,iBAFI,QAGFxQ,wBAAoC,IAA5B;AACZ,QAAM,CAAC3S,SAASojB,UAAV,QAAwBzQ,wBAA6B,IAArB;AACtC,QAAM0Q,mBAAmBtS,YAAYgH,QAAD;AAEpC,MAAI,CAACA,YAAY,CAACmL,kBAAkBG,kBAAkB;AACpDF,sBAAkBE,gBAAD;;AAGnB7N,4BAA0B,MAAA;AACxB,QAAI,CAACxV,SAAS;AACZ;;AAGF,UAAM6F,OAAMqd,kBAAH,OAAA,SAAGA,eAAgBrd;AAC5B,UAAMwL,KAAK6R,kBAAH,OAAA,SAAGA,eAAgBta,MAAMyI;AAEjC,QAAIxL,QAAO,QAAQwL,MAAM,MAAM;AAC7B8R,wBAAkB,IAAD;AACjB;;AAGFtD,YAAQC,QAAQmD,UAAU5R,IAAIrR,OAAL,CAAzB,EAAwCsjB,KAAK,MAAA;AAC3CH,wBAAkB,IAAD;KADnB;KAGC,CAACF,WAAWC,gBAAgBljB,OAA5B,CAhBsB;AAkBzB,SACE2gB,cAAAA,QAAAA,cAAA,cAAAA,QAAA,UAAA,MACG5I,UACAmL,qBAAiBK,4BAAaL,gBAAgB;IAACM,KAAKJ;GAAvB,IAAsC,IAFtE;AAKH;ACzCD,IAAMK,mBAA8B;EAClC1lB,GAAG;EACHE,GAAG;EACHP,QAAQ;EACRE,QAAQ;AAJ0B;AAOpC,SAAgB8lB,yBAAAA,MAAAA;MAAyB;IAAC3L;;AACxC,SACE4I,cAAAA,QAAAA,cAAC9G,gBAAgBgH,UAAjB;IAA0B3f,OAAOwY;KAC/BiH,cAAAA,QAAAA,cAAC7E,uBAAuB+E,UAAxB;IAAiC3f,OAAOuiB;KACrC1L,QADH,CADF;AAMH;ACAD,IAAM4L,aAAkC;EACtC/iB,UAAU;EACVgjB,aAAa;AAFyB;AAKxC,IAAMC,oBAAuC1K,oBAAD;AAC1C,QAAM2K,sBAAsBla,gBAAgBuP,cAAD;AAE3C,SAAO2K,sBAAsB,yBAAyBpb;AACvD;AAEM,IAAMqb,wBAAoBC,0BAC/B,CAAA,MAYER,QAZF;MACE;IACES;IACA9K;IACA7b,aAAAA;IACAya;IACAmM;IACA3lB;IACA4lB;IACA5mB;IACA6mB,aAAaP;;AAIf,MAAI,CAACtlB,MAAM;AACT,WAAO;;AAGT,QAAM8lB,yBAAyB/mB,eAC3BC,YACA;IACE,GAAGA;IACHG,QAAQ;IACRE,QAAQ;;AAEd,QAAM0mB,SAA0C;IAC9C,GAAGX;IACHhmB,OAAOY,KAAKZ;IACZE,QAAQU,KAAKV;IACbK,KAAKK,KAAKL;IACVF,MAAMO,KAAKP;IACXT,WAAWgnB,IAAIC,UAAUC,SAASJ,sBAAvB;IACXhlB,iBACE/B,gBAAe6b,iBACXuL,2BACEvL,gBACA5a,IAFwB,IAI1BmK;IACN0b,YACE,OAAOA,eAAe,aAClBA,WAAWjL,cAAD,IACViL;IACN,GAAGD;;AAGL,SAAOxD,cAAAA,QAAMgE,cACXV,IACA;IACEC;IACAC,OAAOG;IACPd;KAEFzL,QAPK;AASR,CAxDwC;ICwD9B6M,kCACX3kB,aAC6B,UAAA;MAAC;IAAC4J;IAAQgP;;AACvC,QAAMgM,iBAAyC,CAAA;AAC/C,QAAM;IAACP;IAAQJ;MAAajkB;AAE5B,MAAIqkB,UAAJ,QAAIA,OAAQza,QAAQ;AAClB,eAAW,CAAChE,MAAK3E,KAAN,KAAgB4E,OAAO4Q,QAAQ4N,OAAOza,MAAtB,GAA+B;AACxD,UAAI3I,UAAUwH,QAAW;AACvB;;AAGFmc,qBAAehf,IAAD,IAAQgE,OAAOnJ,KAAKyjB,MAAMW,iBAAiBjf,IAAnC;AACtBgE,aAAOnJ,KAAKyjB,MAAMY,YAAYlf,MAAK3E,KAAnC;;;AAIJ,MAAIojB,UAAJ,QAAIA,OAAQzL,aAAa;AACvB,eAAW,CAAChT,MAAK3E,KAAN,KAAgB4E,OAAO4Q,QAAQ4N,OAAOzL,WAAtB,GAAoC;AAC7D,UAAI3X,UAAUwH,QAAW;AACvB;;AAGFmQ,kBAAYnY,KAAKyjB,MAAMY,YAAYlf,MAAK3E,KAAxC;;;AAIJ,MAAIgjB,aAAJ,QAAIA,UAAWra,QAAQ;AACrBA,WAAOnJ,KAAKskB,UAAUjgB,IAAImf,UAAUra,MAApC;;AAGF,MAAIqa,aAAJ,QAAIA,UAAWrL,aAAa;AAC1BA,gBAAYnY,KAAKskB,UAAUjgB,IAAImf,UAAUrL,WAAzC;;AAGF,SAAO,SAASpC,UAAT;AACL,eAAW,CAAC5Q,MAAK3E,KAAN,KAAgB4E,OAAO4Q,QAAQmO,cAAf,GAAgC;AACzDhb,aAAOnJ,KAAKyjB,MAAMY,YAAYlf,MAAK3E,KAAnC;;AAGF,QAAIgjB,aAAJ,QAAIA,UAAWra,QAAQ;AACrBA,aAAOnJ,KAAKskB,UAAUC,OAAOf,UAAUra,MAAvC;;;AAGL;AAED,IAAMqb,0BAA4C,WAAA;AAAA,MAAC;IACjD3nB,WAAW;MAAC2f;MAASiI;;MAD2B;AAAA,SAE5C,CACJ;IACE5nB,WAAWgnB,IAAIC,UAAUC,SAASvH,OAAvB;KAEb;IACE3f,WAAWgnB,IAAIC,UAAUC,SAASU,KAAvB;GALT;AAF4C;AAWlD,IAAaC,oCAAoE;EAC/EC,UAAU;EACVC,QAAQ;EACRC,WAAWL;EACXM,aAAaZ,gCAAgC;IAC3CN,QAAQ;MACNza,QAAQ;QACN4b,SAAS;;;GAH6B;AAJmC;AAajF,SAAgBC,iBAAAA,OAAAA;MAAiB;IAC/BlT;IACApB;IACAmI;IACAE;;AAEA,SAAOtF,SAAoB,CAAC9C,IAAI3Q,SAAL;AACzB,QAAI8R,WAAW,MAAM;AACnB;;AAGF,UAAMmT,kBAA6CvU,eAAepL,IAAIqL,EAAnB;AAEnD,QAAI,CAACsU,iBAAiB;AACpB;;AAGF,UAAMlc,aAAakc,gBAAgBjlB,KAAKiJ;AAExC,QAAI,CAACF,YAAY;AACf;;AAGF,UAAMmc,iBAAiB9N,kBAAkBpX,IAAD;AAExC,QAAI,CAACklB,gBAAgB;AACnB;;AAEF,UAAM;MAACroB;QAAa4C,UAAUO,IAAD,EAAON,iBAAiBM,IAAjC;AACpB,UAAMpB,kBAAkBP,eAAexB,SAAD;AAEtC,QAAI,CAAC+B,iBAAiB;AACpB;;AAGF,UAAM2jB,YACJ,OAAOzQ,WAAW,aACdA,SACAqT,2BAA2BrT,MAAD;AAEhCtN,2BACEuE,YACAgQ,uBAAuBhB,UAAUtT,OAFb;AAKtB,WAAO8d,UAAU;MACfpZ,QAAQ;QACNwH;QACA+L,MAAMuI,gBAAgBvI;QACtB1c,MAAM+I;QACNlL,MAAMkb,uBAAuBhB,UAAUtT,QAAQsE,UAAzC;;MAER2H;MACAyH,aAAa;QACXnY;QACAnC,MAAMkb,uBAAuBZ,YAAY1T,QAAQygB,cAA3C;;MAERrM;MACAE;MACAlc,WAAW+B;KAdG;GAvCH;AAwDhB;AAED,SAASumB,2BACP5lB,SADF;AAGE,QAAM;IAAColB;IAAUC;IAAQE;IAAaD;MAAa;IACjD,GAAGH;IACH,GAAGnlB;;AAGL,SAAO,WAAA;QAAC;MAAC4J;MAAQgP;MAAatb;MAAW,GAAGuoB;;AAC1C,QAAI,CAACT,UAAU;AAEb;;AAGF,UAAMpe,QAAQ;MACZlJ,GAAG8a,YAAYta,KAAKP,OAAO6L,OAAOtL,KAAKP;MACvCC,GAAG4a,YAAYta,KAAKL,MAAM2L,OAAOtL,KAAKL;;AAGxC,UAAM6nB,QAAQ;MACZroB,QACEH,UAAUG,WAAW,IAChBmM,OAAOtL,KAAKZ,QAAQJ,UAAUG,SAAUmb,YAAYta,KAAKZ,QAC1D;MACNC,QACEL,UAAUK,WAAW,IAChBiM,OAAOtL,KAAKV,SAASN,UAAUK,SAAUib,YAAYta,KAAKV,SAC3D;;AAER,UAAMmoB,iBAAiB;MACrBjoB,GAAGR,UAAUQ,IAAIkJ,MAAMlJ;MACvBE,GAAGV,UAAUU,IAAIgJ,MAAMhJ;MACvB,GAAG8nB;;AAGL,UAAME,qBAAqBV,UAAU;MACnC,GAAGO;MACHjc;MACAgP;MACAtb,WAAW;QAAC2f,SAAS3f;QAAW4nB,OAAOa;;KAJL;AAOpC,UAAM,CAACE,aAAD,IAAkBD;AACxB,UAAME,eAAeF,mBAAmBA,mBAAmBzkB,SAAS,CAA7B;AAEvC,QAAImP,KAAKC,UAAUsV,aAAf,MAAkCvV,KAAKC,UAAUuV,YAAf,GAA8B;AAElE;;AAGF,UAAM1P,UAAU+O,eAAH,OAAA,SAAGA,YAAc;MAAC3b;MAAQgP;MAAa,GAAGiN;KAA5B;AAC3B,UAAM7C,YAAYpK,YAAYnY,KAAK0lB,QAAQH,oBAAoB;MAC7DZ;MACAC;MACAe,MAAM;KAHU;AAMlB,WAAO,IAAIxG,QAASC,aAAD;AACjBmD,gBAAUqD,WAAW,MAAA;AACnB7P,mBAAO,OAAP,SAAAA,QAAO;AACPqJ,gBAAO;;KAHJ;;AAOV;AC9RD,IAAIja,MAAM;AAEV,SAAgB0gB,OAAOlV,IAAAA;AACrB,aAAOnB,uBAAQ,MAAA;AACb,QAAImB,MAAM,MAAM;AACd;;AAGFxL;AACA,WAAOA;KACN,CAACwL,EAAD,CAPW;AAQf;ICaYmV,cAAc7F,cAAAA,QAAM1E,KAC/B,UAAA;MAAC;IACC3e,aAAAA,eAAc;IACdya;IACA0O,eAAeC;IACfvC;IACAC;IACA5I;IACAmL,iBAAiB;IACjBzC;IACA0C,SAAS;;AAET,QAAM;IACJzN;IACAtP;IACAuP;IACAE;IACAlI;IACAmI;IACAV;IACAW;IACAC;IACA3U;IACA0K;IACAkI;MACEsK,cAAa;AACjB,QAAMzkB,gBAAYwd,0BAAWe,sBAAD;AAC5B,QAAMjW,OAAM0gB,OAAO1c,UAAD,OAAA,SAACA,OAAQwH,EAAT;AAClB,QAAMwV,oBAAoBtL,eAAeC,WAAW;IAClDrC;IACAtP;IACAuP;IACAE;IACA+E,kBAAkBxF,YAAYta;IAC9Bib;IACAmF,iBAAiB9F,YAAYta;IAC7BuG;IACA0K;IACAjS;IACAma;GAXsC;AAaxC,QAAM5B,cAAcjC,gBAAgBuF,cAAD;AACnC,QAAMqN,gBAAgBf,iBAAiB;IACrClT,QAAQkU;IACRtV;IACAmI;IACAE;GAJoC;AAQtC,QAAM+J,MAAM1N,cAAc+C,YAAYT,SAAS1P;AAE/C,SACEiY,cAAAA,QAAAA,cAAC+C,0BAAD,MACE/C,cAAAA,QAAAA,cAACqC,kBAAD;IAAkBC,WAAWwD;KAC1B5c,UAAUhE,OACT8a,cAAAA,QAAAA,cAACoD,mBAAD;IACEle,KAAKA;IACLwL,IAAIxH,OAAOwH;IACXmS;IACAS,IAAI0C;IACJxN;IACA7b,aAAaA;IACb4mB;IACAE;IACA7lB,MAAMuX;IACNqO,OAAO;MACLyC;MACA,GAAGzC;;IAEL5mB,WAAWspB;KAEV9O,QAhBH,IAkBE,IApBN,CADF;AAyBH,CA9EwB;;;;SCzBX+O,UAAaC,OAAYC,MAAcC,IAAAA;AACrD,QAAMC,WAAWH,MAAMI,MAAN;AACjBD,WAASE,OACPH,KAAK,IAAIC,SAASG,SAASJ,KAAKA,IAChC,GACAC,SAASE,OAAOJ,MAAM,CAAtB,EAAyB,CAAzB,CAHF;AAMA,SAAOE;AACR;SENeI,eACdC,OACAC,OAAAA;AAEA,SAAOD,MAAME,OAAqB,CAACC,aAAaC,IAAIC,UAAlB;AAChC,UAAMC,OAAOL,MAAMM,IAAIH,EAAV;AAEb,QAAIE,MAAM;AACRH,kBAAYE,KAAD,IAAUC;;AAGvB,WAAOH;KACNK,MAAMR,MAAMS,MAAP,CARD;AASR;SCnBeC,aAAaL,OAAAA;AAC3B,SAAOA,UAAU,QAAQA,SAAS;AACnC;SCAeM,WAAWC,IAAuBC,IAAAA;AAChD,MAAID,OAAMC,IAAG;AACX,WAAO;;AAGT,MAAID,GAAEH,WAAWI,GAAEJ,QAAQ;AACzB,WAAO;;AAGT,WAASK,KAAI,GAAGA,KAAIF,GAAEH,QAAQK,MAAK;AACjC,QAAIF,GAAEE,EAAD,MAAQD,GAAEC,EAAD,GAAK;AACjB,aAAO;;;AAIX,SAAO;AACR;SChBeC,kBAAkBC,UAAAA;AAChC,MAAI,OAAOA,aAAa,WAAW;AACjC,WAAO;MACLC,WAAWD;MACXE,WAAWF;;;AAIf,SAAOA;AACR;ACPD,IAAMG,eAAe;EACnBC,QAAQ;EACRC,QAAQ;AAFW;AAKrB,IAAaC,gCAAiD,UAAA;;MAAC;IAC7DrB;IACAsB,gBAAgBC;IAChBC;IACAC;IACArB;;AAEA,QAAMkB,kBAAc,qBAAGtB,MAAMwB,WAAD,MAAR,OAAA,qBAAyBD;AAE7C,MAAI,CAACD,gBAAgB;AACnB,WAAO;;AAGT,QAAMI,UAAUC,WAAW3B,OAAOI,OAAOoB,WAAf;AAE1B,MAAIpB,UAAUoB,aAAa;AACzB,UAAMI,eAAe5B,MAAMyB,SAAD;AAE1B,QAAI,CAACG,cAAc;AACjB,aAAO;;AAGT,WAAO;MACLC,GACEL,cAAcC,YACVG,aAAaE,OACbF,aAAaG,SACZT,eAAeQ,OAAOR,eAAeS,SACtCH,aAAaE,OAAOR,eAAeQ;MACzCE,GAAG;MACH,GAAGd;;;AAIP,MAAId,QAAQoB,eAAepB,SAASqB,WAAW;AAC7C,WAAO;MACLI,GAAG,CAACP,eAAeS,QAAQL;MAC3BM,GAAG;MACH,GAAGd;;;AAIP,MAAId,QAAQoB,eAAepB,SAASqB,WAAW;AAC7C,WAAO;MACLI,GAAGP,eAAeS,QAAQL;MAC1BM,GAAG;MACH,GAAGd;;;AAIP,SAAO;IACLW,GAAG;IACHG,GAAG;IACH,GAAGd;;AAEN;AAED,SAASS,WAAW3B,OAAqBI,OAAeoB,aAAxD;AACE,QAAMS,cAAsCjC,MAAMI,KAAD;AACjD,QAAM8B,eAAuClC,MAAMI,QAAQ,CAAT;AAClD,QAAM+B,WAAmCnC,MAAMI,QAAQ,CAAT;AAE9C,MAAI,CAAC6B,eAAgB,CAACC,gBAAgB,CAACC,UAAW;AAChD,WAAO;;AAGT,MAAIX,cAAcpB,OAAO;AACvB,WAAO8B,eACHD,YAAYH,QAAQI,aAAaJ,OAAOI,aAAaH,SACrDI,SAASL,QAAQG,YAAYH,OAAOG,YAAYF;;AAGtD,SAAOI,WACHA,SAASL,QAAQG,YAAYH,OAAOG,YAAYF,SAChDE,YAAYH,QAAQI,aAAaJ,OAAOI,aAAaH;AAC1D;ICjFYK,sBAAuC,UAAA;MAAC;IACnDpC;IACAwB;IACAC;IACArB;;AAEA,QAAMiC,WAAWC,UAAUtC,OAAOyB,WAAWD,WAAnB;AAE1B,QAAMe,UAAUvC,MAAMI,KAAD;AACrB,QAAMoC,UAAUH,SAASjC,KAAD;AAExB,MAAI,CAACoC,WAAW,CAACD,SAAS;AACxB,WAAO;;AAGT,SAAO;IACLV,GAAGW,QAAQV,OAAOS,QAAQT;IAC1BE,GAAGQ,QAAQC,MAAMF,QAAQE;IACzBtB,QAAQqB,QAAQT,QAAQQ,QAAQR;IAChCX,QAAQoB,QAAQE,SAASH,QAAQG;;AAEpC;AEpBD,IAAMC,iBAAe;EACnBC,QAAQ;EACRC,QAAQ;AAFW;AAKrB,IAAaC,8BAA+C,UAAA;;MAAC;IAC3DC;IACAC,gBAAgBC;IAChBC;IACAC;IACAC;;AAEA,QAAMJ,kBAAc,qBAAGG,MAAMJ,WAAD,MAAR,OAAA,qBAAyBE;AAE7C,MAAI,CAACD,gBAAgB;AACnB,WAAO;;AAGT,MAAIE,UAAUH,aAAa;AACzB,UAAMM,gBAAgBF,MAAMC,SAAD;AAE3B,QAAI,CAACC,eAAe;AAClB,aAAO;;AAGT,WAAO;MACLC,GAAG;MACHC,GACER,cAAcK,YACVC,cAAcG,MACdH,cAAcI,UACbT,eAAeQ,MAAMR,eAAeS,UACrCJ,cAAcG,MAAMR,eAAeQ;MACzC,GAAGb;;;AAIP,QAAMe,UAAUC,aAAWR,OAAOD,OAAOH,WAAf;AAE1B,MAAIG,QAAQH,eAAeG,SAASE,WAAW;AAC7C,WAAO;MACLE,GAAG;MACHC,GAAG,CAACP,eAAeS,SAASC;MAC5B,GAAGf;;;AAIP,MAAIO,QAAQH,eAAeG,SAASE,WAAW;AAC7C,WAAO;MACLE,GAAG;MACHC,GAAGP,eAAeS,SAASC;MAC3B,GAAGf;;;AAIP,SAAO;IACLW,GAAG;IACHC,GAAG;IACH,GAAGZ;;AAEN;AAED,SAASgB,aACPC,aACAV,OACAH,aAHF;AAKE,QAAMc,cAAsCD,YAAYV,KAAD;AACvD,QAAMY,eAAuCF,YAAYV,QAAQ,CAAT;AACxD,QAAMa,WAAmCH,YAAYV,QAAQ,CAAT;AAEpD,MAAI,CAACW,aAAa;AAChB,WAAO;;AAGT,MAAId,cAAcG,OAAO;AACvB,WAAOY,eACHD,YAAYL,OAAOM,aAAaN,MAAMM,aAAaL,UACnDM,WACAA,SAASP,OAAOK,YAAYL,MAAMK,YAAYJ,UAC9C;;AAGN,SAAOM,WACHA,SAASP,OAAOK,YAAYL,MAAMK,YAAYJ,UAC9CK,eACAD,YAAYL,OAAOM,aAAaN,MAAMM,aAAaL,UACnD;AACL;AC5ED,IAAMO,aAAY;AAcX,IAAMC,UAAUC,cAAAA,QAAMC,cAAiC;EAC5DpB,aAAa;EACbqB,aAAaJ;EACbK,mBAAmB;EACnBC,OAAO,CAAA;EACPlB,WAAW;EACXmB,gBAAgB;EAChBC,aAAa,CAAA;EACbC,UAAUC;EACVC,UAAU;IACRC,WAAW;IACXC,WAAW;;AAX+C,CAAvC;AAevB,SAAgBC,gBAAAA,MAAAA;MAAgB;IAC9BC;IACAC;IACAV,OAAOW;IACPR,WAAWC;IACXC,UAAUO,eAAe;;AAEzB,QAAM;IACJC;IACAC;IACAC;IACAC;IACAC;MACEC,cAAa;AACjB,QAAMpB,cAAcqB,YAAYzB,YAAWgB,EAAZ;AAC/B,QAAMT,iBAAiBmB,QAAQN,YAAYO,SAAS,IAAtB;AAC9B,QAAMrB,YAAQsB,uBACZ,MACEX,iBAAiBY,IAAKC,UACpB,OAAOA,SAAS,YAAY,QAAQA,OAAOA,KAAKd,KAAKc,IADvD,GAGF,CAACb,gBAAD,CALmB;AAOrB,QAAMc,aAAaZ,UAAU;AAC7B,QAAMpC,cAAcoC,SAASb,MAAM0B,QAAQb,OAAOH,EAArB,IAA2B;AACxD,QAAM5B,YAAYkC,OAAOhB,MAAM0B,QAAQV,KAAKN,EAAnB,IAAyB;AAClD,QAAMiB,uBAAmBC,sBAAO5B,KAAD;AAC/B,QAAM6B,mBAAmB,CAACC,WAAW9B,OAAO2B,iBAAiBI,OAAzB;AACpC,QAAMhC,oBACHjB,cAAc,MAAML,gBAAgB,MAAOoD;AAC9C,QAAMxB,WAAW2B,kBAAkBpB,YAAD;AAElCqB,4BAA0B,MAAA;AACxB,QAAIJ,oBAAoBJ,YAAY;AAClCR,iCAA2BjB,KAAD;;KAE3B,CAAC6B,kBAAkB7B,OAAOyB,YAAYR,0BAAtC,CAJsB;AAMzBiB,+BAAU,MAAA;AACRP,qBAAiBI,UAAU/B;KAC1B,CAACA,KAAD,CAFM;AAIT,QAAMmC,mBAAeb;IACnB,OAA0B;MACxB7C;MACAqB;MACAO;MACAN;MACAC;MACAlB;MACAmB;MACAC,aAAakC,eAAepC,OAAOe,cAAR;MAC3BZ;;;IAGF,CACE1B,aACAqB,aACAO,SAASC,WACTD,SAASE,WACTR,mBACAC,OACAlB,WACAiC,gBACAd,gBACAE,QAVF;EAb0B;AA2B5B,SAAOP,cAAAA,QAAAA,cAACD,QAAQ0C,UAAT;IAAkBC,OAAOH;KAAe1B,QAAxC;AACR;ICzGY8B,wBAAwC,UAAA;AAAA,MAAC;IACpD7B;IACAV;IACAvB;IACAK;MAJmD;AAAA,SAK/C0D,UAAUxC,OAAOvB,aAAaK,SAArB,EAAgC4C,QAAQhB,EAAjD;AAL+C;AAOrD,IAAa+B,8BAAoD,WAAA;MAAC;IAChE3C;IACA4C;IACAC;IACA/D;IACAoB;IACA4C;IACAC;IACAC;IACAC;;AAEA,MAAI,CAACA,cAAc,CAACJ,aAAa;AAC/B,WAAO;;AAGT,MAAIE,kBAAkB7C,SAASpB,UAAUgE,UAAU;AACjD,WAAO;;AAGT,MAAIF,WAAW;AACb,WAAO;;AAGT,SAAOE,aAAahE,SAASkB,gBAAgBgD;AAC9C;AAEM,IAAME,qBAAwC;EACnDC,UAAU;EACVC,QAAQ;AAF2C;AAK9C,IAAMC,qBAAqB;AAE3B,IAAMC,qBAAqBC,IAAIC,WAAWC,SAAS;EACxDC,UAAUL;EACVF,UAAU;EACVC,QAAQ;AAHgD,CAAxB;AAM3B,IAAMO,oBAAoB;EAC/BC,iBAAiB;AADc;ACzCjC,SAAgBC,oBAAAA,MAAAA;MAAoB;IAACtD;IAAUzB;IAAOgF;IAAMvC;;AAC1D,QAAM,CAACwC,kBAAkBC,mBAAnB,QAA0CC,wBAC9C,IADsD;AAGxD,QAAMC,oBAAgBpC,sBAAOhD,KAAD;AAE5BqD,4BAA0B,MAAA;AACxB,QAAI,CAAC5B,YAAYzB,UAAUoF,cAAcjC,WAAW6B,KAAK7B,SAAS;AAChE,YAAMkC,UAAU5C,KAAKU;AAErB,UAAIkC,SAAS;AACX,cAAMlC,UAAUmC,cAAcN,KAAK7B,SAAS;UAC1CoC,iBAAiB;SADU;AAI7B,cAAMC,QAAQ;UACZpF,GAAGiF,QAAQI,OAAOtC,QAAQsC;UAC1BpF,GAAGgF,QAAQ/E,MAAM6C,QAAQ7C;UACzBZ,QAAQ2F,QAAQK,QAAQvC,QAAQuC;UAChC/F,QAAQ0F,QAAQ9E,SAAS4C,QAAQ5C;;AAGnC,YAAIiF,MAAMpF,KAAKoF,MAAMnF,GAAG;AACtB6E,8BAAoBM,KAAD;;;;AAKzB,QAAIxF,UAAUoF,cAAcjC,SAAS;AACnCiC,oBAAcjC,UAAUnD;;KAEzB,CAACyB,UAAUzB,OAAOgF,MAAMvC,IAAxB,CAzBsB;AA2BzBa,+BAAU,MAAA;AACR,QAAI2B,kBAAkB;AACpBC,0BAAoB,IAAD;;KAEpB,CAACD,gBAAD,CAJM;AAMT,SAAOA;AACR;SCjBeU,YAAAA,MAAAA;MAAY;IAC1BC,uBAAuB/B;IACvBgC,YAAYC;IACZrE,UAAUsE;IACVC,MAAMC;IACNC,cAAcvC;IACd7B;IACAP,UAAU4E;IACVC;IACAjC,aAAaC;;AAEb,QAAM;IACJhD;IACAF;IACArB;IACA4B,UAAU4E;IACVlF;IACAG;IACApB;IACAmB;IACAE,UAAU+E;UACRC,0BAAWxF,OAAD;AACd,QAAMU,WAAqB+E,uBACzBT,eACAM,cAF+C;AAIjD,QAAMrG,QAAQoB,MAAM0B,QAAQhB,EAAd;AACd,QAAMkE,WAAOtD,uBACX,OAAO;IAAC+D,UAAU;MAACvF;MAAalB;MAAOoB;;IAAQ,GAAG6E;MAClD,CAAC/E,aAAa+E,YAAYjG,OAAOoB,KAAjC,CAFkB;AAIpB,QAAMsF,gCAA4BhE,uBAChC,MAAMtB,MAAMuF,MAAMvF,MAAM0B,QAAQhB,EAAd,CAAZ,GACN,CAACV,OAAOU,EAAR,CAFuC;AAIzC,QAAM;IACJW;IACAuC;IACA4B;IACAC,YAAYC;MACVC,aAAa;IACfjF;IACAkE;IACAvE,UAAUA,SAASE;IACnByE,sBAAsB;MACpBY,uBAAuBN;MACvB,GAAGN;;GANS;AAShB,QAAM;IACJnE;IACAgF;IACAnH;IACA+F;IACAgB,YAAYK;IACZC;IACAtE;IACAT;IACAgF;IACAC;MACEC,aAAa;IACfxF;IACAkE;IACAH,YAAY;MACV,GAAGhB;MACH,GAAGiB;;IAELrE,UAAUA,SAASC;GAPL;AAShB,QAAMmF,aAAaU,gBAAgBT,qBAAqBI,mBAAtB;AAClC,QAAMpD,YAAYtB,QAAQP,MAAD;AACzB,QAAMuF,eACJ1D,aACA,CAAC3C,qBACDsG,aAAa5H,WAAD,KACZ4H,aAAavH,SAAD;AACd,QAAMwH,2BAA2B,CAACrG,kBAAkBwB;AACpD,QAAM8E,yBACJD,4BAA4BF,eAAeH,YAAY;AACzD,QAAM9F,WAAW4E,iBAAH,OAAGA,gBAAiBG;AAClC,QAAMsB,iBAAiBJ,eACnBG,0BAD+B,OAC/BA,yBACApG,SAAS;IACPtB,OAAOqB;IACPxB;IACAD;IACAK;IACAF;GALM,IAOR;AACJ,QAAMgE,WACJyD,aAAa5H,WAAD,KAAiB4H,aAAavH,SAAD,IACrCgG,YAAY;IAACpE;IAAIV;IAAOvB;IAAaK;GAA1B,IACXF;AACN,QAAM6H,WAAW5F,UAAH,OAAA,SAAGA,OAAQH;AACzB,QAAMgG,eAAW9E,sBAAO;IACtB6E;IACAzG;IACA4C;IACA9C;GAJqB;AAMvB,QAAM+B,mBAAmB7B,UAAU0G,SAAS3E,QAAQ/B;AACpD,QAAM2G,6BAA6BnC,qBAAqB;IACtD3D;IACAf;IACA2B;IACAiB;IACAhC;IACA9B;IACAoB;IACA4C,UAAU8D,SAAS3E,QAAQa;IAC3BC,eAAe6D,SAAS3E,QAAQ/B;IAChC8C,qBAAqB4D,SAAS3E,QAAQjC;IACtCiD;IACAJ,aAAa+D,SAAS3E,QAAQ0E,YAAY;GAZW;AAevD,QAAM5C,mBAAmBF,oBAAoB;IAC3CtD,UAAU,CAACsG;IACX/H;IACAgF;IACAvC;GAJ0C;AAO5Ca,+BAAU,MAAA;AACR,QAAIQ,aAAagE,SAAS3E,QAAQa,aAAaA,UAAU;AACvD8D,eAAS3E,QAAQa,WAAWA;;AAG9B,QAAI9C,gBAAgB4G,SAAS3E,QAAQjC,aAAa;AAChD4G,eAAS3E,QAAQjC,cAAcA;;AAGjC,QAAIE,UAAU0G,SAAS3E,QAAQ/B,OAAO;AACpC0G,eAAS3E,QAAQ/B,QAAQA;;KAE1B,CAAC0C,WAAWE,UAAU9C,aAAaE,KAAnC,CAZM;AAcTkC,+BAAU,MAAA;AACR,QAAIuE,aAAaC,SAAS3E,QAAQ0E,UAAU;AAC1C;;AAGF,QAAIA,YAAY,QAAQC,SAAS3E,QAAQ0E,YAAY,MAAM;AACzDC,eAAS3E,QAAQ0E,WAAWA;AAC5B;;AAGF,UAAMG,YAAYC,WAAW,MAAA;AAC3BH,eAAS3E,QAAQ0E,WAAWA;OAC3B,EAFyB;AAI5B,WAAO,MAAMK,aAAaF,SAAD;KACxB,CAACH,QAAD,CAfM;AAiBT,SAAO;IACL5F;IACApC;IACAgG;IACAG;IACAvD;IACAzC;IACAgE;IACA5C;IACAwF;IACA9C;IACAjB;IACAsE;IACAnC;IACA9E;IACAkC;IACAyE;IACAO;IACAN;IACAI;IACAG,WAAWpC,oBAAF,OAAEA,mBAAoB2C;IAC/BzD,YAAYgE,cAAa;;AAG3B,WAASA,gBAAT;AACE;;MAEElD;MAEChC,oBAAoB6E,SAAS3E,QAAQa,aAAahE;MACnD;AACA,aAAOwE;;AAGT,QACGkD,4BAA4B,CAACU,gBAAgBnB,cAAD,KAC7C,CAAC9C,YACD;AACA,aAAOkE;;AAGT,QAAIvE,aAAaiE,4BAA4B;AAC3C,aAAOtD,IAAIC,WAAWC,SAAS;QAC7B,GAAGR;QACHS,UAAUL;OAFL;;AAMT,WAAO8D;;AAEV;AAED,SAAS7B,uBACPT,eACAM,gBAFF;;AAIE,MAAI,OAAON,kBAAkB,WAAW;AACtC,WAAO;MACLrE,WAAWqE;;MAEXpE,WAAW;;;AAIf,SAAO;IACLD,YAAS,wBAAEqE,iBAAF,OAAA,SAAEA,cAAerE,cAAjB,OAAA,wBAA8B2E,eAAe3E;IACtDC,YAAS,wBAAEoE,iBAAF,OAAA,SAAEA,cAAepE,cAAjB,OAAA,wBAA8B0E,eAAe1E;;AAEzD;AEzPD,IAAM2G,aAAuB,CAC3BC,aAAaC,MACbD,aAAaE,OACbF,aAAaG,IACbH,aAAaI,IAJc;;;ACZ7B,SAASC,IAAW;AAClB,SAAOA,IAAW,OAAO,SAAS,OAAO,OAAO,KAAI,IAAK,SAAUC,IAAG;AACpE,aAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,UAAIC,KAAI,UAAUD,EAAC;AACnB,eAASE,MAAKD,GAAG,EAAC,CAAA,GAAI,eAAe,KAAKA,IAAGC,EAAC,MAAMH,GAAEG,EAAC,IAAID,GAAEC,EAAC;IAC/D;AACD,WAAOH;EACR,GAAED,EAAS,MAAM,MAAM,SAAS;AACnC;;;;;;ACPG,IAACK,IAAgB,EAAE,MAAQ,EAAE,KAAO,OAAO,OAAS,EAAE,aAAa,WAAW,SAAW,iBAAiB,WAAa,QAAO,GAAI,UAAY,CAAC,EAAE,KAAO,QAAQ,OAAS,EAAE,GAAK,4nBAA6nB,EAAA,CAAE,EAAC,GAAI,MAAQ,SAAS,OAAS,WAAU;;;ACD31B,SAASC,EAAgBC,IAAG;AAC1B,MAAI,MAAM,QAAQA,EAAC,EAAG,QAAOA;AAC/B;;;ACFA,SAASC,GAAsBC,IAAGC,IAAG;AACnC,MAAIC,KAAYF,MAAR,OAAY,OAAsB,OAAO,SAAtB,OAAgCA,GAAE,OAAO,QAAQ,KAAKA,GAAE,YAAY;AAC/F,MAAYE,MAAR,MAAW;AACb,QAAIC,IACFC,IACAC,IACAC,IACAC,KAAI,CAAE,GACNC,KAAI,MACJC,KAAI;AACN,QAAI;AACF,UAAIJ,MAAKH,KAAIA,GAAE,KAAKF,EAAC,GAAG,MAAYC,OAAN,EAGvB,QAAO,EAAEO,MAAKL,KAAIE,GAAE,KAAKH,EAAC,GAAG,UAAUK,GAAE,KAAKJ,GAAE,KAAK,GAAGI,GAAE,WAAWN,KAAIO,KAAI,KAAG;IACxF,SAAQR,IAAG;AACVS,MAAAA,KAAI,MAAIL,KAAIJ;IAClB,UAAA;AACM,UAAI;AACF,YAAI,CAACQ,MAAaN,GAAE,UAAV,SAAwBI,KAAIJ,GAAE,OAAW,GAAE,OAAOI,EAAC,MAAMA,IAAI;MAC/E,UAAA;AACQ,YAAIG,GAAG,OAAML;MACd;IACF;AACD,WAAOG;EACR;AACH;;;AC1BA,SAASG,EAAkBC,IAAGC,IAAG;AAC/B,GAASA,MAAR,QAAaA,KAAID,GAAE,YAAYC,KAAID,GAAE;AACtC,WAASE,KAAI,GAAGC,KAAI,MAAMF,EAAC,GAAGC,KAAID,IAAGC,KAAK,CAAAC,GAAED,EAAC,IAAIF,GAAEE,EAAC;AACpD,SAAOC;AACT;;;ACHA,SAASC,EAA4BC,IAAGC,IAAG;AACzC,MAAID,IAAG;AACL,QAAgB,OAAOA,MAAnB,SAAsB,QAAOE,EAAiBF,IAAGC,EAAC;AACtD,QAAIE,KAAI,CAAA,EAAG,SAAS,KAAKH,EAAC,EAAE,MAAM,GAAG,EAAE;AACvC,WAAoBG,OAAb,YAAkBH,GAAE,gBAAgBG,KAAIH,GAAE,YAAY,OAAiBG,OAAV,SAAyBA,OAAV,QAAc,MAAM,KAAKH,EAAC,IAAoBG,OAAhB,eAAqB,2CAA2C,KAAKA,EAAC,IAAID,EAAiBF,IAAGC,EAAC,IAAI;EACrN;AACH;;;ACPA,SAASG,KAAmB;AAC1B,QAAM,IAAI,UAAU;mFAA2I;AACjK;;;ACEA,SAASC,GAAeC,IAAGC,IAAG;AAC5B,SAAOC,EAAeF,EAAC,KAAKG,GAAqBH,IAAGC,EAAC,KAAKG,EAA2BJ,IAAGC,EAAC,KAAKI,GAAe;AAC/G;;;ACNA,SAASC,GAAQC,IAAG;AAClB;AAEA,SAAOD,KAAwB,OAAO,UAArB,cAA2C,OAAO,OAAO,YAA1B,WAAqC,SAAUC,IAAG;AAChG,WAAO,OAAOA;EACf,IAAG,SAAUA,IAAG;AACf,WAAOA,MAAmB,OAAO,UAArB,cAA+BA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;EACtH,GAAKD,GAAQC,EAAC;AACd;;;ACPA,SAASC,EAAYC,IAAGC,IAAG;AACzB,MAAgBC,GAAQF,EAAC,KAArB,YAA0B,CAACA,GAAG,QAAOA;AACzC,MAAIG,KAAIH,GAAE,OAAO,WAAW;AAC5B,MAAeG,OAAX,QAAc;AAChB,QAAIC,KAAID,GAAE,KAAKH,IAAGC,MAAK,SAAS;AAChC,QAAgBC,GAAQE,EAAC,KAArB,SAAwB,QAAOA;AACnC,UAAM,IAAI,UAAU,8CAA8C;EACnE;AACD,UAAqBH,OAAb,WAAiB,SAAS,QAAQD,EAAC;AAC7C;;;ACRA,SAASK,EAAcC,IAAG;AACxB,MAAIC,KAAIC,EAAYF,IAAG,QAAQ;AAC/B,SAAmBG,GAAQF,EAAC,KAArB,WAAyBA,KAAIA,KAAI;AAC1C;;;ACJA,SAASG,EAAgBC,IAAGC,IAAGC,IAAG;AAChC,UAAQD,KAAIE,EAAcF,EAAC,MAAMD,KAAI,OAAO,eAAeA,IAAGC,IAAG;IAC/D,OAAOC;IACP,YAAY;IACZ,cAAc;IACd,UAAU;EACX,CAAA,IAAIF,GAAEC,EAAC,IAAIC,IAAGF;AACjB;;;ACRA,SAASI,EAA8BC,IAAGC,IAAG;AAC3C,MAAYD,MAAR,KAAW,QAAO,CAAA;AACtB,MAAIE,KAAI,CAAA;AACR,WAASC,MAAKH,GAAG,KAAI,CAAA,EAAG,eAAe,KAAKA,IAAGG,EAAC,GAAG;AACjD,QAAWF,GAAE,QAAQE,EAAC,MAAlB,GAAqB;AACzBD,IAAAA,GAAEC,EAAC,IAAIH,GAAEG,EAAC;EACX;AACD,SAAOD;AACT;;;ACPA,SAASE,EAAyBC,IAAGC,IAAG;AACtC,MAAYD,MAAR,KAAW,QAAO,CAAA;AACtB,MAAIE,IACFC,IACAC,KAAIC,EAA6BL,IAAGC,EAAC;AACvC,MAAI,OAAO,uBAAuB;AAChC,QAAIK,KAAI,OAAO,sBAAsBN,EAAC;AACtC,SAAKG,KAAI,GAAGA,KAAIG,GAAE,QAAQH,KAAKD,CAAAA,KAAII,GAAEH,EAAC,GAAUF,GAAE,QAAQC,EAAC,MAAlB,MAAuB,CAAE,EAAC,qBAAqB,KAAKF,IAAGE,EAAC,MAAME,GAAEF,EAAC,IAAIF,GAAEE,EAAC;EAClH;AACD,SAAOE;AACT;;;;;;ACXA,SAASG,GAAEC,IAAG;AACZ,SAAOA,MAAKA,GAAE,cAAc,OAAO,UAAU,eAAe,KAAKA,IAAG,SAAS,IAAIA,GAAE,UAAUA;AAC/F;;;ACFA,IAAI,IAAI,EAAE,SAAS,CAAC,EAAE;;;;ACOtB,GAAC,WAAY;AAGZ,QAAIC,KAAS,CAAE,EAAC;AAEhB,aAASC,KAAa;AAGrB,eAFIC,KAAU,CAAA,GAELC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAC1C,YAAIC,KAAM,UAAUD,EAAC;AACrB,YAAKC,IAEL;AAAA,cAAIC,KAAU,OAAOD;AAErB,cAAIC,OAAY,YAAYA,OAAY;AACvCH,YAAAA,GAAQ,KAAKE,EAAG;mBACN,MAAM,QAAQA,EAAG,GAAA;AAC3B,gBAAIA,GAAI,QAAQ;AACf,kBAAIE,KAAQL,GAAW,MAAM,MAAMG,EAAG;AAClCE,cAAAA,MACHJ,GAAQ,KAAKI,EAAK;YAEnB;UAAA,WACSD,OAAY;AACtB,gBAAID,GAAI,aAAa,OAAO,UAAU;AACrC,uBAASG,MAAOH;AACXJ,gBAAAA,GAAO,KAAKI,IAAKG,EAAG,KAAKH,GAAIG,EAAG,KACnCL,GAAQ,KAAKK,EAAG;;AAIlBL,cAAAA,GAAQ,KAAKE,GAAI,SAAU,CAAA;QAAA;MAG7B;AAED,aAAOF,GAAQ,KAAK,GAAG;IACvB;AAEoCM,IAAAA,GAAO,WAC3CP,GAAW,UAAUA,IACrBO,GAAA,UAAiBP,MAOjB,OAAO,aAAaA;EAEtB,GAAA;;;;;;ACxBU,IAACQ,KAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/HA,GAAK,UAAUA,GAAK,CAAC;;;;ACjClB,IAACC,SAA2BC,cAAAA,eAAc,CAAE,CAAA;;;ACA/C,SAASC,GAAQC,IAAGC,IAAG;AACrB,MAAIC,KAAI,OAAO,KAAKF,EAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAIG,KAAI,OAAO,sBAAsBH,EAAC;AACtCC,IAAAA,OAAME,KAAIA,GAAE,OAAO,SAAUF,IAAG;AAC9B,aAAO,OAAO,yBAAyBD,IAAGC,EAAC,EAAE;IACnD,CAAK,IAAIC,GAAE,KAAK,MAAMA,IAAGC,EAAC;EACvB;AACD,SAAOD;AACT;AACA,SAASE,GAAeJ,IAAG;AACzB,WAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,QAAIC,KAAY,UAAUD,EAAC,KAAnB,OAAuB,UAAUA,EAAC,IAAI,CAAA;AAC9CA,IAAAA,KAAI,IAAIF,GAAQ,OAAOG,EAAC,GAAG,IAAE,EAAE,QAAQ,SAAUD,IAAG;AAClDI,QAAeL,IAAGC,IAAGC,GAAED,EAAC,CAAC;IAC/B,CAAK,IAAI,OAAO,4BAA4B,OAAO,iBAAiBD,IAAG,OAAO,0BAA0BE,EAAC,CAAC,IAAIH,GAAQ,OAAOG,EAAC,CAAC,EAAE,QAAQ,SAAUD,IAAG;AAChJ,aAAO,eAAeD,IAAGC,IAAG,OAAO,yBAAyBC,IAAGD,EAAC,CAAC;IACvE,CAAK;EACF;AACD,SAAOD;AACT;;;;;;ACpBA,IAAMM,KAAQ,KAAK;AAYnB,SAASC,GAAcC,IAAKC,IAAU;AACpC,QAAMC,KAAQF,GAEb,QAAQ,gBAAgB,IAAI,EAE5B,QAAQ,QAAQ,EAAE,EAAE,MAAM,cAAc,KAAK,CAAA,GACxCG,KAAUD,GAAM,IAAI,CAAAE,OAAQ,WAAWA,EAAI,CAAC;AAClD,WAASC,KAAI,GAAGA,KAAI,GAAGA,MAAK;AAC1BF,IAAAA,GAAQE,EAAC,IAAIJ,GAASE,GAAQE,EAAC,KAAK,GAAGH,GAAMG,EAAC,KAAK,IAAIA,EAAC;AAI1D,SAAIH,GAAM,CAAC,IACTC,GAAQ,CAAC,IAAID,GAAM,CAAC,EAAE,SAAS,GAAG,IAAIC,GAAQ,CAAC,IAAI,MAAMA,GAAQ,CAAC,IAGlEA,GAAQ,CAAC,IAAI,GAERA;AACT;AACA,IAAMG,IAAgB,CAACC,IAAKC,IAAGC,OAAUA,OAAU,IAAIF,KAAMA,KAAM;AAGnE,SAASG,GAAWC,IAAOC,IAAK;AAC9B,QAAMC,KAAYD,MAAO;AACzB,SAAID,KAAQE,KACHA,KAELF,KAAQ,IACH,IAEFA;AACT;AACO,IAAMG,IAAN,MAAMA,GAAU;EACrB,YAAYC,IAAO;AAIjBC,MAAgB,MAAM,WAAW,IAAI,GAIrCA,EAAgB,MAAM,KAAK,CAAC,GAI5BA,EAAgB,MAAM,KAAK,CAAC,GAI5BA,EAAgB,MAAM,KAAK,CAAC,GAI5BA,EAAgB,MAAM,KAAK,CAAC,GAE5BA,EAAgB,MAAM,MAAM,MAAM,GAClCA,EAAgB,MAAM,MAAM,MAAM,GAClCA,EAAgB,MAAM,MAAM,MAAM,GAClCA,EAAgB,MAAM,MAAM,MAAM,GAElCA,EAAgB,MAAM,QAAQ,MAAM,GACpCA,EAAgB,MAAM,QAAQ,MAAM,GACpCA,EAAgB,MAAM,eAAe,MAAM;AAM3C,aAASC,GAAYjB,IAAK;AACxB,aAAOA,GAAI,CAAC,KAAKe,MAASf,GAAI,CAAC,KAAKe,MAASf,GAAI,CAAC,KAAKe;IACxD;AACD,QAAKA,GAEE,KAAI,OAAOA,MAAU,UAAU;AAEpC,UAASG,KAAT,SAAqBC,IAAQ;AAC3B,eAAOC,GAAQ,WAAWD,EAAM;MACjC;AAHD,YAAMC,KAAUL,GAAM,KAAA;AAIlB,0BAAoB,KAAKK,EAAO,IAClC,KAAK,cAAcA,EAAO,IACjBF,GAAY,KAAK,IAC1B,KAAK,cAAcE,EAAO,IACjBF,GAAY,KAAK,IAC1B,KAAK,cAAcE,EAAO,KACjBF,GAAY,KAAK,KAAKA,GAAY,KAAK,MAChD,KAAK,cAAcE,EAAO;IAElC,WAAeL,cAAiBD;AAC1B,WAAK,IAAIC,GAAM,GACf,KAAK,IAAIA,GAAM,GACf,KAAK,IAAIA,GAAM,GACf,KAAK,IAAIA,GAAM,GACf,KAAK,KAAKA,GAAM,IAChB,KAAK,KAAKA,GAAM,IAChB,KAAK,KAAKA,GAAM,IAChB,KAAK,KAAKA,GAAM;aACPE,GAAY,KAAK;AAC1B,WAAK,IAAIP,GAAWK,GAAM,CAAC,GAC3B,KAAK,IAAIL,GAAWK,GAAM,CAAC,GAC3B,KAAK,IAAIL,GAAWK,GAAM,CAAC,GAC3B,KAAK,IAAI,OAAOA,GAAM,KAAM,WAAWL,GAAWK,GAAM,GAAG,CAAC,IAAI;aACvDE,GAAY,KAAK;AAC1B,WAAK,QAAQF,EAAK;aACTE,GAAY,KAAK;AAC1B,WAAK,QAAQF,EAAK;;AAElB,YAAM,IAAI,MAAM,+CAA+C,KAAK,UAAUA,EAAK,CAAC;EAEvF;;EAID,KAAKJ,IAAO;AACV,WAAO,KAAK,IAAI,KAAKA,EAAK;EAC3B;EACD,KAAKA,IAAO;AACV,WAAO,KAAK,IAAI,KAAKA,EAAK;EAC3B;EACD,KAAKA,IAAO;AACV,WAAO,KAAK,IAAI,KAAKA,EAAK;EAC3B;EACD,KAAKA,IAAO;AACV,WAAO,KAAK,IAAI,KAAKA,IAAO,CAAC;EAC9B;EACD,OAAOA,IAAO;AACZ,UAAMU,KAAM,KAAK,MAAA;AACjB,WAAAA,GAAI,IAAIV,IACD,KAAK,GAAGU,EAAG;EACnB;;;;;;EAOD,eAAe;AACb,aAASC,GAAYC,IAAK;AACxB,YAAMC,KAAMD,KAAM;AAClB,aAAOC,MAAO,UAAUA,KAAM,QAAQ,KAAK,KAAKA,KAAM,SAAS,OAAO,GAAG;IAC1E;AACD,UAAMC,KAAIH,GAAY,KAAK,CAAC,GACtBI,KAAIJ,GAAY,KAAK,CAAC,GACtBK,KAAIL,GAAY,KAAK,CAAC;AAC5B,WAAO,SAASG,KAAI,SAASC,KAAI,SAASC;EAC3C;EACD,SAAS;AACP,QAAI,OAAO,KAAK,KAAO,KAAa;AAClC,YAAMC,KAAQ,KAAK,OAAQ,IAAG,KAAK,OAAM;AACrCA,MAAAA,OAAU,IACZ,KAAK,KAAK,IAEV,KAAK,KAAK9B,GAAM,MAAM,KAAK,MAAM,KAAK,OAAA,KAAY,KAAK,IAAI,KAAK,KAAK8B,MAAS,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,KAAK,OAAQ,KAAI,KAAK,IAAI,KAAK,KAAKA,KAAQ,KAAK,KAAK,IAAI,KAAK,KAAKA,KAAQ,EAAE;IAErM;AACD,WAAO,KAAK;EACb;EACD,gBAAgB;AACd,QAAI,OAAO,KAAK,KAAO,KAAa;AAClC,YAAMA,KAAQ,KAAK,OAAQ,IAAG,KAAK,OAAM;AACrCA,MAAAA,OAAU,IACZ,KAAK,KAAK,IAEV,KAAK,KAAKA,KAAQ,KAAK,OAAM;IAEhC;AACD,WAAO,KAAK;EACb;EACD,eAAe;AACb,WAAI,OAAO,KAAK,KAAO,QACrB,KAAK,MAAM,KAAK,OAAQ,IAAG,KAAK,OAAQ,KAAI,MAEvC,KAAK;EACb;EACD,WAAW;AACT,WAAI,OAAO,KAAK,KAAO,QACrB,KAAK,KAAK,KAAK,OAAM,IAAK,MAErB,KAAK;EACb;;;;;;EAOD,gBAAgB;AACd,WAAI,OAAO,KAAK,cAAgB,QAC9B,KAAK,eAAe,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,MAE7D,KAAK;EACb;;EAID,OAAOC,KAAS,IAAI;AAClB,UAAMC,KAAI,KAAK,OAAA,GACTC,KAAI,KAAK,cAAA;AACf,QAAIC,KAAI,KAAK,aAAY,IAAKH,KAAS;AACvC,WAAIG,KAAI,MACNA,KAAI,IAEC,KAAK,GAAG;MACb,GAAAF;MACA,GAAAC;MACA,GAAAC;MACA,GAAG,KAAK;IACd,CAAK;EACF;EACD,QAAQH,KAAS,IAAI;AACnB,UAAMC,KAAI,KAAK,OAAA,GACTC,KAAI,KAAK,cAAA;AACf,QAAIC,KAAI,KAAK,aAAY,IAAKH,KAAS;AACvC,WAAIG,KAAI,MACNA,KAAI,IAEC,KAAK,GAAG;MACb,GAAAF;MACA,GAAAC;MACA,GAAAC;MACA,GAAG,KAAK;IACd,CAAK;EACF;;;;;EAMD,IAAIjB,IAAOc,KAAS,IAAI;AACtB,UAAMI,KAAQ,KAAK,GAAGlB,EAAK,GACrBmB,KAAIL,KAAS,KACbM,KAAO,CAAAC,OAAQH,GAAMG,CAAG,IAAI,KAAKA,CAAG,KAAKF,KAAI,KAAKE,CAAG,GACrDC,KAAO;MACX,GAAGvC,GAAMqC,GAAK,GAAG,CAAC;MAClB,GAAGrC,GAAMqC,GAAK,GAAG,CAAC;MAClB,GAAGrC,GAAMqC,GAAK,GAAG,CAAC;MAClB,GAAGrC,GAAMqC,GAAK,GAAG,IAAI,GAAG,IAAI;IAClC;AACI,WAAO,KAAK,GAAGE,EAAI;EACpB;;;;;EAMD,KAAKR,KAAS,IAAI;AAChB,WAAO,KAAK,IAAI;MACd,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;IACJ,GAAEA,EAAM;EACV;;;;;EAMD,MAAMA,KAAS,IAAI;AACjB,WAAO,KAAK,IAAI;MACd,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;IACJ,GAAEA,EAAM;EACV;EACD,aAAaS,IAAY;AACvB,UAAMC,KAAK,KAAK,GAAGD,EAAU,GACvBE,KAAQ,KAAK,IAAID,GAAG,KAAK,IAAI,KAAK,IAClCJ,KAAO,CAAAC,OACJtC,IAAO,KAAKsC,EAAG,IAAI,KAAK,IAAIG,GAAGH,EAAG,IAAIG,GAAG,KAAK,IAAI,KAAK,MAAMC,EAAK;AAE3E,WAAO,KAAK,GAAG;MACb,GAAGL,GAAK,GAAG;MACX,GAAGA,GAAK,GAAG;MACX,GAAGA,GAAK,GAAG;MACX,GAAGK;IACT,CAAK;EACF;;EAGD,SAAS;AACP,WAAO,KAAK,cAAe,IAAG;EAC/B;EACD,UAAU;AACR,WAAO,KAAK,cAAe,KAAI;EAChC;;EAGD,OAAOC,IAAO;AACZ,WAAO,KAAK,MAAMA,GAAM,KAAK,KAAK,MAAMA,GAAM,KAAK,KAAK,MAAMA,GAAM,KAAK,KAAK,MAAMA,GAAM;EAC3F;EACD,QAAQ;AACN,WAAO,KAAK,GAAG,IAAI;EACpB;;EAGD,cAAc;AACZ,QAAIC,KAAM;AACV,UAAMC,MAAQ,KAAK,KAAK,GAAG,SAAS,EAAE;AACtCD,IAAAA,MAAOC,GAAK,WAAW,IAAIA,KAAO,MAAMA;AACxC,UAAMC,MAAQ,KAAK,KAAK,GAAG,SAAS,EAAE;AACtCF,IAAAA,MAAOE,GAAK,WAAW,IAAIA,KAAO,MAAMA;AACxC,UAAMC,MAAQ,KAAK,KAAK,GAAG,SAAS,EAAE;AAEtC,QADAH,MAAOG,GAAK,WAAW,IAAIA,KAAO,MAAMA,IACpC,OAAO,KAAK,KAAM,YAAY,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG;AAC3D,YAAMC,KAAOhD,GAAM,KAAK,IAAI,GAAG,EAAE,SAAS,EAAE;AAC5C4C,MAAAA,MAAOI,GAAK,WAAW,IAAIA,KAAO,MAAMA;IACzC;AACD,WAAOJ;EACR;;EAGD,QAAQ;AACN,WAAO;MACL,GAAG,KAAK,OAAQ;MAChB,GAAG,KAAK,cAAe;MACvB,GAAG,KAAK,aAAc;MACtB,GAAG,KAAK;IACd;EACG;;EAGD,cAAc;AACZ,UAAMZ,KAAI,KAAK,OAAA,GACTC,KAAIjC,GAAM,KAAK,cAAe,IAAG,GAAG,GACpCkC,KAAIlC,GAAM,KAAK,aAAc,IAAG,GAAG;AACzC,WAAO,KAAK,MAAM,IAAI,QAAQgC,EAAC,IAAIC,EAAC,KAAKC,EAAC,KAAK,KAAK,CAAC,MAAM,OAAOF,EAAC,IAAIC,EAAC,KAAKC,EAAC;EAC/E;;EAGD,QAAQ;AACN,WAAO;MACL,GAAG,KAAK,OAAQ;MAChB,GAAG,KAAK,cAAe;MACvB,GAAG,KAAK,SAAU;MAClB,GAAG,KAAK;IACd;EACG;EACD,QAAQ;AACN,WAAO;MACL,GAAG,KAAK;MACR,GAAG,KAAK;MACR,GAAG,KAAK;MACR,GAAG,KAAK;IACd;EACG;EACD,cAAc;AACZ,WAAO,KAAK,MAAM,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;EAC1G;EACD,WAAW;AACT,WAAO,KAAK,YAAA;EACb;;;EAID,IAAIe,IAAKpC,IAAOC,IAAK;AACnB,UAAMoC,KAAQ,KAAK,MAAA;AACnB,WAAAA,GAAMD,EAAG,IAAIrC,GAAWC,IAAOC,EAAG,GAC3BoC;EACR;EACD,GAAGjC,IAAO;AACR,WAAO,IAAI,KAAK,YAAYA,EAAK;EAClC;EACD,SAAS;AACP,WAAI,OAAO,KAAK,OAAS,QACvB,KAAK,OAAO,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAEtC,KAAK;EACb;EACD,SAAS;AACP,WAAI,OAAO,KAAK,OAAS,QACvB,KAAK,OAAO,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAEtC,KAAK;EACb;EACD,cAAcK,IAAS;AACrB,UAAM6B,KAAgB7B,GAAQ,QAAQ,KAAK,EAAE;AAC7C,aAAS8B,GAAWC,IAAQC,IAAQ;AAClC,aAAO,SAASH,GAAcE,EAAM,IAAIF,GAAcG,MAAUD,EAAM,GAAG,EAAE;IAC5E;AACGF,IAAAA,GAAc,SAAS,KAEzB,KAAK,IAAIC,GAAW,CAAC,GACrB,KAAK,IAAIA,GAAW,CAAC,GACrB,KAAK,IAAIA,GAAW,CAAC,GACrB,KAAK,IAAID,GAAc,CAAC,IAAIC,GAAW,CAAC,IAAI,MAAM,MAGlD,KAAK,IAAIA,GAAW,GAAG,CAAC,GACxB,KAAK,IAAIA,GAAW,GAAG,CAAC,GACxB,KAAK,IAAIA,GAAW,GAAG,CAAC,GACxB,KAAK,IAAID,GAAc,CAAC,IAAIC,GAAW,GAAG,CAAC,IAAI,MAAM;EAExD;EACD,QAAQ;IACN,GAAApB;IACA,GAAAC;IACA,GAAAC;IACA,GAAAqB;EACJ,GAAK;AAKD,QAJA,KAAK,KAAKvB,KAAI,KACd,KAAK,KAAKC,IACV,KAAK,KAAKC,IACV,KAAK,IAAI,OAAOqB,MAAM,WAAWA,KAAI,GACjCtB,MAAK,GAAG;AACV,YAAMgB,IAAMjD,GAAMkC,KAAI,GAAG;AACzB,WAAK,IAAIe,GACT,KAAK,IAAIA,GACT,KAAK,IAAIA;IACV;AACD,QAAIO,KAAI,GACNC,KAAI,GACJC,IAAI;AACN,UAAMC,KAAW3B,KAAI,IACf4B,MAAU,IAAI,KAAK,IAAI,IAAI1B,KAAI,CAAC,KAAKD,IACrC4B,KAAkBD,MAAU,IAAI,KAAK,IAAID,KAAW,IAAI,CAAC;AAC3DA,IAAAA,MAAY,KAAKA,KAAW,KAC9BH,KAAII,IACJH,KAAII,MACKF,MAAY,KAAKA,KAAW,KACrCH,KAAIK,IACJJ,KAAIG,MACKD,MAAY,KAAKA,KAAW,KACrCF,KAAIG,IACJF,IAAIG,MACKF,MAAY,KAAKA,KAAW,KACrCF,KAAII,IACJH,IAAIE,MACKD,MAAY,KAAKA,KAAW,KACrCH,KAAIK,IACJH,IAAIE,MACKD,MAAY,KAAKA,KAAW,MACrCH,KAAII,IACJF,IAAIG;AAEN,UAAMC,KAAwB5B,KAAI0B,KAAS;AAC3C,SAAK,IAAI5D,IAAOwD,KAAIM,MAAyB,GAAG,GAChD,KAAK,IAAI9D,IAAOyD,KAAIK,MAAyB,GAAG,GAChD,KAAK,IAAI9D,IAAO0D,IAAII,MAAyB,GAAG;EACjD;EACD,QAAQ;IACN,GAAA9B;IACA,GAAAC;IACA,GAAA8B;IACA,GAAAR;EACJ,GAAK;AACD,SAAK,KAAKvB,KAAI,KACd,KAAK,KAAKC,IACV,KAAK,KAAK8B,IACV,KAAK,IAAI,OAAOR,MAAM,WAAWA,KAAI;AACrC,UAAMS,KAAKhE,GAAM+D,KAAI,GAAG;AAIxB,QAHA,KAAK,IAAIC,IACT,KAAK,IAAIA,IACT,KAAK,IAAIA,IACL/B,MAAK;AACP;AAEF,UAAMgC,KAAKjC,KAAI,IACTzB,IAAI,KAAK,MAAM0D,EAAE,GACjBC,KAAKD,KAAK1D,GACV6B,KAAIpC,GAAM+D,MAAK,IAAM9B,MAAK,GAAG,GAC7BkC,KAAInE,GAAM+D,MAAK,IAAM9B,KAAIiC,MAAM,GAAG,GAClCE,KAAIpE,GAAM+D,MAAK,IAAM9B,MAAK,IAAMiC,OAAO,GAAG;AAChD,YAAQ3D,GAAC;MACP,KAAK;AACH,aAAK,IAAI6D,IACT,KAAK,IAAIhC;AACT;MACF,KAAK;AACH,aAAK,IAAI+B,IACT,KAAK,IAAI/B;AACT;MACF,KAAK;AACH,aAAK,IAAIA,IACT,KAAK,IAAIgC;AACT;MACF,KAAK;AACH,aAAK,IAAIhC,IACT,KAAK,IAAI+B;AACT;MACF,KAAK;AACH,aAAK,IAAIC,IACT,KAAK,IAAIhC;AACT;MACF,KAAK;MACL;AACE,aAAK,IAAIA,IACT,KAAK,IAAI+B;AACT;IACH;EACF;EACD,cAAc7C,IAAS;AACrB,UAAM+C,KAAQpE,GAAcqB,IAASd,CAAa;AAClD,SAAK,QAAQ;MACX,GAAG6D,GAAM,CAAC;MACV,GAAGA,GAAM,CAAC;MACV,GAAGA,GAAM,CAAC;MACV,GAAGA,GAAM,CAAC;IAChB,CAAK;EACF;EACD,cAAc/C,IAAS;AACrB,UAAM+C,KAAQpE,GAAcqB,IAASd,CAAa;AAClD,SAAK,QAAQ;MACX,GAAG6D,GAAM,CAAC;MACV,GAAGA,GAAM,CAAC;MACV,GAAGA,GAAM,CAAC;MACV,GAAGA,GAAM,CAAC;IAChB,CAAK;EACF;EACD,cAAc/C,IAAS;AACrB,UAAM+C,KAAQpE,GAAcqB,IAAS,CAACb,IAAK6D;;MAE3CA,GAAI,SAAS,GAAG,IAAItE,GAAMS,KAAM,MAAM,GAAG,IAAIA;KAAG;AAChD,SAAK,IAAI4D,GAAM,CAAC,GAChB,KAAK,IAAIA,GAAM,CAAC,GAChB,KAAK,IAAIA,GAAM,CAAC,GAChB,KAAK,IAAIA,GAAM,CAAC;EACjB;AACH;;;ACnhBA,IAAIE,KAAU;AAAd,IACIC,KAAiB;AADrB,IAEIC,IAAkB;AAFtB,IAGIC,KAAkB;AAHtB,IAIIC,IAAkB;AAJtB,IAKIC,IAAkB;AALtB,IAMIC,IAAiB;AANrB,IASIC,IAAe,CAAC;EAClB,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,GAAG;EACD,OAAO;EACP,QAAQ;AACV,CAAC;AACD,SAASC,GAAOC,IAAKC,IAAGC,IAAO;AAC7B,MAAIC;AAEJ,SAAI,KAAK,MAAMH,GAAI,CAAC,KAAK,MAAM,KAAK,MAAMA,GAAI,CAAC,KAAK,MAClDG,KAAMD,KAAQ,KAAK,MAAMF,GAAI,CAAC,IAAIT,KAAUU,KAAI,KAAK,MAAMD,GAAI,CAAC,IAAIT,KAAUU,KAE9EE,KAAMD,KAAQ,KAAK,MAAMF,GAAI,CAAC,IAAIT,KAAUU,KAAI,KAAK,MAAMD,GAAI,CAAC,IAAIT,KAAUU,IAE5EE,KAAM,IACRA,MAAO,MACEA,MAAO,QAChBA,MAAO,MAEFA;AACT;AACA,SAASC,GAAcJ,IAAKC,IAAGC,IAAO;AAEpC,MAAIF,GAAI,MAAM,KAAKA,GAAI,MAAM;AAC3B,WAAOA,GAAI;AAEb,MAAIK;AACJ,SAAIH,KACFG,KAAaL,GAAI,IAAIR,KAAiBS,KAC7BA,OAAMJ,IACfQ,KAAaL,GAAI,IAAIR,KAErBa,KAAaL,GAAI,IAAIP,IAAkBQ,IAGrCI,KAAa,MACfA,KAAa,IAGXH,MAASD,OAAML,KAAmBS,KAAa,QACjDA,KAAa,MAEXA,KAAa,SACfA,KAAa,OAER,KAAK,MAAMA,KAAa,GAAG,IAAI;AACxC;AACA,SAASC,EAASN,IAAKC,IAAGC,IAAO;AAC/B,MAAIK;AACJ,SAAIL,KACFK,KAAQP,GAAI,IAAIN,KAAkBO,KAElCM,KAAQP,GAAI,IAAIL,IAAkBM,IAGpCM,KAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAGA,EAAK,CAAC,GAC/B,KAAK,MAAMA,KAAQ,GAAG,IAAI;AACnC;AACe,SAASC,EAASC,IAAO;AAKtC,WAJIC,KAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA,GAC3EC,KAAW,CAAA,GACXC,KAAS,IAAIC,EAAUJ,EAAK,GAC5BT,KAAMY,GAAO,MAAA,GACRX,KAAIL,GAAiBK,KAAI,GAAGA,MAAK,GAAG;AAC3C,QAAIa,KAAI,IAAID,EAAU;MACpB,GAAGd,GAAOC,IAAKC,IAAG,IAAI;MACtB,GAAGG,GAAcJ,IAAKC,IAAG,IAAI;MAC7B,GAAGK,EAASN,IAAKC,IAAG,IAAI;IAC9B,CAAK;AACDU,IAAAA,GAAS,KAAKG,EAAC;EAChB;AACDH,EAAAA,GAAS,KAAKC,EAAM;AACpB,WAASG,KAAK,GAAGA,MAAMlB,GAAgBkB,MAAM,GAAG;AAC9C,QAAIC,IAAK,IAAIH,EAAU;MACrB,GAAGd,GAAOC,IAAKe,EAAE;MACjB,GAAGX,GAAcJ,IAAKe,EAAE;MACxB,GAAGT,EAASN,IAAKe,EAAE;IACzB,CAAK;AACDJ,IAAAA,GAAS,KAAKK,CAAE;EACjB;AAGD,SAAIN,GAAK,UAAU,SACVZ,EAAa,IAAI,SAAUmB,IAAM;AACtC,QAAIC,KAAQD,GAAK,OACfE,KAASF,GAAK;AAChB,WAAO,IAAIJ,EAAUH,GAAK,mBAAmB,SAAS,EAAE,IAAIC,GAASO,EAAK,GAAGC,EAAM,EAAE,YAAW;EACtG,CAAK,IAEIR,GAAS,IAAI,SAAUG,IAAG;AAC/B,WAAOA,GAAE,YAAA;EACb,CAAG;AACH;;;AC/He,SAASM,KAAY;AAClC,SAAO,CAAC,EAAE,OAAO,SAAW,OAAe,OAAO,YAAY,OAAO,SAAS;AAChF;;;ACFe,SAASC,GAASC,IAAMC,IAAG;AACxC,MAAI,CAACD;AACH,WAAO;AAIT,MAAIA,GAAK;AACP,WAAOA,GAAK,SAASC,EAAC;AAKxB,WADIC,KAAOD,IACJC,MAAM;AACX,QAAIA,OAASF;AACX,aAAO;AAETE,IAAAA,KAAOA,GAAK;EACb;AACD,SAAO;AACT;;;AChBA,IAAIC,KAAe;AAAnB,IACIC,IAAkB;AADtB,IAEIC,IAAW;AAFf,IAGIC,IAAiB,oBAAI,IAAA;AACzB,SAASC,KAAU;AACjB,MAAIC,KAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAE,GAC/EC,KAAOD,GAAK;AACd,SAAIC,KACKA,GAAK,WAAW,OAAO,IAAIA,KAAO,QAAQ,OAAOA,EAAI,IAEvDJ;AACT;AACA,SAASK,GAAaC,IAAQ;AAC5B,MAAIA,GAAO;AACT,WAAOA,GAAO;AAEhB,MAAIC,KAAO,SAAS,cAAc,MAAM;AACxC,SAAOA,MAAQ,SAAS;AAC1B;AACA,SAASC,EAASC,IAAS;AACzB,SAAIA,OAAY,UACP,iBAEFA,KAAU,YAAY;AAC/B;AAKA,SAASC,GAAWC,IAAW;AAC7B,SAAO,MAAM,MAAMV,EAAe,IAAIU,EAAS,KAAKA,IAAW,QAAQ,EAAE,OAAO,SAAUC,IAAM;AAC9F,WAAOA,GAAK,YAAY;EAC5B,CAAG;AACH;AACO,SAASC,EAAUC,IAAK;AAC7B,MAAIR,KAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA;AACjF,MAAI,CAACS,GAAS;AACZ,WAAO;AAET,MAAIC,KAAMV,GAAO,KACfG,KAAUH,GAAO,SACjBW,KAAmBX,GAAO,UAC1BY,KAAWD,OAAqB,SAAS,IAAIA,IAC3CE,KAAcX,EAASC,EAAO,GAC9BW,KAAiBD,OAAgB,gBACjCE,KAAY,SAAS,cAAc,OAAO;AAC9CA,EAAAA,GAAU,aAAavB,IAAcqB,EAAW,GAC5CC,MAAkBF,MACpBG,GAAU,aAAatB,GAAiB,GAAG,OAAOmB,EAAQ,CAAC,GAEzDF,MAAQ,QAA0BA,GAAI,UACxCK,GAAU,QAAQL,MAAQ,OAAyB,SAASA,GAAI,QAElEK,GAAU,YAAYP;AACtB,MAAIH,KAAYN,GAAaC,EAAM,GAC/BgB,KAAaX,GAAU;AAC3B,MAAIF,IAAS;AAEX,QAAIW,IAAgB;AAClB,UAAIG,MAAcjB,GAAO,UAAUI,GAAWC,EAAS,GAAG,OAAO,SAAUC,GAAM;AAE/E,YAAI,CAAC,CAAC,WAAW,cAAc,EAAE,SAASA,EAAK,aAAad,EAAY,CAAC;AACvE,iBAAO;AAIT,YAAI0B,KAAe,OAAOZ,EAAK,aAAab,CAAe,KAAK,CAAC;AACjE,eAAOmB,MAAYM;MAC3B,CAAO;AACD,UAAID,GAAW;AACb,eAAAZ,GAAU,aAAaU,IAAWE,GAAWA,GAAW,SAAS,CAAC,EAAE,WAAW,GACxEF;IAEV;AAGDV,IAAAA,GAAU,aAAaU,IAAWC,EAAU;EAChD;AACIX,IAAAA,GAAU,YAAYU,EAAS;AAEjC,SAAOA;AACT;AACA,SAASI,EAAcC,IAAK;AAC1B,MAAIpB,KAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA,GAC7EK,KAAYN,GAAaC,EAAM;AACnC,UAAQA,GAAO,UAAUI,GAAWC,EAAS,GAAG,KAAK,SAAUC,IAAM;AACnE,WAAOA,GAAK,aAAaV,GAAQI,EAAM,CAAC,MAAMoB;EAClD,CAAG;AACH;AAaA,SAASC,EAAkBhB,IAAWL,IAAQ;AAC5C,MAAIsB,KAAsB3B,EAAe,IAAIU,EAAS;AAGtD,MAAI,CAACiB,MAAuB,CAACC,GAAS,UAAUD,EAAmB,GAAG;AACpE,QAAIE,KAAmBjB,EAAU,IAAIP,EAAM,GACvCyB,KAAaD,GAAiB;AAClC7B,MAAe,IAAIU,IAAWoB,EAAU,GACxCpB,GAAU,YAAYmB,EAAgB;EACvC;AACH;AAQO,SAASE,EAAUlB,IAAKY,IAAK;AAClC,MAAIO,KAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA,GACnFtB,KAAYN,GAAa4B,EAAY,GACrCC,KAASxB,GAAWC,EAAS,GAC7BL,KAAS6B,GAAcA,GAAc,CAAA,GAAIF,EAAY,GAAG,CAAA,GAAI;IAC9D,QAAQC;EACZ,CAAG;AAGDP,IAAkBhB,IAAWL,EAAM;AACnC,MAAI8B,KAAYX,EAAcC,IAAKpB,EAAM;AACzC,MAAI8B,IAAW;AACb,QAAIC,IAAaC;AACjB,SAAKD,KAAc/B,GAAO,SAAS,QAAQ+B,OAAgB,UAAUA,GAAY,SAASD,GAAU,YAAYE,KAAehC,GAAO,SAAS,QAAQgC,OAAiB,SAAS,SAASA,GAAa,QAAQ;AAC7M,UAAIC;AACJH,MAAAA,GAAU,SAASG,KAAejC,GAAO,SAAS,QAAQiC,OAAiB,SAAS,SAASA,GAAa;IAC3G;AACD,WAAIH,GAAU,cAActB,OAC1BsB,GAAU,YAAYtB,KAEjBsB;EACR;AACD,MAAII,KAAU3B,EAAUC,IAAKR,EAAM;AACnC,SAAAkC,GAAQ,aAAatC,GAAQI,EAAM,GAAGoB,EAAG,GAClCc;AACT;;;ACnJA,SAASC,GAAQC,IAAK;AACpB,MAAIC;AACJ,SAAOD,MAAQ,SAA2BC,KAAmBD,GAAI,iBAAiB,QAAQC,OAAqB,SAAS,SAASA,GAAiB,KAAKD,EAAG;AAC5J;AAKO,SAASE,GAASF,IAAK;AAC5B,SAAOD,GAAQC,EAAG,aAAa;AACjC;AAKO,SAASG,GAAcH,IAAK;AACjC,SAAOE,GAASF,EAAG,IAAID,GAAQC,EAAG,IAAI;AACxC;;;AChBA,IAAII,KAAS,CAAA;AAAb,IACIC,KAAgB,CAAA;AADpB,IAOWC,KAAa,SAAoBC,IAAI;AAC9CF,EAAAA,GAAc,KAAKE,EAAE;AACvB;AAaO,SAASC,GAAQC,IAAOC,IAAS;AACtC,MAA6C,CAACD,MAAS,YAAY,QAAW;AAC5E,QAAIE,KAAeN,GAAc,OAAO,SAAUO,IAAKC,IAAc;AACnE,aAAOA,GAAaD,MAAuC,IAAI,SAAS;IACzE,GAAEF,EAAO;AACNC,IAAAA,MACF,QAAQ,MAAM,YAAY,OAAOA,EAAY,CAAC;EAEjD;AACH;AAGO,SAASG,GAAKL,IAAOC,IAAS;AACnC,MAA6C,CAACD,MAAS,YAAY,QAAW;AAC5E,QAAIE,KAAeN,GAAc,OAAO,SAAUO,IAAKC,IAAc;AACnE,aAAOA,GAAaD,MAAuC,IAAI,MAAM;IACtE,GAAEF,EAAO;AACNC,IAAAA,MACF,QAAQ,KAAK,SAAS,OAAOA,EAAY,CAAC;EAE7C;AACH;AACO,SAASI,KAAc;AAC5BX,EAAAA,KAAS,CAAA;AACX;AACO,SAASY,GAAKC,IAAQR,IAAOC,IAAS;AACvC,GAACD,MAAS,CAACL,GAAOM,EAAO,MAC3BO,GAAO,OAAOP,EAAO,GACrBN,GAAOM,EAAO,IAAI;AAEtB;AAGO,SAASQ,GAAYT,IAAOC,IAAS;AAC1CM,EAAAA,GAAKR,IAASC,IAAOC,EAAO;AAC9B;AAGO,SAASS,GAASV,IAAOC,IAAS;AACvCM,EAAAA,GAAKF,IAAML,IAAOC,EAAO;AAC3B;AACAQ,GAAY,aAAaZ;AACzBY,GAAY,cAAcH;AAC1BG,GAAY,WAAWC;;;;AC1DvB,SAASC,EAAUC,IAAO;AACxB,SAAOA,GAAM,QAAQ,SAAS,SAAUC,IAAOC,IAAG;AAChD,WAAOA,GAAE,YAAA;EACb,CAAG;AACH;AACO,SAASC,EAAQC,IAAOC,IAAS;AACtCC,EAAAA,GAAKF,IAAO,uBAAuB,OAAOC,EAAO,CAAC;AACpD;AACO,SAASE,EAAiBC,IAAQ;AACvC,SAAOC,GAAQD,EAAM,MAAM,YAAY,OAAOA,GAAO,QAAS,YAAY,OAAOA,GAAO,SAAU,aAAaC,GAAQD,GAAO,IAAI,MAAM,YAAY,OAAOA,GAAO,QAAS;AAC7K;AACO,SAASE,KAAiB;AAC/B,MAAIC,KAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA;AAChF,SAAO,OAAO,KAAKA,EAAK,EAAE,OAAO,SAAUC,IAAKC,IAAK;AACnD,QAAIC,KAAMH,GAAME,EAAG;AACnB,YAAQA,IAAG;MACT,KAAK;AACHD,QAAAA,GAAI,YAAYE,IAChB,OAAOF,GAAI;AACX;MACF;AACE,eAAOA,GAAIC,EAAG,GACdD,GAAIb,EAAUc,EAAG,CAAC,IAAIC;IACzB;AACD,WAAOF;EACR,GAAE,CAAE,CAAA;AACP;AACO,SAASG,GAASC,IAAMH,IAAKI,IAAW;AAC7C,SAAKA,KAOeC,cAAAA,QAAM,cAAcF,GAAK,KAAKG,GAAcA,GAAc;IAC5E,KAAKN;EACN,GAAEH,GAAeM,GAAK,KAAK,CAAC,GAAGC,EAAS,IAAID,GAAK,YAAY,CAAE,GAAE,IAAI,SAAUI,IAAOC,IAAO;AAC5F,WAAON,GAASK,IAAO,GAAG,OAAOP,IAAK,GAAG,EAAE,OAAOG,GAAK,KAAK,GAAG,EAAE,OAAOK,EAAK,CAAC;EAC/E,CAAA,CAAC,IAVoBH,cAAAA,QAAM,cAAcF,GAAK,KAAKG,GAAc;IAC9D,KAAKN;EACN,GAAEH,GAAeM,GAAK,KAAK,CAAC,IAAIA,GAAK,YAAY,CAAA,GAAI,IAAI,SAAUI,IAAOC,IAAO;AAChF,WAAON,GAASK,IAAO,GAAG,OAAOP,IAAK,GAAG,EAAE,OAAOG,GAAK,KAAK,GAAG,EAAE,OAAOK,EAAK,CAAC;EAC/E,CAAA,CAAC;AAON;AACO,SAASC,GAAkBC,IAAc;AAE9C,SAAOC,EAAcD,EAAY,EAAE,CAAC;AACtC;AACO,SAASE,EAAuBC,IAAc;AACnD,SAAKA,KAGE,MAAM,QAAQA,EAAY,IAAIA,KAAe,CAACA,EAAY,IAFxD,CAAA;AAGX;AAWU,IAACC,KAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAd,IACCC,IAAkB,SAAyBC,IAAQ;AAC5D,MAAIC,SAAcC,cAAAA,YAAWC,EAAW,GACtCC,KAAMH,GAAY,KAClBI,KAAYJ,GAAY,WACxBK,KAAQL,GAAY,OAClBM,KAAiBT;AACjBO,EAAAA,OACFE,KAAiBA,GAAe,QAAQ,YAAYF,EAAS,IAE3DC,OACFC,KAAiB,UAAU,OAAOD,IAAO;CAAM,EAAE,OAAOC,IAAgB;EAAK,QAE/EC,cAAAA,WAAU,WAAY;AACpB,QAAIC,KAAMT,GAAO,SACbU,KAAaC,GAAcF,EAAG;AAClCG,MAAUL,IAAgB,qBAAqB;MAC7C,SAAS,CAACD;MACV,KAAKF;MACL,UAAUM;IAChB,CAAK;EACF,GAAE,CAAE,CAAA;AACP;;;ACzFA,IAAIG,IAAY,CAAC,QAAQ,aAAa,WAAW,SAAS,gBAAgB,gBAAgB;AAA1F,IAGIC,KAAsB;EACxB,cAAc;EACd,gBAAgB;EAChB,YAAY;AACd;AACA,SAASC,GAAiBC,IAAM;AAC9B,MAAIC,KAAeD,GAAK,cACtBE,KAAiBF,GAAK;AACxBF,EAAAA,GAAoB,eAAeG,IACnCH,GAAoB,iBAAiBI,MAAkBC,GAAkBF,EAAY,GACrFH,GAAoB,aAAa,CAAC,CAACI;AACrC;AACA,SAASE,KAAmB;AAC1B,SAAOC,GAAc,CAAA,GAAIP,EAAmB;AAC9C;AACG,IAACQ,KAAW,SAAkBC,IAAO;AACtC,MAAIC,KAAOD,GAAM,MACfE,KAAYF,GAAM,WAClBG,KAAUH,GAAM,SAChBI,KAAQJ,GAAM,OACdN,KAAeM,GAAM,cACrBL,KAAiBK,GAAM,gBACvBK,IAAYC,EAAyBN,IAAOV,CAAS,GACnDiB,KAAe,UAAA,GACfC,KAASjB;AASb,MARIG,OACFc,KAAS;IACP,cAAcd;IACd,gBAAgBC,MAAkBC,GAAkBF,EAAY;EACtE,IAEEe,EAAgBF,EAAM,GACtBG,EAAQC,EAAiBV,EAAI,GAAG,0CAA0C,OAAOA,EAAI,CAAC,GAClF,CAACU,EAAiBV,EAAI;AACxB,WAAO;AAET,MAAIW,KAASX;AACb,SAAIW,MAAU,OAAOA,GAAO,QAAS,eACnCA,KAASd,GAAcA,GAAc,CAAE,GAAEc,EAAM,GAAG,CAAA,GAAI;IACpD,MAAMA,GAAO,KAAKJ,GAAO,cAAcA,GAAO,cAAc;EAClE,CAAK,IAEIK,GAASD,GAAO,MAAM,OAAO,OAAOA,GAAO,IAAI,GAAGd,GAAcA,GAAc;IACnF,WAAWI;IACX,SAASC;IACT,OAAOC;IACP,aAAaQ,GAAO;IACpB,OAAO;IACP,QAAQ;IACR,MAAM;IACN,eAAe;EACnB,GAAKP,CAAS,GAAG,CAAA,GAAI;IACjB,KAAKE;EACN,CAAA,CAAC;AACJ;AACAR,GAAS,cAAc;AACvBA,GAAS,mBAAmBF;AAC5BE,GAAS,mBAAmBP;;;AC3DrB,SAASsB,GAAgBC,IAAc;AAC5C,MAAIC,KAAwBC,EAAuBF,EAAY,GAC7DG,KAAyBC,GAAeH,IAAuB,CAAC,GAChEI,KAAeF,GAAuB,CAAC,GACvCG,KAAiBH,GAAuB,CAAC;AAC3C,SAAOI,GAAU,iBAAiB;IAChC,cAAcF;IACd,gBAAgBC;EACpB,CAAG;AACH;AACO,SAASE,KAAkB;AAChC,MAAIC,KAASF,GAAU,iBAAA;AACvB,SAAKE,GAAO,aAGL,CAACA,GAAO,cAAcA,GAAO,cAAc,IAFzCA,GAAO;AAGlB;;;ACbA,IAAIC,KAAY,CAAC,aAAa,QAAQ,QAAQ,UAAU,YAAY,WAAW,cAAc;AAU7FC,GAAgBC,GAAK,OAAO;AAIzB,IAACC,KAA0B,cAAW,SAAUC,IAAOC,IAAK;AAC7D,MAAIC,KAAYF,GAAM,WACpBG,KAAOH,GAAM,MACbI,KAAOJ,GAAM,MACbK,KAASL,GAAM,QACfM,KAAWN,GAAM,UACjBO,KAAUP,GAAM,SAChBQ,KAAeR,GAAM,cACrBS,KAAYC,EAAyBV,IAAOJ,EAAS,GACnDe,KAA0B,cAAWC,EAAO,GAC9CC,KAAwBF,GAAkB,WAC1CG,KAAYD,OAA0B,SAAS,YAAYA,IAC3DE,KAAgBJ,GAAkB,eAChCK,KAAcC,EAAWF,IAAeD,IAAWI,EAAgBA,EAAgB,CAAA,GAAI,GAAG,OAAOJ,IAAW,GAAG,EAAE,OAAOX,GAAK,IAAI,GAAG,CAAC,CAACA,GAAK,IAAI,GAAG,GAAG,OAAOW,IAAW,OAAO,GAAG,CAAC,CAACV,MAAQD,GAAK,SAAS,SAAS,GAAGD,EAAS,GAC9NiB,KAAeb;AACfa,EAAAA,OAAiB,UAAaZ,OAChCY,KAAe;AAEjB,MAAIC,KAAWf,KAAS;IACtB,aAAa,UAAU,OAAOA,IAAQ,MAAM;IAC5C,WAAW,UAAU,OAAOA,IAAQ,MAAM;EAC3C,IAAG,QACAgB,IAAwBC,EAAuBd,EAAY,GAC7De,KAAyBC,GAAeH,GAAuB,CAAC,GAChEI,KAAeF,GAAuB,CAAC,GACvCG,IAAiBH,GAAuB,CAAC;AAC3C,SAA0B,iBAAc,QAAQI,EAAS;IACvD,MAAM;IACN,cAAcxB,GAAK;EACpB,GAAEM,IAAW;IACZ,KAAKR;IACL,UAAUkB;IACV,SAASZ;IACT,WAAWS;EACf,CAAG,GAAsB,iBAAcY,IAAW;IAC9C,MAAMzB;IACN,cAAcsB;IACd,gBAAgBC;IAChB,OAAON;EACR,CAAA,CAAC;AACJ,CAAC;AACDrB,GAAK,cAAc;AACnBA,GAAK,kBAAkB8B;AACvB9B,GAAK,kBAAkBF;;;ACxDvB,IAAIiC,KAAgB,SAAuBC,IAAOC,IAAK;AACrD,SAA0B,iBAAcC,IAAUC,EAAS,CAAA,GAAIH,IAAO;IACpE,KAAKC;IACL,MAAMG;EACP,CAAA,CAAC;AACJ;AALA,IAQIC,KAA6B,cAAWN,EAAa;AAEvDM,GAAQ,cAAc;;;ACLxB,IAAM,EAAE,QAAAC,EAAW,IAAAC,YAAAA;AAAnB,IAUMC,KAAkD;EACtD,MAAMC;EACN,UAAUC;EACV,YAAYC;AACd;AAdA,IA6BMC,IAAkD,CAAC;EACvD,cAAAC;EACA,OAAAC;EACA,SAAAC,KAAU,CAAC;EACX,UAAAC;EACA,OAAAC,KAAQ,EAAE,OAAO,OAAO;EACxB,YAAAC,KAAa;EACb,qBAAAC,KAAsB;EACtB,UAAAC,KAAW;EACX,iBAAAC,KAAkB;EAClB,YAAAC,KAAa;AACf,MAAM;AACE,QAAA,CAACC,IAAeC,EAAgB,QAAIC,cAAAA;IACxCX,MAASD,MAAgB,CAAC;EAAA;AAG5Ba,oBAAAA,WAAU,MAAM;AACVZ,IAAAA,OAAU,UACZU,GAAiBV,EAAK;EACxB,GACC,CAACA,EAAK,CAAC;AAEV,QAAMa,SAAgBC,cAAAA;IACpB,CAACC,OAAwB;AACjB,YAAA,EAAE,QAAAC,IAAQ,MAAAC,GAAS,IAAAF;AACzB,UAAI,CAACE,MAAQD,GAAO,OAAOC,GAAK,GAAI;AAE9B,YAAAC,KAAmBT,GAAc,MAAA,GACjCU,KAAWD,GAAiB,QAAQF,GAAO,EAAY,GACvDI,KAAWF,GAAiB,QAAQD,GAAK,EAAY;AAEvD,UAAAE,OAAa,MAAMC,OAAa,IAAI;AAChC,cAAAC,KAAoB,CAAC,GAAGH,EAAgB;AAC5BG,QAAAA,GAAA,OAAOF,IAAU,CAAC,GACpCE,GAAkB,OAAOD,IAAU,GAAGJ,GAAO,EAAY,GAEzDN,GAAiBW,EAAiB,GAClCnB,MAAA,QAAAA,GAAWmB,IAAmBpB,EAAAA;MAChC;IACF;IACA,CAACQ,IAAeP,IAAUD,EAAO;EAAA,GAG7BqB,KAAwB,CAACC,OAA0B;AACvDb,IAAAA,GAAiBa,EAAW,GAC5BrB,MAAA,QAAAA,GAAWqB,IAAatB,EAAAA;EAAO,GAG3BuB,KAAqB,CAACC,IAAeC,OAAkC;AAC3E,QAAI,CAACtB,MAAc,CAACsB,GAAe,QAAA;AAEnC,UAAMC,KAAStB,KAAsBoB,KAAQA,GAAM,YAAY;AAKxD,YAJOpB,KACVqB,GAAO,WACPA,GAAO,SAAS,YAAA,GAEP,SAASC,EAAM;EAAA,GAIxBC,KAAU,CAAC,EAAE,OAAAC,IAAO,OAAA7B,IAAO,UAAA8B,IAAU,SAAAC,GAAAA,MAAmB;AACtD,UAAA;MACJ,YAAAC;MACA,WAAAC;MACA,YAAAC;MACA,WAAAC;MACA,YAAAC;MACA,YAAAC;IAAA,IACEC,YAAY;MACd,IAAItC;MACJ,UAAAM;IAAA,CACD,GAEKiC,KAAgC;MACpC,SAASF,KAAa,MAAM;MAC5B,WAAWF,IACP,eAAeA,EAAU,CAAC,OAAOA,EAAU,CAAC,WAC5C;MACJ,YAAAC;MACA,QAAQ9B,KAAW,gBAAgB;MACnC,aAAa;MACb,SAAS;IAAA;AAIT,eAAAkC,mBAAAA;MAAC;MAAA;QACC,KAAKN;QACL,OAAOK;QACP,aAAa,CAACE,OAAMA,GAAE,gBAAgB;QACtC,OAAOZ;QAEP,cAAAa,mBAAAA,MAAC,QAAK,EAAA,WAAU,6BACd,UAAA;cAAAF,mBAAAA;YAAC;YAAA;cACC,WAAU;cACT,GAAGR;cACH,GAAGC;cAEH,UAAAJ;YAAA;UACH;UACCC,UACCU,mBAAAA;YAAC;YAAA;cACC,WAAU;cACV,SAAST;cACT,eAAY;cAEZ,cAAA,mBAAAY,KAACC,IAAc,CAAA,CAAA;YAAA;UACjB;QAAA,EAAA,CAEJ;MAAA;IAAA;EACF;AAKF,aAAAJ,mBAAAA,KAACK,YAAW,EAAA,WAAWhC,IACrB,cAAA2B,mBAAAA;IAACM;IAAA;MACC,OAAOrC;MACP,UAAUf,GAAkBa,EAAe;MAE3C,cAAAiC,mBAAAA;QAAC/C,YAAAA;QAAA;UACC,MAAK;UACL,OAAAU;UACA,OAAOM;UACP,UAAUa;UACV,YAAAlB;UACA,UAAAE;UACA,cAAckB;UACd,kBAAiB;UACjB,WAAW,CAACuB,WAAWP,mBAAAA,KAAAZ,IAAA,EAAS,GAAGmB,GAAAA,CAAO;UAC1C,YAAAvC;UAEC,UAAQP,GAAA,IAAI,CAAC+C,WACXR,mBAAAA,KAAAhD,GAAA,EAAwB,OAAOwD,GAAK,OAClC,UAAAA,GAAK,MADK,GAAAA,GAAK,KAElB,CACD;QAAA;MACH;IAAA;EAEJ,EAAA,CAAA;AAEJ;AAzKA,IA2KAC,IAAenD;", "names": ["useCombinedRefs", "refs", "useMemo", "node", "for<PERSON>ach", "ref", "canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "getWindow", "target", "ownerDocument", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "getOwnerDocument", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "useInterval", "intervalRef", "set", "listener", "duration", "setInterval", "clear", "clearInterval", "useLatestValue", "value", "dependencies", "valueRef", "useLazyMemo", "callback", "newValue", "useNodeRef", "onChange", "onChangeHandler", "setNodeRef", "usePrevious", "ids", "useUniqueId", "prefix", "id", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isKeyboardEvent", "KeyboardEvent", "isTouchEvent", "TouchEvent", "getEventCoordinates", "touches", "length", "clientX", "x", "clientY", "y", "changedTouches", "CSS", "freeze", "Translate", "transform", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "property", "easing", "SELECTOR", "findFirstFocusableNode", "matches", "querySelector", "hiddenStyles", "display", "HiddenText", "id", "value", "React", "style", "LiveRegion", "announcement", "ariaLiveType", "visuallyHidden", "position", "top", "left", "width", "height", "margin", "border", "padding", "overflow", "clip", "clipPath", "whiteSpace", "role", "useAnnouncement", "setAnnouncement", "useState", "announce", "useCallback", "DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "defaultCoordinates", "Object", "freeze", "x", "y", "getRelativeTransformOrigin", "event", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "x", "left", "width", "y", "top", "height", "sortCollisionsDesc", "data", "value", "a", "b", "getFirstCollision", "collisions", "property", "length", "firstCollision", "getIntersectionRatio", "entry", "target", "top", "Math", "max", "left", "right", "min", "width", "bottom", "height", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "Number", "toFixed", "rectIntersection", "collisionRect", "droppableRects", "droppableContainers", "collisions", "droppableContainer", "id", "rect", "get", "push", "data", "value", "sort", "sortCollisionsDesc", "adjustScale", "transform", "rect1", "rect2", "scaleX", "width", "scaleY", "height", "getRectDelta", "x", "left", "y", "top", "defaultCoordinates", "createRectAdjustmentFn", "modifier", "adjustClientRect", "rect", "adjustments", "reduce", "acc", "adjustment", "bottom", "right", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "transform<PERSON><PERSON>in", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "options", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "property", "value", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "length", "isDocument", "scrollingElement", "includes", "push", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "Math", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "add", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "Object", "defineProperty", "get", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "target", "listeners", "removeAll", "for<PERSON>ach", "listener", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "sqrt", "EventName", "preventDefault", "event", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "active", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "collisionRect", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "min", "max", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "getEventCoordinates", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "noop", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "pointerCoordinates", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "useMemo", "DraggableRect", "scrollContainerRef", "autoScroll", "useCallback", "sortedScrollableAncestors", "reverse", "useEffect", "index", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "id", "draggableNode", "cachedNode", "useCombineActivators", "sensors", "getSyntheticHandler", "accumulator", "sensor", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "useState", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "filter", "droppableRects", "previousValue", "container", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "type", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "entry", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "draggable", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "collisions", "containerNodeRect", "droppableContainers", "over", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "dispatch", "InternalContext", "createContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "Action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "delete", "RestoreFocus", "useContext", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "rectIntersection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "useDndMonitorProvider", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "data", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "useUniqueId", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "getFirstCollision", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "onDragStart", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "onDragMove", "onDragOver", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "React", "DndMonitorContext", "Provider", "restoreFocus", "Accessibility", "hiddenTextDescribedById", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "getRelativeTransformOrigin", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform", "arrayMove", "array", "from", "to", "newArray", "slice", "splice", "length", "getSortedRects", "items", "rects", "reduce", "accumulator", "id", "index", "rect", "get", "Array", "length", "isValidIndex", "itemsEqual", "a", "b", "i", "normalizeDisabled", "disabled", "draggable", "droppable", "defaultScale", "scaleX", "scaleY", "horizontalListSortingStrategy", "activeNodeRect", "fallbackActiveRect", "activeIndex", "overIndex", "itemGap", "getItemGap", "newIndexRect", "x", "left", "width", "y", "currentRect", "previousRect", "nextRect", "rectSortingStrategy", "newRects", "arrayMove", "oldRect", "newRect", "top", "height", "defaultScale", "scaleX", "scaleY", "verticalListSortingStrategy", "activeIndex", "activeNodeRect", "fallbackActiveRect", "index", "rects", "overIndex", "overIndexRect", "x", "y", "top", "height", "itemGap", "getItemGap", "clientRects", "currentRect", "previousRect", "nextRect", "ID_PREFIX", "Context", "React", "createContext", "containerId", "disableTransforms", "items", "useDragOverlay", "sortedRects", "strategy", "rectSortingStrategy", "disabled", "draggable", "droppable", "SortableContext", "children", "id", "userDefinedItems", "disabledProp", "active", "dragOverlay", "droppableRects", "over", "measureDroppableContainers", "useDndContext", "useUniqueId", "Boolean", "rect", "useMemo", "map", "item", "isDragging", "indexOf", "previousItemsRef", "useRef", "itemsHaveChanged", "itemsEqual", "current", "normalizeDisabled", "useIsomorphicLayoutEffect", "useEffect", "contextValue", "getSortedRects", "Provider", "value", "defaultNewIndexGetter", "arrayMove", "defaultAnimateLayoutChanges", "isSorting", "wasDragging", "newIndex", "previousItems", "previousContainerId", "transition", "defaultTransition", "duration", "easing", "transitionProperty", "disabledTransition", "CSS", "Transition", "toString", "property", "defaultAttributes", "roleDescription", "useDerivedTransform", "node", "derivedTransform", "setDerivedtransform", "useState", "previousIndex", "initial", "getClientRect", "ignoreTransform", "delta", "left", "width", "useSortable", "animateLayoutChanges", "attributes", "userDefinedAttributes", "localDisabled", "data", "customData", "getNewIndex", "localStrategy", "resizeObserverConfig", "globalDisabled", "globalStrategy", "useContext", "normalizeLocalDisabled", "sortable", "itemsAfterCurrentSortable", "slice", "isOver", "setNodeRef", "setDroppableNodeRef", "useDroppable", "updateMeasurementsFor", "activatorEvent", "setDraggableNodeRef", "listeners", "setActivatorNodeRef", "transform", "useDraggable", "useCombinedRefs", "displaceItem", "isValidIndex", "shouldDisplaceDragSource", "dragSourceDisplacement", "finalTransform", "activeId", "previous", "shouldAnimateLayoutChanges", "timeoutId", "setTimeout", "clearTimeout", "getTransition", "isKeyboardEvent", "undefined", "directions", "KeyboardCode", "Down", "Right", "Up", "Left", "_extends", "n", "e", "t", "r", "CloseOutlined", "_arrayWithHoles", "r", "_iterableToArrayLimit", "r", "l", "t", "e", "n", "i", "u", "a", "f", "o", "_arrayLikeToArray", "r", "a", "e", "n", "_unsupportedIterableToArray", "r", "a", "arrayLikeToArray", "t", "_nonIterableRest", "_slicedToArray", "r", "e", "arrayWithHoles", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest", "_typeof", "o", "toPrimitive", "t", "r", "_typeof", "e", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t", "i", "toPrimitive", "_typeof", "_defineProperty", "e", "r", "t", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "_objectWithoutProperties", "e", "t", "o", "r", "i", "objectWithoutPropertiesLoose", "n", "e", "t", "hasOwn", "classNames", "classes", "i", "arg", "argType", "inner", "key", "module", "blue", "IconContext", "createContext", "ownKeys", "e", "r", "t", "o", "_objectSpread2", "defineProperty", "round", "splitColorStr", "str", "parseNum", "match", "numList", "item", "i", "parseHSVorHSL", "num", "_", "index", "limitRange", "value", "max", "mergedMax", "FastColor", "input", "_defineProperty", "matchFormat", "matchPrefix", "prefix", "trimStr", "hsv", "<PERSON><PERSON><PERSON><PERSON>", "raw", "val", "R", "G", "B", "delta", "amount", "h", "s", "l", "color", "p", "calc", "key", "rgba", "background", "bg", "alpha", "other", "hex", "rHex", "gHex", "bHex", "aHex", "rgb", "clone", "withoutPrefix", "connectNum", "index1", "index2", "a", "r", "g", "b", "huePrime", "chroma", "secondComponent", "lightnessModification", "v", "vv", "hh", "ff", "q", "t", "cells", "txt", "hueStep", "saturationStep", "saturationStep2", "brightnessStep1", "brightnessStep2", "lightColorCount", "darkColorCount", "darkColorMap", "getHue", "hsv", "i", "light", "hue", "getSaturation", "saturation", "getValue", "value", "generate", "color", "opts", "patterns", "pColor", "FastColor", "c", "_i", "_c", "_ref", "index", "amount", "canUseDom", "contains", "root", "n", "node", "APPEND_ORDER", "APPEND_PRIORITY", "MARK_KEY", "containerCache", "getMark", "_ref", "mark", "getContainer", "option", "head", "getOrder", "prepend", "findStyles", "container", "node", "injectCSS", "css", "canUseDom", "csp", "_option$priority", "priority", "mergedOrder", "isPrependQueue", "styleNode", "<PERSON><PERSON><PERSON><PERSON>", "existStyle", "nodePriority", "findExistNode", "key", "syncRealContainer", "cachedRealContainer", "contains", "placeholder<PERSON><PERSON><PERSON>", "parentNode", "updateCSS", "originOption", "styles", "_objectSpread", "existNode", "_option$csp", "_option$csp2", "_option$csp3", "newNode", "getRoot", "ele", "_ele$getRootNode", "inShadow", "getShadowRoot", "warned", "preWarningFns", "preMessage", "fn", "warning", "valid", "message", "finalMessage", "msg", "preMessageFn", "note", "resetWarned", "call", "method", "warningOnce", "noteOnce", "camelCase", "input", "match", "g", "warning", "valid", "message", "warn", "isIconDefinition", "target", "_typeof", "normalizeAttrs", "attrs", "acc", "key", "val", "generate", "node", "rootProps", "React", "_objectSpread", "child", "index", "getSecondaryColor", "primaryColor", "generateColor", "normalizeTwoToneColors", "twoToneColor", "iconStyles", "useInsertStyles", "eleRef", "_useContext", "useContext", "IconContext", "csp", "prefixCls", "layer", "mergedStyleStr", "useEffect", "ele", "shadowRoot", "getShadowRoot", "updateCSS", "_excluded", "twoToneColorPalette", "setTwoToneColors", "_ref", "primaryColor", "secondaryColor", "getSecondaryColor", "getTwoToneColors", "_objectSpread", "IconBase", "props", "icon", "className", "onClick", "style", "restProps", "_objectWithoutProperties", "svgRef", "colors", "useInsertStyles", "warning", "isIconDefinition", "target", "generate", "setTwoToneColor", "twoToneColor", "_normalizeTwoToneColo", "normalizeTwoToneColors", "_normalizeTwoToneColo2", "_slicedToArray", "primaryColor", "secondaryColor", "ReactIcon", "getTwoToneColor", "colors", "_excluded", "setTwoToneColor", "blue", "Icon", "props", "ref", "className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor", "restProps", "_objectWithoutProperties", "_React$useContext", "Context", "_React$useContext$pre", "prefixCls", "rootClassName", "classString", "classNames", "_defineProperty", "iconTabIndex", "svgStyle", "_normalizeTwoToneColo", "normalizeTwoToneColors", "_normalizeTwoToneColo2", "_slicedToArray", "primaryColor", "secondaryColor", "_extends", "ReactIcon", "getTwoToneColor", "CloseOutlined", "props", "ref", "AntdIcon", "_extends", "CloseOutlinedSvg", "RefIcon", "Option", "Select", "sortingStrategies", "rectSortingStrategy", "verticalListSortingStrategy", "horizontalListSortingStrategy", "DraggableSelect", "defaultValue", "value", "options", "onChange", "style", "showSearch", "caseSensitiveSearch", "disabled", "sortingStrategy", "allowClear", "selectedItems", "setSelectedItems", "useState", "useEffect", "handleDragEnd", "useCallback", "event", "active", "over", "newSelectedItems", "oldIndex", "newIndex", "reorderedSelected", "handleSelectionChange", "newSelected", "handleFilterOption", "input", "option", "search", "TagItem", "label", "closable", "onClose", "attributes", "listeners", "setNodeRef", "transform", "transition", "isDragging", "useSortable", "tagStyle", "jsx", "e", "jsxs", "r", "CloseOutlined", "DndContext", "SortableContext", "props", "item", "DraggableSelect$1"]}