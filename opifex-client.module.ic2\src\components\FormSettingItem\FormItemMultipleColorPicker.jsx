import { useEffect, useState } from "react";
import { ColorPicker, Button, Space, Tooltip, Form } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import useDebounce from "../../hooks/useDebounce";

export const FormItemMultipleColorPicker = ({
  name,
  label = "",
  className,
}) => {
  return (
    <Form.Item name={name} label={label} className={className}>
      <ColorPickerItem />
    </Form.Item>
  );
};

const ColorPickerItem = ({ value, onChange }) => {
  let colorDefaul = typeof value === "string" ? value?.split(",") : value;
  const [colors, setColors] = useState(colorDefaul ?? []);

  const debouncedOnColorChange = useDebounce(() => {
    onChange(colors);
  });

  const handleColorChange = (color, index) => {
    const newColors = [...colors];
    newColors[index] = color.toHexString();
    setColors(newColors);
    debouncedOnColorChange();
  };

  const addColorPicker = () => {
    setColors([...colors, ""]);
    debouncedOnColorChange();
  };

  const removeColorPicker = (index) => {
    const newColors = colors.filter((_, i) => i !== index);
    setColors(newColors);
    debouncedOnColorChange();
  };

  const clearColorPicker = (index) => {
    const newColors = [...colors];
    newColors[index] = '';
    setColors(newColors);
    debouncedOnColorChange();
  };

  useEffect(() => {
    setColors(colorDefaul ?? []);
  }, [colorDefaul]);

  return (
    <div className="flex items-center gap-4 mb-4 flex-wrap">
      {colors?.map((color, index) => (
        <Space key={index}>
          <ColorPicker
            value={color}
            showText={(color) =>
              !color?.cleared ? color.toHexString().toUpperCase() : "Pick color"
            }
            allowClear
            onChange={(c) => handleColorChange(c, index)}
            onClear={() => clearColorPicker(index)}
          />
          {colors?.length > 1 && (
            <Button
              icon={<DeleteOutlined />}
              onClick={() => removeColorPicker(index)}
              danger
              shape="circle"
            />
          )}
        </Space>
      ))}
      <Tooltip title="Add Color Picker">
        <Button icon={<PlusOutlined />} onClick={addColorPicker} />
      </Tooltip>
    </div>
  );
};
