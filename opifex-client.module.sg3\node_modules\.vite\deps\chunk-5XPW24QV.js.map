{"version": 3, "sources": ["../../__mf__virtual/sg3_mf_2_project_mf_2_name__mf_v__runtimeInit__mf_v__.js"], "sourcesContent": ["\n    let initResolve, initReject\n    const initPromise = new Promise((re, rj) => {\n      initResolve = re\n      initReject = rj\n    })\n    module.exports = {\n      initPromise,\n      initResolve,\n      initReject\n    }\n    "], "mappings": ";;;;;AAAA;AAAA;AACI,QAAI;AAAJ,QAAiB;AACjB,QAAM,cAAc,IAAI,QAAQ,CAAC,IAAI,OAAO;AAC1C,oBAAc;AACd,mBAAa;AAAA,IACf,CAAC;AACD,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;", "names": []}