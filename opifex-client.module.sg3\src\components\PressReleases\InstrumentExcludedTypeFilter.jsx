import { useState, useEffect, useContext } from "react";
import { Button, Flex, Form, Table, Tooltip } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { useInputStyle } from "../../hooks/useInputStyle";
import { SettingsContext } from "../../context/SettingsContext";
import {
  generateUniqueKey,
  getMessageTypeOptions,
  parseSourceMessageTypeString,
} from "../../utils/common";
import CustomInputTableCell from "../common/CustomInputTableCell";
import { useMemo } from "react";
import LazyLoad from "../common/LazyLoading";
import SkeletonTable from "./SkeletionTable";

const InstrumentExcludedTypeFilter = ({
  onChange,
  form,
  sources,
  instrumentId,
}) => {
  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const [excludedTypeFilterDatas, setExcludedTypeFilterDatas] = useState([]);

  const sourceOptions = Object.values(sources)?.map((source) => ({
    label: source?.name,
    value: source?.id?.toString(),
  }));

  const editExcludedTypeFilterForm = Form.useWatch(
    ["InstrumentPressReleases", instrumentId, "EditExcludedTypeFilter"],
    { form, preserve: true }
  );

  /**
   * Effect to handle excluded type filter updates for instrument press releases.
   */
  useEffect(() => {
    const excludedTypeFilterArray = [];
    if (editExcludedTypeFilterForm) {
      Object.keys(editExcludedTypeFilterForm).forEach((key) => {
        const item = editExcludedTypeFilterForm[key];
        if (item?.Source && item?.MessageType) {
          // Clear invalid message types
          const source = sources[item.Source];
          if (
            source?.messageTypes &&
            !source.messageTypes.some(
              (mt) => mt.messageTypeId?.toString() === item.MessageType
            )
          ) {
            form.setFieldValue(
              [
                "InstrumentPressReleases",
                instrumentId,
                "EditExcludedTypeFilter",
                key,
                "MessageType",
              ],
              undefined
            );
            return;
          }
          excludedTypeFilterArray.push(`${item.Source}|${item.MessageType}`);
        }
      });
    }
    const excludedTypeFilterString = excludedTypeFilterArray.join(";");
    form.setFieldValue(
      ["InstrumentPressReleases", instrumentId, "ExcludedTypeFilter"],
      excludedTypeFilterString
    );
    onChange && onChange();
  }, [editExcludedTypeFilterForm, sources, form, onChange, instrumentId]);

  /**
   * Effect to initialize excluded type filter data for instrument press releases.
   */
  useEffect(() => {
    const initState = settings?.initState?.Instruments?.Instrument?.find(
      (inst) => inst.Id?.toString() === instrumentId?.toString()
    );

    if (
      !form.getFieldValue([
        "InstrumentPressReleases",
        instrumentId,
        "EditExcludedTypeFilterKeys",
      ])
    ) {
      const editExcludedTypeFilter = parseSourceMessageTypeString(
        initState?.PressRelease?.ExcludedTypeFilter || ""
      )
        ?.filter((item) => Object.keys(sources || {}).includes(item.Source))
        ?.reduce(
          (editExcludedTypeFilterObj, item) => ({
            ...editExcludedTypeFilterObj,
            [generateUniqueKey()]: item,
          }),
          {}
        );
      const excludedTypeFilterKeys = Object.keys(editExcludedTypeFilter || {});
      const initExcludedTypeFilterDatas = excludedTypeFilterKeys?.map(
        (itemKey) => ({
          key: itemKey,
          value: editExcludedTypeFilter[itemKey],
        })
      );

      form.setFieldValue(
        ["InstrumentPressReleases", instrumentId, "EditExcludedTypeFilter"],
        editExcludedTypeFilter
      );
      form.setFieldValue(
        ["InstrumentPressReleases", instrumentId, "EditExcludedTypeFilterKeys"],
        excludedTypeFilterKeys
      );
      setExcludedTypeFilterDatas(initExcludedTypeFilterDatas || []);
      onChange && onChange();
    } else {
      const excludedTypeFilterKeys = form.getFieldValue([
        "InstrumentPressReleases",
        instrumentId,
        "EditExcludedTypeFilterKeys",
      ]);
      const excludedTypeFilterDatas = excludedTypeFilterKeys?.map(
        (itemKey) => ({
          key: itemKey,
          value: editExcludedTypeFilterForm?.[itemKey],
        })
      );
      setExcludedTypeFilterDatas(excludedTypeFilterDatas || []);
    }
  }, [
    sources,
    settings,
    form,
    onChange,
    instrumentId,
    editExcludedTypeFilterForm,
  ]);

  const addExcludedTypeFilter = () => {
    const newKey = generateUniqueKey();
    const currentKeys =
      form.getFieldValue([
        "InstrumentPressReleases",
        instrumentId,
        "EditExcludedTypeFilterKeys",
      ]) || [];
    const newKeys = [...currentKeys, newKey];

    form.setFieldValue(
      ["InstrumentPressReleases", instrumentId, "EditExcludedTypeFilterKeys"],
      newKeys
    );
    setExcludedTypeFilterDatas((prev) => [...prev, { key: newKey, value: {} }]);
  };

  const removeExcludedTypeFilter = (keyToRemove) => {
    const currentKeys =
      form.getFieldValue([
        "InstrumentPressReleases",
        instrumentId,
        "EditExcludedTypeFilterKeys",
      ]) || [];
    const newKeys = currentKeys.filter((key) => key !== keyToRemove);

    form.setFieldValue(
      ["InstrumentPressReleases", instrumentId, "EditExcludedTypeFilterKeys"],
      newKeys
    );

    const currentEditData =
      form.getFieldValue([
        "InstrumentPressReleases",
        instrumentId,
        "EditExcludedTypeFilter",
      ]) || {};
    const newEditData = { ...currentEditData };
    delete newEditData[keyToRemove];
    form.setFieldValue(
      ["InstrumentPressReleases", instrumentId, "EditExcludedTypeFilter"],
      newEditData
    );

    setExcludedTypeFilterDatas((prev) =>
      prev.filter((item) => item.key !== keyToRemove)
    );
  };

  const excludedTypeFilterColumns = useMemo(() => [
    {
      title: "Source",
      align: "left",
      minWidth: 300,
      key: "Source",
      render: (_, record) => (
        <CustomInputTableCell
          type={"select"}
          options={sourceOptions}
          form={form}
          name={[
            "InstrumentPressReleases",
            instrumentId,
            "EditExcludedTypeFilter",
            `${record.key}`,
            "Source",
          ]}
          rules={[{ required: true, message: "Source is required" }]}
          defaultValue={record.value?.["Source"]}
          // isDirectEdit={true}
        />
      ),
    },
    {
      title: "Message Type",
      align: "left",
      minWidth: 300,
      key: "MessageType",
      render: (_, record) => (
        <CustomInputTableCell
          type={"select"}
          options={getMessageTypeOptions(sources, record.value?.["Source"])}
          form={form}
          name={[
            "InstrumentPressReleases",
            instrumentId,
            "EditExcludedTypeFilter",
            `${record.key}`,
            "MessageType",
          ]}
          defaultValue={record.value?.["MessageType"]}
          rules={[{ required: true, message: "Message Type is required" }]}
          // isDirectEdit={true}
        />
      ),
    },
    {
      title: "Action",
      align: "center",
      minWidth: 100,
      key: "Action",
      render: (_, record) => (
        <Tooltip title={`Delete excluded type filter ${record.name}`}> 
          <Button
            type="primary"
            danger
            onClick={() => removeExcludedTypeFilter(record.key)}
            icon={<DeleteOutlined />}
          />
        </Tooltip>
      ),
    }
  ], []);
  return (
    <LazyLoad threshold={0.8} fallback={<SkeletonTable rowCount={1} />}>
      <Flex justify="flex-start" align="center" className="mb-5">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={addExcludedTypeFilter}
        >
          Add Excluded Type Filter
        </Button>
      </Flex>
      <Table
        key={`excluded-type-filter-${instrumentId}`}
        columns={excludedTypeFilterColumns}
        dataSource={excludedTypeFilterDatas}
        size={isMobile ? "small" : "default"}
        bordered
        pagination={false}
        style={{ width: "100%", marginTop: 16, marginBottom: 24 }}
        scroll={{ x: "max-content", y: 400 }}
        tableLayout="auto"
        rowKey={`excluded-type-filter-${instrumentId}`}
      />
    </LazyLoad>
  );
};

export default InstrumentExcludedTypeFilter;
