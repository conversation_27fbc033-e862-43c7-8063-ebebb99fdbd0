#!/bin/sh
set -e

# Script to update version information using semantic-release
# Usage: ./update-versions-semantic.sh <version>
CLIENT_DIR="."

# If no version specified, try to get from Git tag, otherwise use a default
if [ -z "$1" ]; then
  # Check if we're on a Git tag
  if [ -n "$CI_COMMIT_TAG" ]; then
    VERSION=${CI_COMMIT_TAG#v}
    echo "Using version from Git tag: $VERSION"
  elif git describe --exact-match --tags HEAD >/dev/null 2>&1; then
    VERSION=$(git describe --exact-match --tags HEAD | sed 's/^v//')
    echo "Using version from Git tag: $VERSION"
  # Check if we're on a release branch
  elif [[ $(git symbolic-ref --short HEAD) =~ ^release/v ]]; then
    VERSION=$(git symbolic-ref --short HEAD | sed 's/^release\/v//')
    echo "Using version from release branch: $VERSION"
  else
    # Use timestamp-based version for dev builds
    VERSION="0.0.0-${CI_COMMIT_BRANCH:-mr}_$(date -u +%Y%m%d%H%M)"
    echo "Using development version: $VERSION"
  fi
else
  VERSION=$1
  echo "Using specified version: $VERSION"
fi

if [ -z "$VERSION" ]; then
  echo "Error: Version is required"
  echo "Usage: $0 <version>"
  exit 1
fi

BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
echo "Updating all projects to version: $VERSION"
echo "Build date: $BUILD_DATE"

# Update client (React) version
echo "Updating client version..."
if [ -f "$CLIENT_DIR/package.json" ]; then
  # Use jq if available, otherwise use sed
  if command -v jq >/dev/null 2>&1; then
    jq ".version = \"$VERSION\"" $CLIENT_DIR/package.json > $CLIENT_DIR/package.json.tmp
    mv $CLIENT_DIR/package.json.tmp $CLIENT_DIR/package.json
  else
    sed -i "s/\"version\": \"[^\"]*\"/\"version\": \"$VERSION\"/" $CLIENT_DIR/package.json
  fi

  # Create/update version.js file
  cat > $CLIENT_DIR/src/version.js << EOF
// Auto-generated file - DO NOT EDIT MANUALLY
// Generated by semantic-release
export const VERSION = '$VERSION';
export const BUILD_DATE = '$BUILD_DATE';
export const COMMIT_SHA = '${CI_COMMIT_SHA:-unknown}';
export const BRANCH = '${CI_COMMIT_REF_NAME:-unknown}';
EOF
  echo "✅ Updated client version to $VERSION"
else
  echo "❌ Client package.json not found"
fi

# Create a version.json in the root directory
cat > version.json << EOF
{
  "version": "$VERSION",
  "buildDate": "$BUILD_DATE",
  "branch": "${CI_COMMIT_REF_NAME:-unknown}",
  "commit": "${CI_COMMIT_SHA:-unknown}",
  "generatedBy": "semantic-release"
}
EOF

echo "✅ Created version.json in root directory"
echo "🎉 Version update completed: $VERSION ($BUILD_DATE)"
