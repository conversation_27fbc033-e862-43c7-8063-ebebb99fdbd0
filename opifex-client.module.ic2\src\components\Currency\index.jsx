import { Checkbox, Form, Table, Typography } from "antd";
import { useContext } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";
import CustomInputTableCell from "../common/CustomInputTableCell";
import CurrencySelect from "./CurrencySelect";
const { Title } = Typography;

export const Currency = ({ form }) => {
  const enableCurrency = Form.useWatch("CurrencyEnabled", form);

  const settings = useContext(SettingsContext);
  const { isMobile } = useInputStyle();
  const selectedCurrencies = Form.useWatch("currencyCodes", form) ?? [];

  const currenciesTextColumns = [
    {
      title: "Currencies Code",
      align: "left",
      minWidth: 200,
      fixed: !isMobile ? "left" : false,
      dataIndex: "key",
      key: "key",
      showSorterTooltip: {
        target: "full-header",
      },
      sorter: (a, b) => {
        if (a.key === b.key) {
          return 0;
        } else if (a.key > b.key) {
          return 1;
        } else {
          return -1;
        }
      },
      render: (key) => key,
    },
    ...(settings?.basicSettings?.availableLanguages
      ? settings.basicSettings.availableLanguages.map((lang) => ({
          title: lang,
          align: "left",
          minWidth: 300,
          dataIndex: "",
          key: "",
          render: (_, record) => (
            <CustomInputTableCell
              form={form}
              name={["CustomCurrencySign", record?.key, `${lang}`]}
              defaultValue={record?.key}
              disabled={!enableCurrency}
            />
          ),
        }))
      : []),
  ];

  return (
    <div>
      <Form.Item name={["CurrencyEnabled"]} valuePropName="checked">
        <Checkbox>Enable currencies</Checkbox>
      </Form.Item>
      {enableCurrency && (
        <Form.Item name={["currencyCodes"]}>
          <CurrencySelect form={form} />
        </Form.Item>
      )}
      <Title level={5}>Currency text</Title>
      <Form.Item noStyle>
        <Table
          size={isMobile ? "small" : "default"}
          dataSource={selectedCurrencies?.map((c) => ({ key: c }))}
          columns={currenciesTextColumns}
          bordered
          pagination={false}
          style={{ width: "100%", marginTop: 16, marginBottom: 24 }}
          scroll={{ x: "max-content", y: 500 }}
          tableLayout="auto"
          showSorterTooltip={{
            target: "sorter-icon",
          }}
        />
      </Form.Item>
    </div>
  );
};
