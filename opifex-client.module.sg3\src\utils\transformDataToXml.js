export function getSG3XmlSettings(basicSettings, state) {
  const xml = {
    ...state,
    TimeZone: basicSettings?.timezone,
    UseLatinNumber: basicSettings?.availableLanguages?.includes('ar-AE')
      ? basicSettings?.UseLatinNumber ?? false
      : true,
    Accessibilities: {
      ...state?.Accessibilities,
      TimeZone: basicSettings?.timezone,
    },
    Currencies: {
      Enabled: state?.Currencies?.Enabled ?? false,
      Currency: state?.Currencies?.Currencies?.map((c, i) => ({
        ...state?.Currencies?.EditCurrency?.[c],
        Code: c,
        Order: i + 1,
      })),
    },
    Instruments: {
      Instrument: basicSettings?.instrumentIds?.map((id, index) => {
        const instrument = ({
          Id: id,
          Order: index + 1,
          EnabledAdjustPrice: state?.EnabledAdjustPrice?.[id] ?? false,
          ...basicSettings.instrument[id],
          CustomTimezone: basicSettings.instrument[id]?.timezone,
          isRT: basicSettings.instrument[id]?.isRealTime ?? false,
        });

        // Add instrument-specific PressRelease config if custom config is enabled
        // Try both number and string keys since form might store as string
        const instrumentPressReleaseConfig = state?.InstrumentPressReleases?.[id] || state?.InstrumentPressReleases?.[id.toString()];
        console.log(`Debug instrument ${id}:`, {
          id,
          idAsString: id.toString(),
          instrumentPressReleaseConfig,
          allInstrumentPressReleases: state?.InstrumentPressReleases
        });

        if (instrumentPressReleaseConfig?.useCustomConfig) {
          instrument.PressRelease = {
            SourceFilter: instrumentPressReleaseConfig?.SourceFilter?.join(',') ?? '',
            TypeFilter: instrumentPressReleaseConfig?.TypeFilter ?? '',
            ExcludedTypeFilter: instrumentPressReleaseConfig?.ExcludedTypeFilter ?? '',
            LanguageForwarding: instrumentPressReleaseConfig?.LanguageForwarding ?? '',
          };
        }

        delete instrument.timezone;
        delete instrument.isRealTime;
        return instrument;
      }),
    },
    Peers: {
      Enabled: state?.Peers?.Enabled,
      Animation: state?.Peers?.Animation,
      Peer: state?.Peers?.Instruments?.map((instrument, index) => ({
        Id: instrument.id,
        Order: index + 1,
        ...state?.Peers?.EditInstruments?.[instrument.id],
        EnabledAdjustPrice: state?.Peers?.EditInstruments?.[instrument.id]?.EnabledAdjustPrice ?? false,
      })),
    },
    Indices: {
      Enabled: state?.Indices?.Enabled,
      Animation: state?.Indices?.Animation,
      Index: state?.Indices?.Instruments?.map((instrument, index) => ({
        Id: instrument.id,
        Order: index + 1,
        ...state?.Indices?.EditInstruments?.[instrument.id],
        EnabledAdjustPrice: state?.Indices?.EditInstruments?.[instrument.id]?.EnabledAdjustPrice ?? false,
      })),
    },
    CustomPhrases: state?.EditCustomPhrases,
    Format: basicSettings?.availableLanguages?.reduce((acc, lang) => {
      acc[lang] = {
        ...state?.Format?.[lang],
        ...basicSettings?.Format?.[lang],
      };
      return acc;
    }, {}),
    ShareDetails: {
      ...state?.ShareDetails,
      MarketCapDisplay: state?.ShareDetails?.MarketCapDisplay ?? '',
      NumberOfSharesDisplay: state?.ShareDetails?.NumberOfSharesDisplay ?? '',
      TurnOverDisplay: state?.ShareDetails?.TurnOverDisplay ?? '',
    },
    Chart: {
      ...state.Chart,
      BlinkingPricePointer: {
        ...state.Chart?.BlinkingPricePointer,
        Enabled: state.Chart?.BlinkingPricePointer?.Enabled ?? false
      }
    },
    PressReleases: {
      LanguageForwarding: state?.PressReleases?.LanguageForwarding ?? '',
      TypeFilter: state?.PressReleases?.TypeFilter ?? '',
      ExcludedTypeFilter: state?.PressReleases?.ExcludedTypeFilter ?? '',
      NewPageUrlPattern: state?.PressReleases?.NewPageUrlPattern ?? '',
      OpenAs: state?.PressReleases?.OpenAs ?? '',
      SourceFilter: state?.PressReleases?.SourceFilter?.join(',') ?? '',
    },
    ColorBlindMode: {
      Enabled: state?.ColorBlindMode?.Enabled ?? false,
      DefaultSelected: state?.ColorBlindMode?.DefaultSelected ?? false,
    }
  };

  delete xml['EditCustomPhrases'];
  delete xml['EnabledAdjustPrice'];

  return xml;
}
