import { createContext } from 'react';

const SettingsContext = createContext({
  basicSettings: {
    availableLanguages: [],
    dataRefreshRate: undefined,
    instrumentConfigurations: [],
    instrumentIds: [],
    instrumentLocalizations: null,
    timezone: '',
  },
  availableTools: {
    availableTools: [],
    companyCode: '',
    version: '',
  },
  optionsFonts:[],
  initState: {},
  currencies: [],
  toolName: ''
});

export { SettingsContext };
