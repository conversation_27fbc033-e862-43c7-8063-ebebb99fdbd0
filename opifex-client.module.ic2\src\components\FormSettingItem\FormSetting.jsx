import { <PERSON>, Checkbox, Col, Flex, Row } from "antd";

import { FORM_ITEM_TYPES } from "./type";
import { FormItemSelectInput } from "./FormItemSelectInput";
import { FormItemTextFormat } from "./FormItemTextFormat";
import { FormItemColorInput } from "./FormItemColorInput";
import { FormItemSizeInput } from "./FormItemSizeInput";
import { FormItemSelectFontFamily } from "./FormItemSelectFontFamily";
import { Fragment } from "react";
import { FormItemCheckBox } from "./FormItemCheckBox";
import { FormItemMultipleColorPicker } from "./FormItemMultipleColorPicker";

/**
 * @typedef {Object} ResponsiveColType
 * @property {number} [xs]
 * @property {number} [sm]
 * @property {number} [md]
 * @property {number} [lg]
 * @property {number} [xl]
 * @property {number} [xxl]
 */

/**
 * Default responsive column settings.
 * @type {ResponsiveColType}
 */
const DEFAULT_RESPONSIVE_COL = {
  xs: 24,
  md: 12,
  xl: 8,
  xxl: 6,
};

const formItemMapping = {
  [FORM_ITEM_TYPES.SELECT]: FormItemSelectInput,
  [FORM_ITEM_TYPES.TEXT_FORMAT]: FormItemTextFormat,
  [FORM_ITEM_TYPES.COLOR]: FormItemColorInput,
  [FORM_ITEM_TYPES.SIZE]: FormItemSizeInput,
  [FORM_ITEM_TYPES.SELECT_FONT_FAMILY]: FormItemSelectFontFamily,
  [FORM_ITEM_TYPES.CHECKBOX]: FormItemCheckBox,
  [FORM_ITEM_TYPES.MULTIPLECOLOR]: FormItemMultipleColorPicker,
};

const renderFormItem = (item) => {
  const { type, name, label, options, className } = item;
  const Component = formItemMapping[type];

  if (!Component) {
    return null;
  }

  return (
    <Component
      key={name}
      name={name}
      label={label}
      className={className}
      options={options}
    />
  );
};

/**
 * FormSetting component.
 * @param {Object} props - The component props.
 * @param {ResponsiveColType} [props.responsiveCol] - Responsive column settings.
 */
export default function FormSetting({ settings, responsiveCol = {} }) {
  return (
    <Row gutter={[15, 15]}>
      {settings.map((setting) => {
        const {
          title,
          content,
          className,
          style,
          vertical = true,
          responsive = {},
        } = setting;
        const responsiveColSetting = {
          ...DEFAULT_RESPONSIVE_COL,
          ...responsiveCol,
          ...responsive,
        };
        return (
          <Col key={title} {...responsiveColSetting}>
            <Card
              size="small"
              className={className}
              title={title}
              style={{
                width: "100%",
                height: "100%",
                ...style,
              }}
            >
              <Flex gap={15} vertical={vertical} wrap>
                {content.map((item, index) => {
                  const { title, children, className } = item;
                  return (
                    <Fragment key={index}>
                      {renderFormItem(item) || (
                        <Card size="small" title={title} className={className}>
                          {children.map((child) => renderFormItem(child))}
                        </Card>
                      )}
                    </Fragment>
                  );
                })}
              </Flex>
            </Card>
          </Col>
        );
      })}
    </Row>
  );
}
